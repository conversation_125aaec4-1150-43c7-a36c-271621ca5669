<div class="no-gap-col">
	<div class="et-guide-text">
		<?= my_lang('既存のメールを参考にしたい場合は選択してください。')?><br>
		<?= my_lang('本文に掲載するショートフォームの[カウンターURL]はカウンター機能付きとなります。')?><br>
		<?= my_lang('送信元のメールアドレスには返信することができません。');?>
	</div>
    <div class="">
	    <div class="et-input-title et-guide-text noted_input mb-3">
		    <?= my_lang('既存のメールを参考にする')?>
	    </div>
	    <div class="select-box">
		    <select class="et-select-btn" name="template_id"
		            data-delete-link="<?php echo site_url("/manager/email_template/delete_template/"); ?>"
		            data-link="<?php echo site_url("/manager/email_template/get_template/"); ?>">
			    <option value="0">
				    <?= my_lang('選択してください')?>
			    </option>
			    <?php
			    if (is_array($mail_templates) && count($mail_templates)) {
				    foreach ($mail_templates as $mail_template) {
					    printf(
						    '<option value="%d">%s</option>',
						    $mail_template->id,
						    $mail_template->mail_name
					    );
				    }
			    } ?>
		    </select>
	    </div>
    </div>
</div>
<!--- row 2 --->
<div class="no-gap-col ">
    <div class="">
        <div class="et-input-title et-guide-text noted_input mb-3">
            <?= my_lang('メールのタイトル名')?>
            <span class="text-FD6600 required-text"><?= my_lang('必須')?></span>
        </div>
    </div>
    <div class="form-group">
        <input name="mail_name" class="et-input" type="text" placeholder="<?= my_lang('メールのタイトル名をご記入ください。')?>">
    </div>
</div>
<!--- row 4 --->
<div class="no-gap-col e_mail_content">
    <div class="mb-4">
        <div class="et-input-title et-guide-text mb-3">
            <?= my_lang('本文')?>
        </div>
        <div class="e_mail_template  e_mail_template_input">
                    <textarea name="mail_content"
                              placeholder="<?= my_lang('[予約者会社名]
[予約者氏名]様

本文をご記入ください。

受信者が本メールを開封した日時と下記予約フォームへのアクセス日時を取得可能です。
[カウンターURL]

配信停止を希望の方は、お手数ですが、下記から削除をしていただけます。
[メール配信停止URL]')?>"></textarea>
            <div id="e_count_url"></div>
        </div>
        <div class="e_mail_template">
            <p><?= my_lang('下記のショートコード[ ]を含む部分をタイトルや本文に挿入することでデーターが反映されます。')?></p>
            <p><?= my_lang('但し、申し込みフォームにて取得された情報に限ります。')?></p>
            <?= my_lang('[予約者会社名]')?><br>
            <?= my_lang('[予約者部署名]')?><br>
            <?= my_lang('[予約者学校名]')?><br>
            <?= my_lang('[予約者クラス名]')?><br>
            <?= my_lang('[予約者氏名]様')?><br>
            <div class="e_open_count_link_modal"
                 data-url="<?php echo site_url('manager/email_template/set_count_link_modal'); ?>">
                <?= my_lang('[カウンターURL]')?>
            </div>
            <br>
            <?= my_lang('[メール配信停止URL]')?><br>
        </div>
    </div>
</div>
<div class="line-x"></div>
<div class="button-container et-button-container box-item-2 d-flex justify-content-center gap-3 align-items-center">
    <button type="button" class="btn-general btn-yellow w-xl-maxcontent minw-120-200 e_preview_button">
        <?= my_lang('プレビュー')?>
    </button>
    <a class="delete-button"
       data-msg="<?= my_lang('削除いたしますか')?>?"
       data-button="<?= my_lang('実行')?>"
       data-button-class="e_ajax_link"
       data-button-url="#">
        <button type="button" class="btn-general btn-black w-xl-maxcontent ms-0 minw-120-200">
            <?= my_lang('削除')?>
        </button>
    </a>
</div>