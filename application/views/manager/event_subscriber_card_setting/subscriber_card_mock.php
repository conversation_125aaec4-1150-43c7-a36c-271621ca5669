<?php
    function convertHttpToHttps($url) {
        if (strpos($url, 'http://') === 0) {
            $url = 'https://' . substr($url, 7);
        }
        return $url;
    }
    function convertToBase64($url) {
        $url = convertHttpToHttps($url);
        $imageData = file_get_contents($url);
        $base64 = base64_encode($imageData);
        $mimeType = 'image/'.pathinfo($url, PATHINFO_EXTENSION);
        return "data:$mimeType;base64,$base64";
    }
?>
<style type="text/css">
	* {
		box-sizing:border-box;
    	-moz-box-sizing:border-box; /* Firefox */
    	-webkit-box-sizing:border-box;
	} 

	table {
		border-spacing: 0;
		border-collapse: collapse;
	}
	.subscriber_info_card * {
		margin: 0;
		padding: 0;
		/*font-family: ヒラギノ角ゴ Pro W3, <PERSON><PERSON><PERSON>, Osaka, メイリオ, Meiryo, ＭＳ Ｐゴシック, MS PGothic, sans-serif;*/
		font-family: Arial !important;

	}

	.subscriber_info_card {
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 24px;
	}

	.subscriber_info_card table {
		width: 100%;
		margin-bottom: 8px;
	}
    @media (min-width: 1024px) {
        .subscriber_info_card table {
            margin-bottom: 16px;
        }
    }
    .subscriber_info_card .table-box {
        margin: 16px 0;
    }

	.subscriber_info_card table td {
		vertical-align: middle;
	}

	.subscriber_info_card .main_info {
		border: 1px dashed #FDBC00;
		text-align: center;
		word-break: break-all;
		overflow: hidden;
	}
    .subscriber_info_card .box-area p {
        font-weight: bold;
    }
    .subscriber_info_card .box-area {
        padding: 17px 19px;
    }
    @media (min-width: 1024px) {
        .subscriber_info_card .box-area {
            padding: 21px 19px;
        }
    }

    .subscriber_info_card .main_info img {
		max-height: 14mm;
	}

	.subscriber_info_card .main_info p {
		overflow-y: hidden;
		white-space: pre-wrap;
	}
    @media (min-width: 1024px) {
        .subscriber_info_card .image_container {
            justify-content: center;
            width: 100%;
            position: relative;
        }
    }

	.subscriber_info_card .image_container .image_qrcode {
        margin: 8px;
	}
    @media (min-width: 1024px) {
        .subscriber_info_card .image_container .image_qrcode {
            position: absolute;
            left: 28px;
            margin: 0;
            bottom: 16px;
        }
    }

	.subscriber_info_card .image_container .image_subscriber {
	    width: 96px;
        height: 144px;
        border: 1px solid #707070;
        text-align: center;
	}

	.subscriber_info_card .sub_info {
		width: 100%;
		border: 1px dashed #FDBC00;
		margin-bottom: 8px;
		text-align: center;
		overflow: hidden;
		word-break: break-all;
	}

	.subscriber_info_card .sub_info p {
		overflow-y: hidden;
		white-space: pre-wrap;
	}

	.subscriber_info_card .sub_info.area_6 p{
		text-align: left;
		max-height: 19mm;
	}

	.subscriber_info_card .area_1 img{
		/*height: 100%;*/
	}

	.subscriber_info_card .image_qrcode img{
		max-height: 100%;
        width: 64px;
        height: 64px;
	}
    @media (min-width: 1024px) {
        .subscriber_info_card .image_qrcode img {
            width: 80px;
            height: 80px;
        }
    }

	.subscriber_info_card .image_subscriber img{
		max-width: 30mm;
		max-height: 40mm;
	}
</style>



<div id="subscriber_info_card" class="subscriber_info_card">
	<table class="main_info">
		<tr>
			<td>
                <div class="area_1 box-area">
                    <?php
                    echo isset($title_image_file_url)
                        ? "<img src='" . convertToBase64($title_image_file_url) . "'/>"
                        : "<img style='opacity:0' src=''/>";
                    ?>
                </div>

            </td>
		</tr>
	</table>
	<table class="main_info mb-0">
		<tr>
			<td class="area_2 box-area">
				<p class="text-14-16 fw-bold"><?php echo isset($area_2) ? $area_2 : my_lang('自由記入項目１') ?></p>
			</td>
		</tr>
	</table>

	<div class="image_container">
		<div class="image_qrcode d-flex align-items-end" style="<?php echo isset($show_qrcode) && $show_qrcode == '0' ? 'opacity:0;' : ''?>">
			<img src="<?php echo isset($qrcode_url) ? $qrcode_url : '../../../assets/images/default_qrcode.png'?>" />
		</div>
		<div>
			<table class="table-box">
				<tr>
					<td class="image_subscriber">
                            <?php
                            echo isset($image_subscriber_url)
                                ? "<img src='" . convertToBase64($image_subscriber_url) . "'/>"
                                : "<img style='opacity:0' src=''/>";
                            ?>
                    </td>
				</tr>
			</table>
		</div>
	</div>

	<table class="sub_info">
		<tr >
			<td class="area_3 box-area">
				<p class="text-14-16 fw-bold"><?php echo isset($area_3) ? $area_3["field_name"] : my_lang('選択項目１') ?></p>
			</td>
		</tr>
	</table>
	<table class="sub_info">
		<tr>
			<td class="area_4 box-area">
				<p class="text-14-16 fw-bold"><?php echo isset($area_4) ? $area_4["field_name"] : my_lang('選択項目２') ?></p>
			</td>
		</tr>
	</table>
	<table class="sub_info" style="display: none">
		<tr>
			<td class="area_5 box-area">
				<p class="text-14-16 fw-bold"><?php echo isset($area_5) ? $area_5["field_name"] : "Info 5" ?></p>
			</td>
		</tr>
	</table>
	<table class="sub_info">
		<tr>
			<td class="area_6 box-area">
				<p class="text-14-16 fw-bold text-center"><?php echo isset($area_6) ? $area_6 : my_lang('自由記入項目２') ?></p>
			</td>
		</tr>
	</table>
</div>
<?php if(!isset($is_api)):?>
	<script src="<?php echo base_url("assets/plugins/fittext.js"); ?>"></script>
<!--	<script type="text/javascript">-->
<!--		setTimeout(function(){-->
<!--			window.fitText(document.querySelectorAll(".main_info"), 1, { minFontSize: 16, maxFontSize: 32 });-->
<!--			window.fitText(document.querySelectorAll(".sub_info"), 1, { minFontSize: 8, maxFontSize: 24 });-->
<!--		}, 500);-->
<!--	</script>-->
<?php endif;?>