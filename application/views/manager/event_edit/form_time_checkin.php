<div class="index-top time-checkin mb-5">
    <?php echo $step_nav ?>
    <form id="i_event_edit_form" class="e_ajax_submit" method="post" action="<?php echo $save_link ?>" form-type="edit">
        <div class="event-detail-container event-edit-container event-edit-time-container">
            <div class="event-detail-title event-detail-title-2">
                <div class="event-detail-info">
                    <div class="d-flex align-items-start block-status-title">
                        <div class="status-sms"><?php echo $event->status; ?></div>
                        <div class="event-title"><?php echo $event->name; ?></div>
                    </div>
                    <div class="subscribe-title">
                        <div class="title_date">
                            <span><?php echo my_lang('予約開始日時') ?>:</span> <?php echo (isset($event->start_subscribe) && (strtotime($event->start_subscribe) > 0)) ? format_date_hour_en_jp($event->start_subscribe)  : '<i>' . my_lang('未設定') . '</i>'; ?>
                        </div>
                        <div class="title_date">
                            <span><?php echo my_lang('予約終了日時') ?>:</span> <?php echo (isset($event->end_subscribe) && (strtotime($event->start_subscribe) > 0)) ? format_date_hour_en_jp($event->end_subscribe) : '<i>' . my_lang('未設定') . '</i>'; ?>
                        </div>
                        <?php if (isset($event->type_ticket_id) && ($event->type_ticket_id == 2)) { ?>
                            <div class="title_date">
                                <span><?= my_lang('本申し込み終了日時') ?>：</span><?php echo (isset($event->end_payment) && (strtotime($event->start_subscribe) > 0)) ? format_date_hour_en_jp($event->end_payment) : '<i>  <u>' . my_lang('未設定') . '</u></i>'; ?>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <div class="w-640 mx-auto">
                <div class="event-edit-time-content">
                    <div class="event-edit-time-title fw-bold d-flex align-items-center work-break-all gap-4 gap-lg-5">
                        <?= my_lang('受付アプリ起動日時設定')?>
                    </div>
                    <div class="form_holder event_wrap">
                        <!-- row -->
                        <div class="input_holder bottom-border-none">
                            <div class="no-gap-col">
                                <div class="input_title text-14-16 fw-bold mb-3"><?= my_lang('受付アプリ起動日時')?>
                                </div>
                            </div>
                            <div class="content_base-form ">
                                <div class="form-group input_field_holder d-flex flex-column flex-xl-row gap-3 gap-lg-4">
                                    <div class="d-block near_icon position-relative mb-3 mb-lg-0 flex-lg-grow-1" >
                                        <input type="text" name="start_checkin[]" class="date_picker e_change_select_time item-input"
                                               placeholder="<?= my_lang('0000年00月00日')?>"
                                               data-img="<?php echo base_url() . "/assets/manager/images/calender-ic.svg"; ?>"
                                               value="<?php echo isset($event->start_checkin_date) ? $event->start_checkin_date : ''; ?>"
                                        >
                                    </div>
                                    <div class="d-flex align-items-center gap-3 gap-lg-4">
                                        <div class="d-flex align-items-center gap-3 no-gap-row">
                                            <select name="start_checkin[]"
                                                    class="select_button hour_select_btn e_change_select_time select_custom-onlygary">
                                                <option value="" selected disabled hidden><?= my_lang('選択')?></option>
                                                <?php foreach ($list_hours as $hour) { ?>
                                                    <option <?php echo (isset($event->start_checkin_hour) && $event->start_checkin_hour == $hour) ? 'selected' : '' ?>
                                                            value="<?php echo $hour; ?>"><?php echo $hour; ?></option>
                                                <?php } ?>
                                            </select>
                                            <label class="no_bold_label text-14-16 mb-0"><?= my_lang('時')?></label>
                                        </div>
                                        <div class="d-flex align-items-center gap-3 no-gap-row">
                                            <select name="start_checkin[]"
                                                    class="select_button minute_select_btn e_change_select_time select_custom-onlygary">
                                                <option value="" selected disabled hidden><?= my_lang('選択')?></option>
                                                <?php foreach ($list_minutes as $minute) { ?>
                                                    <option <?php echo (isset($event->start_checkin_minute) && $event->start_checkin_minute == $minute) ? 'selected' : '' ?>
                                                            value="<?php echo $minute; ?>"><?php echo $minute; ?></option>
                                                <?php } ?>
                                            </select>
                                            <label class="no_bold_label text-14-16 mb-0"><?= my_lang('分')?></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- row -->
                        <div class="input_holder">
                            <div class="no-gap-col">
                                <div class="input_title text-14-16 fw-bold mb-3"><?= my_lang('受付アプリ終了日時')?>
                                </div>
                            </div>
                            <div class="content_base-form">
                                <div class="form-group input_field_holder d-flex flex-column flex-xl-row gap-3 gap-lg-4">
                                    <div class="d-block near_icon position-relative mb-3 mb-lg-0 flex-lg-grow-1">
                                        <input type="text" name="end_checkin[]" class="date_picker e_change_select_time item-input "
                                               placeholder="<?= my_lang('0000年00月00日')?>"
                                               data-img="<?php echo base_url() . "/assets/manager/images/calender-ic.svg"; ?>"
                                               value="<?php echo isset($event->end_checkin_date) ? $event->end_checkin_date : ''; ?>"
                                        >
                                    </div>
                                    <div class="d-flex align-items-center gap-3 gap-lg-4">
                                        <div class="d-flex align-items-center gap-3 no-gap-row">
                                            <select name="end_checkin[]" class="select_button hour_select_btn e_change_select_time select_custom-onlygary">
                                                <option value="" selected disabled hidden><?= my_lang('選択')?></option>
                                                <?php foreach ($list_hours as $hour) { ?>
                                                    <option <?php echo (isset($event->end_checkin_hour) && $event->end_checkin_hour == $hour) ? 'selected' : '' ?>
                                                            value="<?php echo $hour; ?>"><?php echo $hour; ?></option>
                                                <?php } ?>
                                            </select>
                                            <label class="no_bold_label text-14-16 mb-0"><?= my_lang('時')?></label>
                                        </div>
                                        <div class="d-flex align-items-center gap-3 no-gap-row">
                                            <select name="end_checkin[]"
                                                    class="select_button minute_select_btn e_change_select_time select_custom-onlygary">
                                                <option value="" selected disabled hidden><?= my_lang('選択')?></option>
                                                <?php foreach ($list_minutes as $minute) { ?>
                                                    <option <?php echo (isset($event->end_checkin_minute) && $event->end_checkin_minute == $minute) ? 'selected' : '' ?>
                                                            value="<?php echo $minute; ?>"><?php echo $minute; ?></option>
                                                <?php } ?>
                                            </select>
                                            <label class="no_bold_label text-14-16 mb-0"><?= my_lang('分')?></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="box-settings" style="margin-top: 20px">
                            <div class="mb-4 mb-lg-5">
                                <h4 class="event-edit-time-title fw-bold d-flex align-items-center work-break-all gap-4 gap-lg-5"><?= my_lang('スキャナー番号設定')?></h4>
                                <div class="text-14-16 mb-4"><?= my_lang('任意のスキャナー番号４桁をアプリに付与する事で受付場所を特定することが可能です。')?></div>
                                <ul class="list_work-box text-14-16">
                                    <li style="list-style: none"><div class="d-flex align-items-center mb-0 gap-2"><span style="font-size: 12px">●</span><?= my_lang('運用方法')?>：</div></li>
                                    <li>１. <?= my_lang('管理者が任意の数字４桁（半角数字）を受付者に付与。')?></li>
                                    <li>２. <?= my_lang('受付者が付与された番号を使ってアプリにログイン。')?></li>
                                    <li>３. <?= my_lang('電子チケットをスキャン。')?></li>
                                    <li>４. <?= my_lang('申込者一覧画面にてスキャナー番号が掲載。')?></li>
                                </ul>
                                <div class="form-group clearfix d-flex flex-column gap-4 mb-0">
                                    <div class="radio m-0">
                                        <input type="radio" class="" name="is_scan_number" id="is_scan_number_0" value="0" <?php echo ($event && $event->is_scan_number == 0) ? 'checked' : '' ?>>
                                        <label for="is_scan_number_0" class="radio-label d-flex align-items-center gap-2 mb-0 p-0 text-14-18 fw-bold"><?= my_lang('スキャナー番号を使用しない。')?></label>
                                    </div>
                                    <div class="radio">
                                        <input type="radio" class="" name="is_scan_number" id="is_scan_number_1" value="1" <?php echo ($event && $event->is_scan_number == 1) ? 'checked' : '' ?>>
                                        <label for="is_scan_number_1" class="radio-label d-flex align-items-center gap-2 mb-0 p-0 text-14-18 fw-bold"><?= my_lang('スキャナー番号を使用する。')?></label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 mb-lg-5 pb-4 pb-lg-5 border-bottom border-color-ccc">
                                <h4 class="event-edit-time-title fw-bold d-flex align-items-center work-break-all gap-4 gap-lg-5"><?= my_lang('再入場の有無')?></h4>
                                <div class="text-14-16 mb-4"><?= my_lang('同一チケットでの受付記録の回数を設定して下さい。')?></div>
                                <div class="form-group clearfix d-flex flex-column gap-4 mb-0">
                                    <div class="radio m-0">
                                        <input type="radio" class="" name="type_checkin" id="type_checkin_2" value="2" <?php echo ($event && $event->type_checkin == 2) ? 'checked' : '' ?>>
                                        <label for="type_checkin_2" class="radio-label d-flex align-items-center gap-2 mb-0 p-0 text-14-16 fw-bold"><?= my_lang('再入場も毎回記録する。')?></label>
                                    </div>
                                    <div class="radio">
                                        <input type="radio" class="" name="type_checkin" id="type_checkin_1" value="1" <?php echo ($event && $event->type_checkin == 1) ? 'checked' : '' ?>>
                                        <label for="type_checkin_1" class="radio-label d-flex align-items-center gap-2 mb-0 p-0 text-14-16 fw-bold"><?= my_lang('一回の利用のみ有効とする。')?></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="button-container detail-button-container box-item-2 d-flex justify-content-center mb-4">
                    <?php
                    $text_button = my_lang('変更');
                    if ((strtotime($event->start_checkin) <= 0)
                        || (strtotime($event->end_checkin) <= 0)
                        || (strtotime($event->start_checkin) >= time())
                    ) {
                        $text_button = my_lang('決定する');
                    }
                    if (strtotime($event->start_subscribe) >= time()) { ?>
                        <button type="submit" class="detail-accept-button btn-general btn-yellow w-maxcontent fw-bold ms-0 d-flex align-items-center justify-content-center min-w-104-200">
                            <?php echo $text_button ?>
                        </button>
                    <?php } else {
                        ?>
                        <button type="button" class="e_ajax_confirm detail-accept-button btn-general btn-yellow w-maxcontent fw-bold d-flex align-items-center justify-content-center ms-0 min-w-104-200"
                                data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに')?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は')?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。')?>"
                                data-button-class="; submit_time_checkin_form"
                                data-button="<?= my_lang('閉じる;実行')?>"
                                data-button-url="#;<?php echo isset($save_link) ? $save_link : "#" ?>">
                            <?php echo $text_button ?>
                        </button>
                    <?php } ?>
                    <?php if (isset ($step_setting) && $step_setting == 'create') { ?>
                        <a href="<?php echo site_url('/manager/event_detail/index_top/' . (!empty($event->id) ? $event->id : 0)) ?>">
                            <button type="button" class=" detail-accept-button btn-general btn-yellow w-maxcontent fw-bold d-flex align-items-center justify-content-center ms-0 min-w-104-200">
                                <?= my_lang('スキップ')?>
                            </button>
                        </a>
                    <?php } ?>
                </div>
                <div class="event-edit-time-content mb-5">
                <span class="text-FD6600 text-14-16">＊<?= my_lang('設定した起動日時内でのみアプリが本イベントを認識いたします。')?><br>
                <?= my_lang('未設定の場合、又は起動時間外は、受付アプリで本イベントが認識されません。')?></span>
                </div>
            </div>
        </div>
    </form>
    <div class="anchor_link-box m-0">
        <a class="fw-bold d-flex align-items-center justify-content-center gap-4 text-decoration-none text-dark btnScrollTop">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="20" viewBox="0 0 14 20">
                <path id="north_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M206-860v-16.175l-4.6,4.575L200-873l7-7,7,7-1.4,1.425-4.6-4.6V-860Z" transform="translate(-200 880)"/>
            </svg>
            <?= my_lang('ページトップ') ?>
        </a>
    </div>
</div>