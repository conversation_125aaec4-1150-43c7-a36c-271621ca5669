<div class="wrap_custom_field form_holder-parentnew">
    <input id="i_form_id" type="hidden" value="<?php echo isset($form_id) ? $form_id : 0; ?>"
           name="form_id"/>
    <div class="row row_custom_field input_row">
        <div class="input_holder d-flex flex-column">
            <div class="item-box d-flex align-items-end gap-2 w-100">
                <div class="input_title-new fw-bold"><?= my_lang('アンケートのタイトル名')?>
                </div>
                <span class="fw-bold d-block"><?= my_lang('必須')?></span>
            </div>
            <div class="item-box">
                <div class="item-box">
                    <div class="form-group input_field_holder">
                        <input type="text" placeholder="<?= my_lang('ご自由にご記入ください。')?>" name="form_title" class="input_field"
                               value="<?php echo isset($event_form->form_title) ? $event_form->form_title : ''; ?>">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row row_custom_field input_row">
        <div class="input_holder d-flex flex-column">
            <div class="item-box d-flex align-items-end gap-2 w-100">
                <div class="input_title-new fw-bold"><?= my_lang('本文')?></div>
            </div>
            <div class="item-box">
                <div class="item-box">
                    <div class="form-group input_field_holder">
                        <textarea rows="1" class="choice_item_content" name="form_description"
                          placeholder="<?= my_lang('ご自由にご記入ください。')?>"><?php echo isset($event_form->form_description) ? $event_form->form_description : ''; ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row row_custom_field input_row footer_field">
        <div class="input_holder d-flex flex-column">
            <div class="item-box d-flex align-items-end gap-2 w-100">
                <div class="input_title-new fw-bold"><?= my_lang('フッター')?></div>
            </div>
            <div class="item-box">
                <div class="item-box">
                    <div class="form-group input_field_holder">
                        <textarea rows="1" class="choice_item_content" name="form_footer"
                                  placeholder="<?= my_lang('ご自由にご記入ください。')?>"><?php echo isset($event_form->form_footer) ? $event_form->form_footer : ''; ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="setting_answer_container survey_question_custom">
    <div class="text-description-answer">
        <?= my_lang('下記にてご質問が最大20個まで、ご回答が１質門あたり最大20個まで設定できます。')?><br/>
        <?= my_lang('自由入力回答の場合はA20をご選択ください。設定した回答数にかかわらず最後の回答に挿入されます。')?>
    </div>
</div>
<div id="i_field_container">
    <?php echo isset($custom_field) ? $custom_field : ""; ?>
</div>
<?php if ($this->session->userdata('role_id') == 1 || $this->session->userdata('role_id') == 2  ||  $this->session->userdata('role_id') == 4): ?>
<div class="button-container detail-button-container box-item d-flex">
    <button type="button" class="btn-general btn-black e_ajax_confirm big-button btn-question-delete text-14-16" data-msg="<?= my_lang('クリアしますか')?>？</br><?= my_lang('この質問の回答も結果画面から削除されます。')?>" data-button-class="btn-fee-deleted;"
    data-button="<?= my_lang('はい;いいえ')?>"
    data-button-url="<?php echo site_url("manager/event_edit/question_delete/" . $field_id); ?>;#">
                    <?= my_lang('クリア')?>
    </button>
    <button type="submit" class="btn-general btn-yellow big-button detail-accept-button text-14-16">
        <?= my_lang('確認画面')?>
    </button>
</div>
<?php endif ?>
