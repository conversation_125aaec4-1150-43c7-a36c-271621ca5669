<?php echo $data_event["user"]->company_name . "\r\n"; ?>
<?php echo $data_event["user"]->first_name . $data_event["user"]->last_name . "\r\n"; ?>
<?php echo "\r\n"; ?>
<?php echo  my_lang('この度はフリッパをご愛顧いただき誠にありがとうございます。') ?>​
<?php echo my_lang('下記の通りお支払いが完了いたしました。') ?>
<?php echo my_lang('本メールをもちまして領収書とさせていただきます') ?>
<?php echo "\r\n"; ?>
<?php echo "\r\n"; ?>
<?php echo my_lang('領収書') ?>​
<?php echo "\r\n"; ?>
<?php echo my_lang('決済日')?>：​<?php echo date("Y-m-d H:i:s") . "\r\n"; ?>
<?php echo my_lang('決済方法：クレジットカード決済') ?>​
<?php echo "\r\n"; ?>
<?php 
foreach($data_event["pay_items"] as $item){
    echo my_lang('項目') . "：" . $item["name"] . "\r\n";
    echo my_lang('小計') . "：" . number_format($item["fee"]) . my_lang('円') . "\r\n";
    echo my_lang('消費税') . "10％：" . number_format($item["tax"]) . my_lang('円') . "\r\n";
    echo my_lang('合計金額') . "：" . number_format($item["total"]) . my_lang('円（税込）') . "\r\n";
    echo "\r\n";
}
?>
<?php echo "\r\n"; ?>
<?php echo my_lang('上記金額を領収いたしました。')?>
<?php echo "\r\n"; ?>
<?php echo my_lang('フリッパ運営会社株式会社ディグラ') ?> 　​
<?php echo my_lang('登録番号：T7010401059495') ?>​
<?php echo my_lang('〒107-0052 東京都港区赤坂8-5-6イピアス青山104') ?>​
<?php echo "\r\n"; ?>
​<?php echo "\r\n"; ?>
<?php echo my_lang('【Flippa|フリッパ】誰もが自由に電子チケットを発券できるイベント管理システム') . "​ https://www.flippa.jp" ?>