<html>
<head>
    <title><?php echo isset($title) ? $title : "お申し込みフォーム" ?></title>
    <!-- text fonts -->

    <link href='//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&subset=vietnamese' rel='stylesheet'
          type='text/css'>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">

    <script>
        const translate = <?php echo json_encode($this->lang->language); ?>;
    </script>
    <!-- page specific plugin styles -->
    <link rel="stylesheet"
          href="<?php echo base_url("assets/plugins-bower/jquery-ui/themes/base/jquery-ui.min.css"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/plugins-bower/jGrowl/jquery.jgrowl.css"); ?>"/>
    <!-- bootstrap & fontawesome -->
    <link rel="stylesheet" href="<?php echo base_url("assets/plugins-bower/bootstrap/dist/css/bootstrap.min.css"); ?>"/>
    <link rel="stylesheet"
          href="<?php echo base_url("assets/plugins-bower/font-awesome/css/font-awesome.min.css"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/plugins-bower/select2/dist/css/select2.min.css"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/front/style.css"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/manager/css/table.css"); ?>"/>
    <link rel="icon" type="image/png" href="<?php echo base_url("assets/manager/images/flippa.ico"); ?>"/>

    <script src="<?php echo base_url("assets/plugins-bower/jquery/dist/jquery.min.js"); ?>"></script>
    <script type="text/javascript">
        window.jQuery || document.write("<script src='<?php echo base_url('assets/plugins-bower/jquery/dist/jquery.min.js') ?>'>" + "<" + "/script>");
    </script>

    <script src="<?php echo base_url("assets/plugins-bower/bootstrap/dist/js/bootstrap.min.js"); ?>"></script>
    <script src="<?php echo base_url("assets/plugins-bower/jquery-ui/jquery-ui.js"); ?>"></script>
    <script src="<?php echo base_url("assets/plugins-bower/jquery-form/jquery.form.js"); ?>"></script>
    <script src="<?php echo base_url("assets/plugins-bower/jGrowl/jquery.jgrowl.min.js"); ?>"></script>
    <script src="<?php echo base_url("assets/plugins-bower/select2/dist/js/select2.min.js"); ?>"></script>

    <script src="<?php echo base_url("assets/js/base/form.js"); ?>"></script>
    <script src="<?php echo base_url("assets/js/base/function.js"); ?>"></script>
    <script src="<?php echo base_url("assets/manager/js/application.js?v=".VERSION_JS); ?>"></script>
    <script src="<?php echo base_url("assets/front/js/rule.js"); ?>"></script>
    <script src="<?php echo base_url("assets/plugins/ajaxzip3.js"); ?>"></script>
    <script src="<?php echo base_url("assets/plugins/mouse_pointer.js"); ?>"></script>
    <link rel="stylesheet" href="<?php echo base_url("assets/manager/style.css?v=20241128"); ?>"/>
    <?php if (empty($is_call_index_css)) { ?>
        <link rel="stylesheet" href="<?php echo base_url("assets/manager/css/base.css?v=20241128"); ?>"/>
        <link rel="stylesheet" href="<?php echo base_url("assets/manager/css/index.css?v=20241128"); ?>"/>
    <?php }else{ ?>
        <style>
            .application_wrap .event-fee-form .fee-item-error{
                background-color: unset !important;
            }
        </style>
    <?php } ?>
    <link rel="stylesheet" href="<?php echo base_url("assets/manager/css/event.css?v=20241128"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/manager/css/application.css?v=20241128"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/manager/css/application_form.css?v=20241128"); ?>"/>
    <link rel="stylesheet" href="<?php echo base_url("assets/front/css/trade_info.css?v=20241128"); ?>"/>
</head>
<body>
<div class="page-payment w-100">
	<div class="event_container form-over w-100" <?php echo empty($is_call_index_css) ? 'style="margin: 0 auto;"' : '' ?>>
		<div class="form_area box_content-bg-fff rounded-0 m-auto">
			<?php echo isset($content) ? $content : '' ?>
		</div>
	</div>
</div>

<!-- Loading -->
<div class="message-loading-overlay customer-overlay" id="loading-overlay" style="display: none;">
    <i class="fa-spin ace-icon fa fa-refresh big-spin"></i>
</div>
<!-- /Loading -->
<!-- Confirm modal -->
<div class="modal-dialog manager-modal notification_modal e_confirm_modal soft_hide" id="confirm-modal-template">
    <div class="modal-content">
        <div class="modal-header no-padding">
            <span type="button" class="close glyphicon glyphicon-remove" data-dismiss="modal"
                  aria-label="Close"></span>
        </div>
        <div class="modal-body">
            <div class="notification_popup_container">
                <div class="e_confirm_text">msg</div>
                <div class="button-container e_confirm_button_container">
                    <a class="black-button big-button soft_hide e_confirm_button_link"
                       data-dismiss="modal"
                       aria-label="Close" href="#">
                        <span class="e_confirm_button_text">button-text</span>
                        <span class="glyphicon glyphicon-triangle-right"></span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Confirm modal -->
</body>
</html>