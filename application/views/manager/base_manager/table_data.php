<div class="dataTables_wrapper no-footer manager_table_scroll">
    <table cellpadding="0" cellspacing="0" border="0"
           class="table table-striped table-bordered table-hover dataTable no-footer DTTT_selectable background-write">
        <thead>
        <tr>
            <?php
            foreach ($columns as $key => $value) {
                $class = "";
                $over = "";
                if (isset($order_view[$key])) {
                    $temp = strtolower($order_view[$key]);
                    $class .= " sorting_" . strtolower($order_view[$key]);
                } else {
                    $temp = "";
                    $class .= " sorting ";
                    isset($value['field']) && $value['field'] == 'total_ship' ? $class = "" : $class;
                }

                $more_attr = "";

                if (isset($value['table']['attr'])) {
                    $more_attr = $value['table']['attr'];
                }
                if (isset($value['table']['class'])) {
                    $class .= " " . $value['table']['class'];
                }

                if ($value['mark']) {
                    $key = $value['field'];
                    if($value['field'] == 'id as is_answer_survey') {
                        $key = 'is_answer_survey';
                    }
                    $icon = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                              <path id="help_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M87.96-867.2a.965.965,0,0,0,.71-.29.965.965,0,0,0,.29-.71.965.965,0,0,0-.29-.71.966.966,0,0,0-.71-.29.966.966,0,0,0-.71.29.965.965,0,0,0-.29.71.966.966,0,0,0,.29.71A.965.965,0,0,0,87.96-867.2Zm-.72-3.08h1.48a2.91,2.91,0,0,1,.15-1.04,3.5,3.5,0,0,1,.85-1.04,6,6,0,0,0,.82-.99,2.056,2.056,0,0,0,.3-1.13,2,2,0,0,0-.82-1.72,3.2,3.2,0,0,0-1.94-.6,2.769,2.769,0,0,0-1.85.6,3.175,3.175,0,0,0-.99,1.44l1.32.52a2.176,2.176,0,0,1,.45-.78,1.313,1.313,0,0,1,1.07-.42,1.237,1.237,0,0,1,.96.35,1.118,1.118,0,0,1,.32.77,1.3,1.3,0,0,1-.24.75,3.219,3.219,0,0,1-.6.65,4.977,4.977,0,0,0-1.08,1.18A3.643,3.643,0,0,0,87.24-870.28ZM88-864a7.79,7.79,0,0,1-3.12-.63,8.079,8.079,0,0,1-2.54-1.71,8.079,8.079,0,0,1-1.71-2.54A7.79,7.79,0,0,1,80-872a7.79,7.79,0,0,1,.63-3.12,8.078,8.078,0,0,1,1.71-2.54,8.078,8.078,0,0,1,2.54-1.71A7.791,7.791,0,0,1,88-880a7.791,7.791,0,0,1,3.12.63,8.078,8.078,0,0,1,2.54,1.71,8.078,8.078,0,0,1,1.71,2.54A7.79,7.79,0,0,1,96-872a7.79,7.79,0,0,1-.63,3.12,8.079,8.079,0,0,1-1.71,2.54,8.079,8.079,0,0,1-2.54,1.71A7.79,7.79,0,0,1,88-864Zm0-1.6a6.177,6.177,0,0,0,4.54-1.86A6.177,6.177,0,0,0,94.4-872a6.176,6.176,0,0,0-1.86-4.54A6.177,6.177,0,0,0,88-878.4a6.177,6.177,0,0,0-4.54,1.86A6.176,6.176,0,0,0,81.6-872a6.177,6.177,0,0,0,1.86,4.54A6.177,6.177,0,0,0,88-865.6ZM88-872Z" transform="translate(-80 880)" fill="#fff"/>
                            </svg>';
                    $over = "<a href=\"#\" class=\"ticket_management_page over_mark_admin content_" .$key ."\"><span id=\"content_$key\" class=\"ms-2 tips\">$icon</span></a>";
                }
                $class = str_replace("'", "", $class);
                $class = str_replace('"', "", $class);
                $order_post = array_search($key, array_keys($order_view));
                ?>
                <th class="<?php echo $class; ?>" <?php echo $more_attr; ?> order="<?php echo $temp; ?>"
                    <?php echo (array_search($key, array_keys($order_view)) !== FALSE) ? "order_pos='" . $order_post . "'" : "" ?>
                    field_name="<?php echo $key; ?>"><?php echo is_string($value) ? my_lang($value) : my_lang($value['label'] .$over); ?></th>
            <?php } ?>
        </tr>
        </thead>
        <?php if (sizeof($record)) { ?>
            <tbody>
            <?php foreach ($record as $item) { ?>
                <tr data-id="<?php echo $item->$key_name; ?>" class="<?php echo isset($item->delegate) && empty($item->delegate) ? 'highlight-row' : '' ;?>">
                    <?php foreach ($columns as $key => $value) {
                        $more_attr = "";
                        $class = "";
                        if (isset($value['table']['attr'])) {
                            $more_attr = $value['table']['attr'];
                        }
                        if (isset($value['table']['class'])) {
                            $class = $value['table']['class'];
                        }
                        $explodeResult = explode(".", $key);
                        $dataKey = end($explodeResult);
                        ?>
                        <td for_key="<?php echo $key . ""; ?>" class="<?php echo $class ?>" <?php echo $more_attr; ?> >


                            <?php echo isset($item->$dataKey) ? my_lang($item->$dataKey) : ''; ?>
                        </td>
                    <?php } ?>
                </tr>
            <?php } ?>
            </tbody>
        <?php } ?>
    </table>
</div>
<?php if (!sizeof($record)) { ?>
    <p class="no_record"><?= my_lang('データがございません。')?></p>
<?php } else { ?>
    <div class="row no-magin pagination__base--box">
        <div class="col-xs-8 pagination-custom">
            <div class="dataTables_paginate paging_bootstrap pagination no-margin subscribers-top-paging">
                <?php echo $paging; ?>
            </div>
        </div>
    </div>
<?php } ?>
