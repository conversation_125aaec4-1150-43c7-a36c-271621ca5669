<div class="text-center text-20-28 fw-bold mb-4 mb-xl-5"><?= my_lang('お受取口座情報')?></div>
<div class="forn_descript text-12-14">
    <?= my_lang('下記の内容は、予約受付後の自動返信メールにて、お申し込み者に通知されます。')?><br>
    <?= my_lang('なお、お受取口座名義は、フリッパ＋口座名義任意名となり自動で付与されます。')?>
</div>
<br>
<div class="form_holder event_wrap">
    <!-- row 1 -->
    <div class="input_holder mb-3">
        <div class="no-gap-col">
            <div class="input_title mb-2 text-14-16 fw-bold"><?= my_lang('口座名義任意名')?>
            </div>
        </div>
        <div class="form_holder-parentnew w-100">
            <div class="form-group input_field_holder item-box">
                <input readonly type="text" name="account_name" class="input_field outline-none placeholder-none mt-0" placeholder="<?= my_lang('20文字以内の「半角カナ」でご入力ください。')?>"
                       value="<?php echo isset($receiver_banking->account_name) ? $receiver_banking->account_name : ''; ?>">
            </div>
        </div>
    </div>
    <!-- row 2 -->
    <div class="input_holder noted_input">
        <div class="no-gap-col">
            <div class="input_title mb-2 text-14-16 fw-bold"><?= my_lang('お支払い期限日')?>
            </div>
        </div>
        <div class="form_holder-parentnew w-100">
            <div class="form-group input_field_holder d-flex flex-column gap-3">
                <div class="item-box">
                    <div class="far_icon position-relative d-flex align-content-center gap-3 mt-0">
                        <input readonly type="text" name="transfer_deadline" class="date_picker_fee input_date h-auto input_field w-100 flex-grow-1 outline-none  placeholder-none mt-0"
                               placeholder="<?= my_lang('0000年00月00日')?>"
                               data-img="<?php echo base_url() . "/assets/manager/images/calender-ic.svg"; ?>"
                               value="<?php echo isset($receiver_banking->transfer_deadline) ? $receiver_banking->transfer_deadline : ''; ?>"
                        >
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- row 3 -->
    <div class="input_holder noted_input">
        <div class="no-gap-col">
            <div class="input_title mb-2 text-14-16 fw-bold"><?= my_lang('お問い合わせ先')?>
            </div>
        </div>
        <div class="form_holder-parentnew w-100">
            <div class="form-group input_field_holder item-box">
                <input readonly type="text" name="contact_information" class="input_field w-100 outline-none placeholder-none mt-0"
                       value="<?php echo isset($receiver_banking->contact_information) ? $receiver_banking->contact_information : ''; ?>">
            </div>
        </div>
    </div>

    <!-- row 4 -->
    <div class="input_holder noted_input">
        <div class="no-gap-col">
            <div class="input_title mb-2 text-14-16 fw-bold"><?= my_lang('お問い合わせメールアドレス')?>
            </div>
        </div>
        <div class="form_holder-parentnew w-100">
            <div class="form-group input_field_holder item-box">
                <input readonly type="text" name="email" class="input_field w-100 outline-none placeholder-none mt-0"
                       value="<?php echo isset($receiver_banking->email) ? $receiver_banking->email : ''; ?>">
            </div>
        </div>
    </div>

    <!-- row 5 -->
    <div class="input_holder noted_input">
        <div class="no-gap-col">
            <div class="input_title mb-2 text-14-16 fw-bold"><?= my_lang('お問い合わせ')?><br><?= my_lang('電話番号')?>
            </div>
        </div>
        <div class="d-flex flex-column gap-2 form_holder-parentnew w-100 mb-3">
            <div class="input_field_holder form-group item-box">
                <input readonly type="text" name="phone_number" class="input_field outline-none placeholder-none mt-0"
                       value="<?php echo isset($receiver_banking->phone_number) ? $receiver_banking->phone_number : ''; ?>">
            </div>
        </div>

    </div>
</div>