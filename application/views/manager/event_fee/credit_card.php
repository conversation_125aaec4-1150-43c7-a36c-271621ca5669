<?php $lang = isset($lang) ? $lang : null?>
<style>
    .w-100 {
        width: 100% !important;
    }
    .d-flex {
        display: flex;
    }
    .flex-column {
        flex-direction: column;
    }
    .align-items-center {
        align-items: center;
    }
    .text-14-16 {
        font-size: 14px ;
        line-height: 19px;
    }
    @media (min-width: 1024px) {
        .text-14-16 {
            font-size: 16px;
            line-height: 21px;
        }
        .flex-xl-row {
            flex-direction: row;
        }
    }
    .mb-0 {
        margin-bottom: 0;
    }
    .mb-8-9 {
        margin-bottom: 8px !important;
    }
    @media (min-width: 1024px) {
        .mb-8-9 {
            margin-bottom: 9px !important;
        }
        .align-items-xl-center {
            align-items: center;
        }
    }
    .px-0 {
        padding-right: 0px;
        padding-left: 0px;
    }
    .page-payment h2 {
        font-size: 24px;
        line-height: 56px;
        margin-top: 0;
        margin-bottom: 0;
    }
    @media (min-width: 1024px) {
        .page-payment h2 {
            font-size: 40px;
            margin-bottom: 12px;
        }
    }
    .mt-0 {
        margin-top: 0 !important;
    }
    .ms-0 {
        margin-left: 0;
    }
    .gap-4 {
        gap: 16px;
    }
    .minw-120-200 {
        min-width: 120px;
    }
    @media (min-width: 1024px) {
        .minw-120-200 {
            min-width: 200px;
        }
        .mb-xl-0 {
            margin-bottom: 0 !important;
        }
    }
</style>
<div class="input_row no-gap-row">
	<div class="d-flex align-items-xl-center flex-column flex-xl-row form-group ">
		<div class="col-12 col-lg-4 no-gap-col noted_input padding-title px-0">
			<label class="control-label input_title text-14-16 mb-8-9 mb-xl-0" title="<?= my_lang('カード番号',$lang)?>"><?= my_lang('カード番号',$lang)?></label>
		</div>
		<div class="col-12 col-lg-8 padding-mobile px-0">
			<input autocomplete="new-password" type="text"
			       class="input_field_credit input-custom" name="card_no" id="cardno"
			       value="">
		</div>
		<div class="clear"></div>
	</div>
</div>

<div class="input_row no-gap-row">
	<div class="d-flex align-items-xl-center flex-column flex-xl-row form-group ">
		<div class="col-12 col-lg-4 no-gap-col noted_input padding-title px-0">
			<label class="control-label input_title text-14-16 mb-8-9 mb-xl-0" title="<?= my_lang('カード有効期限YYMM',$lang)?>"><?= my_lang('カード有効期限YYMM',$lang)?></label>
		</div>
		<div class="col-12 col-lg-8 padding-mobile px-0">
			<div class="wrapper-expire">
				<div class="flex-1">
                    <div class="form_holder-parentnew w-100">
                        <div class="item-box">
                            <select class="shadow_input mt-0" name="expire_year" id="expire_year">
                                <option value=""></option>
                                <?php
                                for ($i=date("Y"); $i < (date("Y")+30); $i++) {
                                    echo "<option value=\"$i\">$i</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>
				</div>
				<div class="flex-1">
                    <div class="form_holder-parentnew w-100">
                        <div class="item-box">
                            <select class="shadow_input mt-0" name="expire_month" id="expire_month">
                                <option value=""></option>
                                <?php for ($i = 1; $i <= 12; $i++) {
                                    $key = ($i < 10) ? ('0' . $i) : $i;
                                    ?>
                                    <option value="<?php echo $key; ?>"><?php echo $key; ?></option>
                                <?php } ?>
                            </select>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="input_row no-gap-row">
	<div class="d-flex align-items-xl-center flex-column flex-xl-row form-group">
		<div class="col-12 col-lg-4 no-gap-col noted_input padding-title px-0">
			<label class="control-label input_title text-14-16 mb-8-9 mb-xl-0" title="<?= my_lang('セキュリティコード',$lang)?>"><?= my_lang('セキュリティコード',$lang)?></label>
		</div>
		<div class="col-12 col-lg-8 px-0 d-flex gap-4">
            <div class="col-12 col-lg-6 padding-mobile px-0">
                <input autocomplete="new-password" type="text"
                       class="input_field_credit input-custom" name="security_code" id="securitycode"
                       value="">
            </div>
            <div class="col-12 col-lg-6 security_code_image px-0 ms-0 mt-0">
                <img src="<?php echo base_url('assets/images/credit-card/securitycode.png') ?>" alt="securitycode">
            </div>
        </div>
	</div>
</div>

<div class="row input_row no-gap-row hidden">
    <div class="col-xs-12 col-md-6 no-gap-col ">
        <div class="form-group clearfix">
            <div class="col-12 col-lg-5 no-gap-col padding-title">
                <label class="control-label input_title text-14-16 mb-8-9 mb-xl-0" title="<?= my_lang('カード名義人',$lang)?>"><?= my_lang('カード名義人',$lang)?></label>
            </div>
        </div>
    </div>
</div>

<div class="wrap_subscriber">
    <button type="button" id="i_back_application_form" class="back_application_form btn-black-common minw-120-200">
        <?= my_lang('戻る',$lang)?>
    </button>
    <button id="get_gmo_token_btn" class="btn-yellow-common minw-120-200" onclick="doPurchase();return false;">
        <?= my_lang('送信',$lang)?>
    </button>

    <input type="submit" id="subscriber_btn" style="display: none;" />
</div>

<div class="col-xs-6 no-gap-col form-group clearfix">
    <input type="hidden" name="token" value="" id="token"/>
</div>
<div class="col-xs-6 no-gap-col form-group clearfix">
    <input type="hidden" name="access_id" value="<?php echo $AccessID; ?>"/>
</div>
<div class="col-xs-6 no-gap-col form-group clearfix">
    <input type="hidden" name="access_pass" value="<?php echo $AccessPass; ?>"/>
</div>
<div class="col-xs-6 no-gap-col form-group clearfix">
    <input type="hidden" name="order_id" value="<?php echo $OrderID; ?>"/>
</div>
<div class="col-xs-6 no-gap-col form-group clearfix">
    <input type="hidden" name="amount" value="<?php echo $Amount; ?>"/>
</div>

<script src='https://static.mul-pay.jp/ext/js/token.js'></script>
<script type="text/javascript">

    function execPurchase(response) {
        if (response.resultCode != '000') {
            window.alert("<?= my_lang('購入処理中にエラーが発生しました',$lang)?>")
        } else {
            //カード情報は念のため値を除去
            document.getElementById('cardno').value = "";
            document.getElementById('expire_year').value = "";
            document.getElementById('expire_month').value = "";
            document.getElementById('securitycode').value = "";
            // document.getElementById('tokennumber').value = "";
            //予め購入フォームに用意した token フィールドに、値を設定
            document.getElementById('token').value = response.tokenObject.token;
            //スクリプトからフォームを submit
            // document.getElementById('purchaseForm').submit();
            $("#subscriber_btn").click();
        }
    }

    function doPurchase() {
        var cardno, expire, securitycode;
        cardno = document.getElementById('cardno').value;
        expire = document.getElementById('expire_year').value + '' + document.getElementById('expire_month').value;
        securitycode = document.getElementById('securitycode').value;
        tokennumber = "";
        Multipayment.init("9101272446002");
        Multipayment.getToken(
            {
                cardno: cardno,
                expire: expire,
                securitycode: securitycode,
                tokennumber: tokennumber
            }, execPurchase
        );
    }               

    function debugPurchase() {
        document.getElementById('cardno').value = "debug";
        $("#subscriber_btn").click();
    } 
</script>
