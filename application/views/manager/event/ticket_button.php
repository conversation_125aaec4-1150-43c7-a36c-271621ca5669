<div class="main-button-container ticket-container ticket__button--bottom">
    <div class="bottom-event-action">
        <a class="text-decoration-none" href="<?php echo isset($step_previous) ? $step_previous : '#' ?>">
            <button type="button" class="black-button back-button big-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="9.867" height="16" viewBox="0 0 9.867 16">
                    <path id="chevron_right_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M323.733-712l6.133-6.133L328-720l-8,8,8,8,1.867-1.867Z" transform="translate(-320 720)" fill="#fff"/>
                </svg>
                <?= my_lang('戻 る')?>
            </button>
        </a>
    </div>
    <div class="bottom-event-action">
        <?php if (isset($edit_status) && $edit_status) { ?>
            <button type="submit" class="black-button big-button">
                <?= my_lang('確定する')?>
            </button>
        <?php } else { ?>
            <button class="black-button big-button e_ajax_confirm" type="button"
                    data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに')?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は')?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。')?>"
                    data-button-class=";submit_ticket_form"
                    data-button="<?= my_lang('閉じる;実行')?>" data-button-url="#;#">
                <?= my_lang('確定する')?>
            </button>
        <?php } ?>
    </div>
</div>