<div class="event_container overview-form">
    <div class="overview-container">
        <?php if (isset($check_input_email['state']) && $check_input_email['state']) { ?>
            <div class="overview-little-box">
                <div class="overview-main-title">
                    <div><?= my_lang('タイトル：') . my_lang('[イベント名]電子チケットの送付', $event_info->lang)?></div>
                    <div><?= my_lang('宛先：申請者')?></div>
                </div>
                <div class="little-box-content first-overview-box">
                    <?php if ($event_info->status != "in_preparation") { ?>
                        <button type="button" class="black-button e_ajax_confirm small-button edit-button"
                                data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに')?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は')?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。')?>"
                                data-button-class="; e_ajax_link"
                                data-button="<?= my_lang('閉じる;実行')?>"
                                data-button-url="#;<?php echo site_url("manager/event/edit_mail_e_ticket/" . $event_id) ?>">
                            <?= my_lang('本文編集')?>
                            <span class="glyphicon glyphicon-triangle-right"></span>
                        </button>
                    <?php } else { ?>
                        <a class="e_ajax_link"
                           href="<?php echo site_url("manager/event/edit_mail_e_ticket/" . $event_id) ?>">
                            <button class="black-button small-button edit-button"><?= my_lang('本文編集')?>
                                <span class="glyphicon glyphicon-triangle-right"></span>
                            </button>
                        </a>
                    <?php } ?>
                    <div id="i_content_mail_e_ticket">
        
                        <?php if (!empty($event_info->e_ticket_mail)) {
                            echo $event_info->e_ticket_mail;
                            
                        } else { ?>
                            [<?= my_lang('[予約者会社名]', $event_info->lang)?></br>
                            <?= my_lang('[予約者部署名]', $event_info->lang)?></br>
                            <?= my_lang('[予約者氏名]様', $event_info->lang)?></br></br>

                            <?= my_lang('この度は、[イベント名]にお申し込みいただきまして誠にありがとうございました。', $event_info->lang)?></br>
                            <?= my_lang('下記の通り、受け付けを完了し、電子チケットを添付画像にてお送りいたします。', $event_info->lang)?></br></br>

                            <?= my_lang('当日の受付にて添付画像のご提示をお願いいたします。', $event_info->lang)?>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <?php if (isset($event_info->type_ticket) && $event_info->type_ticket > 0) { ?>
                <div class="overview-little-box">
                    <div class="little-box-title" style="white-space: nowrap;">
                        <span class="arrow-left"></span>
                        <?= my_lang('電子チケット（チケット無しでの発券、又は上記で動画視聴画面URLを設定した場合は添付されません。）')?>
                    </div>
                    <div class="little-box-content">
                        <div class="ticket-box">
                            <?php echo $template_ticket_content; ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <div class="overview-little-box">
                <div class="little-box-title">
                    <div><span class="arrow-left"></span><?= my_lang('お申し込み内容（氏名・メールアドレス等が自動挿入されます。）')?></div>
                </div>
                <div class="little-box-content">
                    
                </div>
            </div>
            <br>
            <div class="overview-little-box" style="display: none;">
                <div class="little-box-title ">
                    <div><span class="arrow-left"></span><?= my_lang('メール送信')?></div>
                </div>
                <div class="little-box-content">
                    <div class="full_width method_description">
                        <?= my_lang('上記のスクリプトを下記のアドレスにメールで送る。(最大3件個別に同時送信できます。）')?>
                    </div>
                    <form action="<?php echo isset($send_mail_template) ? $send_mail_template : '#'; ?>"
                          class="e_ajax_submit" method="post">
                        <div class="content_box">
                            <div class="form-group">
                                <input type="text" name="email1" class="shadow_input full_width high_input"
                                       id="input1">
                            </div>
                            <div class="form-group">
                                <input type="text" name="email2" class="shadow_input full_width high_input"
                                       id="input2">
                            </div>
                            <div class="form-group">
                                <input type="text" name="email3" class="shadow_input full_width high_input"
                                       id="input3">
                            </div>
                        </div>
                        <div class="main-button-container">
                            <button type="button" class="black-button small-button"><?= my_lang('送　信')?>
                                <span class="glyphicon glyphicon-triangle-right"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        <?php } else { ?>
            <div class="form_area new_design" style="margin-bottom: 40px">
                <div class="form_box new_design">
                    <div class="event-detail-title">
                        <?= my_lang('イベント名')?>：<?php echo isset($event_info) ? $event_info->name : '' ?>
                    </div>
                    <div class="notify_content new_design">
                        <?php echo isset($check_input_email['msg']) ? $check_input_email['msg'] : ''; ?>
                    </div>
                </div>
            </div>
        <?php } ?>
        <form class="e_ajax_submit" id="form_list_email" method="post" action="<?php echo isset($save_link) ? $save_link : "#" ?>">
            <?php if (isset($check_input_email['state']) && $check_input_email['state']) { ?>
                <div class="overview-little-box">
                    <div class="little-box-title ">
                        <div><span class="arrow-left"></span><?= my_lang('予約変更・キャンセルについて（自動発券の無料イベントのみURLが自動挿入されます。）')?></div>
                        <?php if (isset($event_type) && !$event_type) { ?>
                            <!--div class="update_link_container">
                                <div class="update_link_option">
                                    <input type="checkbox" name="is_update_link" value="1" id="is_update_link_01"
                                        <?php echo isset($event_info->is_update_link) && $event_info->is_update_link == 1 ? 'checked' : '' ?>>
                                    <label for="is_update_link_01">あり</label>
                                </div>
                                <div class="update_link_option">
                                    <input type="checkbox" name="is_update_link" value="0" id="is_update_link_00"
                                        <?php echo isset($event_info->is_update_link) && $event_info->is_update_link == 0 ? 'checked' : '' ?>>
                                    <label for="is_update_link_00">なし</label>
                                </div>
                            </div-->
                        <?php } ?>
                    </div>
                    <!-- <div class="little-box-content last-overview-box">
                        <p>
                            こちらにお申込み者専用画面のURLが挿入されます。
                        </p>
                    </div> -->
                </div>
                <div class="overview-little-box  overview-little-box-custom">
                    <div class="little-box-title little-box-custom">
                        <div><span class="star">＊</span>未到達メールの返却について</div>
                    </div>
                    <div class="little-box-content last-overview-box little-box-content-custom">
                        <div class="list_email_contain">
                            <div class="list_mail_title">
                                <?= my_lang('上記で設定した電子チケット送信メールが、何らかの理由により予約者の入力したメールアドレスに届かない場合')?>、
                                <?= my_lang('未到達メールとして返却いたします。')?>
                                <?= my_lang('下記に返却先の宛先をご登録ください。（未到達メールは予約者リスト上でもご確認いただけます。）')?>
                            </div>
                            <div class="list_email_content">
                                <?php for ($i = 0; $i < 2; $i++) { ?>
                                    <div class="list_email_row" id="<?php echo $i ?>">
                                        <div class="form-group col-xs-5">
                                            <div class="col-xs-4 no-gap-col">
                                                <label class="control-label input_title" title="<?= my_lang('メールアドレス')?>①">
                                                    <?= my_lang('メールアドレス')?><?php echo ($i == 0) ? '①' : '②'; ?>：
                                                </label>
                                            </div>
                                            <div class="col-xs-6">
                                                <input name="list_email[]" class="input_field email"
                                                       value="<?php echo isset($list_email[$i]) ? $list_email[$i] : ''; ?>"
                                                       id="email">
                                            </div>
                                            <div class="clear"></div>
                                        </div>
                                        <div class="form-group col-xs-7">
                                            <div class="col-xs-6 no-gap-col">
                                                <label class="control-label input_title" title="<?= my_lang('再確認用メールアドレス')?>①">
                                                    <?= my_lang('再確認用メールアドレス')?><?php echo ($i == 0) ? '①' : '②'; ?>：
                                                </label>
                                            </div>
                                            <div class="col-xs-6">
                                                <input name="list_confirm_email[]" class="input_field confirm_email"
                                                       value="<?php echo isset($list_email[$i]) ? $list_email[$i] : ''; ?>"
                                                       id="confirm_email">
                                            </div>
                                            <div class="clear"></div>
                                        </div>
                                        <div class="clear"></div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>

            <input type="hidden" name="event_id" value="<?php echo isset($event_id) ? $event_id : 0; ?>">
            <div class="overview-little-box">
                <div class="main-button-container">
                    <a href="<?php echo isset($step_previous) ? $step_previous : '#' ?>">
                        <button type="button" class="black-button big-button back-button">
                            <?= my_lang('戻 る')?>
                            <span class="glyphicon glyphicon-triangle-left"></span>
                        </button>
                    </a>
                    <?php if ($event_info->status != "in_preparation") { ?>
                        <button type="button" class="black-button e_ajax_confirm big-button"
                                data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに')?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は')?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。')?>"
                                data-button-class="; submit_overview_form"
                                data-button="<?= my_lang('閉じる;実行')?>"
                                data-button-url="#;<?php echo isset($save_link) ? $save_link : "#" ?>">
                            <?= my_lang('次のステップへ')?>
                            <span class="glyphicon glyphicon-triangle-right"></span>
                        </button>
                    <?php } else { ?>
                    <button type="submit" class="black-button big-button">
                        <?= my_lang('次のステップへ')?>
                        <span class="glyphicon glyphicon-triangle-right"></span>
                    </button>
                    <?php } ?>
                </div>
            </div>
        </form>
    </div>

</div>