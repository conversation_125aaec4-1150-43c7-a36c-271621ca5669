<?php
$view = (!isset($is_view) || (isset($is_view) && !$is_view)) ? '' : 'disabled';
$default_checked_field = true;
$iconPlus = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><path id="add_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M208.571-748.571H200v-2.857h8.571V-760h2.857v8.571H220v2.857h-8.571V-740h-2.857Z" transform="translate(-200 760)"/></svg>';
?>
<div class="event_container event_container application_form-box mb-40-80">
    <div id="i_event_application_form" class="checkbox-custom">
        <form class="e_ajax_submit" id="i_form_event" method="post"
              action="<?php echo isset($save_temp_link) ? $save_temp_link : "#" ?>">
            <div class="form_area">
                <div class="guide_text">
                    <!--イベントお申し込みフォームの入力項目を選択してください。<br>
                    予約申し込みフォームで何も問わずに、参加人数のみを把握したい場合は、<br>
                    下欄の「申し込みボタンのみ掲載」を選択してください。-->
                    <h3 class="fw-bold"><?= my_lang('お申込者情報設定') ?></h3>
                </div>
                <!-- form box -->
                <div class="form_box">
                    <div class="bg-white border-radius-bot-24">
                        <div class="event-detail-title application-title fw-bold text-16-20"><?php echo isset($event_info) ? $event_info->name : '' ?>
                            　<?= my_lang('お申し込みフォーム') ?>
                        </div>
                        <div class="form_box_content">
                            <input type="hidden" name="event_id" value="<?php echo isset($event_id) ? $event_id : 0; ?>">
                            <input type="hidden" name="event_name"
                                   value="<?php echo isset($event_name) ? $event_name : ''; ?>">
                            <div class="input_box-new pb-3 ps-xl-5">
                                <label class="text_required_emmail text-14-16"><?php echo my_lang('氏名・メールアドレスは必須となります。') ?></label>
                            </div>
                            <?php
                            $show_button_add_custom_field_password = TRUE;
                            foreach ($list_item_form as $index => $item) {

                                // AnhLD - hide custom text view field
                                $hide_row = "";
                                foreach ($item as $value) {
                                    if (is_object($value) && isset($value->type) && $value->type == "text_view") {
                                        $hide_row = "hidden";
                                        break;
                                    }
                                }

                                ?>
                                <div class="input_row e_input_row input_box-new gap-4 d-grid grid-template-columns-two position-relative <?php echo $hide_row; ?>">
                                    <?php foreach ($item as $value) { ?>
                                        <?php if (is_array($value) && count($value)) {
                                            $checked_required_str = '';
                                            $checked_show_on_friend_form_str = '';
                                            $disabled_show_on_friend_form_str = '';
                                            ?>
                                            <div class="d-flex gap-3 flex-column flex-xl-row align-items-start">
                                            <?php
                                            foreach ($value as $field) {
                                                $checked_field_id_str = (!empty($field->event_id) || !empty($field->is_custom_field)) ? 'checked' : '';
                                                if (!$checked_required_str) {
                                                    $checked_required_str = !empty($field->required) ? 'checked' : '';
                                                }

                                                if (($field->field_name == 'first_name' || $field->field_name == 'email') && $checked_field_id_str == 'checked') {
                                                    $default_checked_field = false;
                                                }

                                                // AnhLD - pharse 7
                                                if ($field->field_id != 9 && $field->field_id != 20) {
                                                    $checked_show_on_friend_form_str = !empty($field->show_on_friend_form) ? 'checked' : '';
                                                    $disabled_show_on_friend_form_str = !empty($field->show_on_friend_form) ? '' : 'disabled';
                                                }

                                                $disabled_required_str = !empty($field->required) ? '' : 'disabled';
                                                $disabled_selector = ($count_subscriber_form || ($page_draft == $draft_step)) ? '' : 'disabled';
                                                $e_is_custom_field = '';
                                                if (!empty($field->is_custom_field)) {
                                                    $e_is_custom_field = "e_is_custom_field";
                                                    if (isset($field->type) && $field->type != 'text') {
                                                        $e_is_custom_field .= '_' . $field->type;
                                                    }
                                                }
                                                ?>
                                                <div class="d-flex align-items-start gap-2 w-xl-50 <?php echo $e_is_custom_field; ?>">
                                                    <input <?php echo $disabled_selector ?>
                                                        <?php echo $view; ?>
                                                            class="e_change_field_required m-0"
                                                            name="field_id[]"
                                                            data-field-name="<?php echo $field->field_name ?>"
                                                            type="checkbox"
                                                        <?php echo ($field->field_id == 1 || $field->field_id == 13) ? 'onclick="return false;"' : ""; ?>
                                                            id="<?php echo 'choose_' . $field->field_id ?>"
                                                            value="<?php echo $field->field_id ?>" <?php echo $checked_field_id_str; ?>>

                                                    <input type="hidden" <?php echo $view; ?>
                                                           name="required[]" <?php echo $disabled_required_str; ?>
                                                           value="<?php echo $field->field_id ?>"/>
                                                    <!--AnhLD -pharse 7-->
                                                    <input type="hidden" <?php echo $view; ?>
                                                           name="show_on_friend_form[]" <?php echo $disabled_show_on_friend_form_str; ?>
                                                           value="<?php echo $field->field_id ?>"/>

                                                    <input type="hidden" <?php echo $view; ?>
                                                           name="type[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                           value="<?php echo isset($field->type) ? $field->type : ''; ?>"/>
                                                    <input type="hidden" <?php echo $view; ?>
                                                           name="is_custom_field[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                           value="<?php echo isset($field->is_custom_field) ? $field->is_custom_field : ''; ?>"/>
                                                    <input type="hidden" <?php echo $view; ?>
                                                           name="max_choice[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                           value="<?php echo isset($field->max_choice) ? $field->max_choice : 0; ?>"/>
                                                    <input type="hidden" <?php echo $view; ?>
                                                           name="position[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                           value="<?php echo isset($field->default_position) ? $field->default_position : 0; ?>"/>
                                                    <textarea <?php echo $view; ?>
                                                        class="hidden" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                        name="default_value[]"><?php echo !empty($field->default_value) ? $field->default_value : '' ?></textarea>
                                                    <label for="<?php echo 'choose_' . $field->field_id ?>"
                                                           class="text-xl-nowrap normal_text"><?php echo $field->field_display_name . ((!empty($field->field_note) && empty($field->is_custom_field)) ? "(" . $field->field_note . ")" : "") ?></label>
                                                </div>

                                            <?php } ?>
                                            </div>
                                            <?php
                                            // 8.会社名 9.会社名フリガナ
                                            if ($field->field_id == 9) {
                                                $field->field_id == 8;
                                            }
                                            // 13.メールアドレス 20.再確認用メールアドレス
                                            if ($field->field_id == 20) {
                                                $field->field_id == 13;
                                            }
                                            ?>

                                            <div class="d-flex flex-column flex-xl-row gap-3 justify-content-xl-end">
                                                <!-- AnhLD - pharse 7 -->
                                                <div class="pull-right">
                                                    <div class="checkbox_col align-items-start gap-2">
                                                        <input <?php echo $disabled_selector ?>
                                                            <?php echo $view; ?>
                                                                id="<?php echo 'require' . $field->field_id ?>" type="checkbox"
                                                                class="e_change_field_required e_field_required m-0"
                                                            <?= $field->field_id == 1 || $field->field_id == 20 ? 'onclick="return false;"' : ""?>
                                                                value="1" <?php echo $checked_required_str; ?>>
                                                        <label for="<?php echo 'require' . $field->field_id ?>"
                                                               class="normal_text"><?= my_lang('必須項目') ?></label>
                                                    </div>
                                                </div>
                                                <div class="pull-right" style="<?= $field->field_id != 1 && $field->field_id != 20 ? "display: block" : "visibility: hidden"  ?>">
                                                    <div class="checkbox_col align-items-start gap-2" style="<?= $field->field_id != 1 && $field->field_id != 20 ? "display: flex" : "visibility: hidden"  ?>">
                                                        <input <?php echo $disabled_selector ?>
                                                            <?php echo $view; ?>
                                                                id="<?php echo 'show_on_friend_form' . $field->field_id ?>"
                                                                type="checkbox"
                                                                class="e_change_field_required e_field_show_on_friend_form m-0"
                                                            <?= $field->field_id == 1 || $field->field_id == 20 ? 'checked' : '' ?>
                                                                value="1" <?php echo $checked_show_on_friend_form_str; ?>>
                                                        <label for="<?php echo 'show_on_friend_form' . $field->field_id ?>"
                                                               class="normal_text"><?= my_lang('同伴者にも必要') ?></label>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php } else {
                                            $checked_field_id_str = (!empty($value->event_id) || !empty($value->is_custom_field)) ? 'checked' : '';
                                            $checked_required_str = (!empty($value->required)) ? 'checked' : '';

                                            // AnhLD- pharse 7
                                            $checked_show_on_friend_form_str = !empty($value->show_on_friend_form) ? 'checked' : '';
                                            $disabled_show_on_friend_form_str = !empty($value->show_on_friend_form) ? '' : 'disabled';

                                            $disabled_required_str = (!empty($value->required)) ? '' : 'disabled';
                                            $disabled_selector = ($count_subscriber_form || ($page_draft == $draft_step)) ? '' : 'disabled';
                                            $e_is_custom_field = '';
                                            if (!empty($value->is_custom_field)) {
                                                $e_is_custom_field = "e_is_custom_field";
                                                if (isset($value->type) && $value->type != 'text') {
                                                    $e_is_custom_field .= '_' . $value->type;
                                                }
                                            }
                                            ?>
                                            <div
                                                    class="e_row_setting d-flex align-items-start gap-2 w-xl-50 <?php echo $e_is_custom_field; ?>">
                                                <input <?php echo $disabled_selector ?>
                                                    <?php echo $view; ?>
                                                        class="e_change_field_required m-0"
                                                        name="field_id[]"
                                                        data-field-name= <?php echo $value->field_name ?>
                                                        type="checkbox"
                                                        id="<?php echo 'choose_' . $value->field_id ?>"
                                                        value="<?php echo $value->field_id ?>" <?php echo $checked_field_id_str; ?>>
                                                <input type="hidden" name="required[]" <?php echo $disabled_required_str; ?>
                                                    <?php echo $view; ?>
                                                       value="<?php echo $value->field_id ?>"/>

                                                <!--AnhLD -pharse 7-->
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="show_on_friend_form[]" <?php echo $disabled_show_on_friend_form_str; ?>
                                                       value="<?php echo $value->field_id ?>"/>

                                                <input type="hidden"<?php echo $view; ?>
                                                       name="type[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->type) ? $value->type : ''; ?>"/>
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="is_custom_field[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->is_custom_field) ? $value->is_custom_field : ''; ?>"/>
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="custom_field_height[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->field_height) ? $value->field_height : ''; ?>"/>
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="custom_field_width[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->field_width) ? $value->field_width : ''; ?>"/>
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="custom_field_color[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->field_color) ? $value->field_color : ''; ?>"/>
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="max_choice[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->max_choice) ? $value->max_choice : 0; ?>"/>
                                                <input type="hidden" <?php echo $view; ?>
                                                       name="position[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?>
                                                       value="<?php echo isset($value->default_position) ? $value->default_position : 0; ?>"/>
                                                <textarea <?php echo $view; ?>
                                                    class="hidden <?php echo (!empty($value->field_name) && $value->field_name == 'rules') ? 'e_default_value' : ''; ?>"
                                                    name="default_value[]" <?php echo $checked_field_id_str ? '' : 'disabled'; ?> ><?php echo !empty($value->default_value) ? $value->default_value : '' ?></textarea>
                                                <?php if($value->field_name == 'subscriber_image'){ ?>
                                                    <label
                                                            class="text-xl-nowrap normal_text left-label-title text-break d-xl-flex gap-4 <?php echo($value->is_custom_field == 1 ? 'label-normal-field' : ''); ?>"
                                                            for="<?php echo 'choose_' . $value->field_id ?>">
                                                        <?php echo $value->field_display_name; ?>
                                                        <div class="text-12-14 fw-normal text-666 text-note-img">
                                                            <div>
                                                                ※<?= my_lang('ファイル種別（拡張子）は「jpg,jpeg,png,bmp」のファイルです。') ?>
                                                            </div>
                                                            <div>
                                                                ※<?= my_lang('ファイルサイズは最大3MB。')?>
                                                            </div>
                                                        </div>
                                                    </label>
                                                <?php }else{ ?>
                                                    <label
                                                            class="text-xl-nowrap normal_text left-label-title text-break <?php echo($value->is_custom_field == 1 ? 'label-normal-field' : ''); ?>"
                                                            for="<?php echo 'choose_' . $value->field_id ?>">
                                                        <?php echo $value->field_display_name . ((!empty($value->field_note) && empty($value->is_custom_field)) ? "(" . $value->field_note . ")" : "") ?>
                                                    </label>
                                                <?php }?>
                                            </div>

                                            <div class="d-flex flex-column flex-xl-row gap-3 justify-content-xl-end">
                                                <!-- AnhLD - pharse 7 -->
                                                <div class="pull-right">
                                                    <div class="checkbox_col align-items-start gap-2">
                                                        <input <?php echo $disabled_selector ?>
                                                            <?php echo $view; ?>
                                                                id="<?php echo 'require' . $value->field_id ?>" type="checkbox"
                                                                class="e_change_field_required e_field_required m-0"
                                                                value="1" <?php echo $checked_required_str; ?>>
                                                        <label for="<?php echo 'require' . $value->field_id ?>"
                                                               class="normal_text"> <?= my_lang('必須項目') ?></label>
                                                    </div>
                                                </div>
                                                <div class="pull-right">
                                                    <div class="checkbox_col align-items-start gap-2">
                                                        <input <?php echo $disabled_selector ?> <?php echo $view; ?>
                                                                class="e_change_field_required e_field_show_on_friend_form m-0"
                                                                type="checkbox"
                                                                value="1" <?php echo $checked_show_on_friend_form_str; ?>
                                                                id="<?php echo 'show_on_friend_form' . $value->field_id ?>">
                                                        <label class="normal_text text-xl-nowrap"
                                                               for="<?php echo 'show_on_friend_form' . $value->field_id ?>">
                                                            <?= my_lang('同伴者にも必要') ?></label>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php if (!empty($value->field_name) && $value->field_name == 'custom_field_password') {
                                                $show_button_add_custom_field_password = FALSE;
                                                ?>
                                                <div class="height-password"></div>
                                                <a class="e_ajax_link btn-edit-default-value e_btn_default_value set_possition text-decoration-none m-0 w-100 position-absolute btn-main9"
                                                   href="<?php echo site_url('manager/form_item/add_form_item_password/' . (isset($event_id) ? $event_id : '') . '/' . $value->field_id); ?>">
                                                    <button class="black-button small-button application-button d-flex align-items-center justify-content-between">
                                                        <?= my_lang('パスワード入力フィールドの追加') ?>
                                                        <span class="glyphicon glyphicon-triangle-right">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                                              <path id="add_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M206.857-750.857H200v-2.286h6.857V-760h2.286v6.857H216v2.286h-6.857V-744h-2.286Z" transform="translate(-200 760)"/>
                                                            </svg>
                                                        </span>
                                                    </button>
                                                </a>
                                            <?php } ?>
                                        <?php } ?>
                                    <?php } ?>
                                </div>
                            <?php } ?>

                            <div class="input_row btn_row iblock input_box-new d-flex align-items-center">
                                <?php if (isset($event_info->status) && $event_info->status != "in_preparation") { ?>
                                    <button class="black-button small-button application-button form-end-button e_ajax_confirm d-flex align-items-center justify-content-between gap-2 text-14-16"
                                            type="button"
                                            data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに') ?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は') ?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。') ?>"
                                            data-button-class=";e_ajax_link"
                                            data-button="<?= my_lang('閉じる;実行') ?>"
                                            id="new_field_btn"
                                            data-button-url="#;<?php echo site_url('manager/form_item/add_text_view/' . (isset($event_id) ? $event_id : '')); ?>">
                                        <?= my_lang('挿入文の追加') ?>
                                        <span class="glyphicon">
                                            <?= $iconPlus ?>
                                        </span>
                                    </button>
                                <?php } else { ?>
                                    <a class="e_ajax_link new_event_btn iblock text-decoration-none w-100"
                                       href="<?php echo site_url('manager/form_item/add_text_view/' . (isset($event_id) ? $event_id : '')); ?>">
                                        <button class="black-button small-button application-button form-end-button fw-bold d-flex justify-content-between align-items-center gap-3 text-14-16">
                                            <?= my_lang('挿入文の追加') ?>
                                            <span class="glyphicon">
                                                <?= $iconPlus ?>
                                            </span>
                                        </button>
                                    </a>
                                <?php } ?>
                            </div>

                            <div class="input_row  btn_row e_btn_row input_box-new">
                                <?php if (isset($event_info->status) && $event_info->status != "in_preparation") { ?>
                                    <button class="black-button small-button application-button form-end-button e_ajax_confirm d-flex align-items-center justify-content-between gap-2 text-14-16"
                                            type="button"
                                            data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに') ?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は') ?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。') ?>"
                                            data-button-class=";e_ajax_link"
                                            data-button="<?= my_lang('閉じる;実行') ?>"
                                            id="new_field_btn"
                                            data-button-url="#;<?php echo site_url('manager/form_item/add_form_item_checkbox/' . (isset($event_id) ? $event_id : '')); ?>">
                                        <?= my_lang('チェックボックス型での選択項目の追加') ?>
                                        <span class="glyphicon">
                                            <?= $iconPlus ?>
                                        </span>
                                    </button>
                                <?php } else { ?>
                                    <a class="e_ajax_link new_event_btn iblock text-decoration-none w-100"
                                       href="<?php echo site_url('manager/form_item/add_form_item_checkbox/' . (isset($event_id) ? $event_id : '')); ?>"
                                       id="new_field_btn">
                                        <button class="black-button small-button application-button form-end-buttion fw-bold d-flex justify-content-between align-items-center gap-3 text-14-16">
                                            <?= my_lang('チェックボックス型での選択項目の追加') ?>
                                            <span class="glyphicon">
                                                <?= $iconPlus ?>
                                            </span>
                                        </button>
                                    </a>
                                <?php } ?>
                            </div>
                            <div class="input_row  btn_row iblock input_box-new d-flex align-items-center">
                                <?php if (isset($event_info->status) && $event_info->status != "in_preparation") { ?>
                                    <button class="black-button small-button application-button form-end-button e_ajax_confirm d-flex align-items-center justify-content-between gap-2 text-14-16"
                                            type="button"
                                            data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに') ?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は') ?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。') ?>"
                                            data-button-class=";e_ajax_link"
                                            data-button="<?= my_lang('閉じる;実行') ?>"
                                            id="new_field_btn"
                                            data-button-url="#;<?php echo site_url('manager/form_item/add_form/' . (isset($event_id) ? $event_id : '')); ?>">
                                        <?= my_lang('テキストフィールドの追加') ?>
                                        <span class="glyphicon">
                                            <?= $iconPlus ?>
                                        </span>
                                    </button>
                                <?php } else { ?>
                                    <a class="e_ajax_link new_event_btn iblock text-decoration-none w-100"
                                       href="<?php echo site_url('manager/form_item/add_form/' . (isset($event_id) ? $event_id : '')); ?>">
                                        <button class="black-button small-button application-button form-end-button fw-bold d-flex justify-content-between align-items-center gap-3 text-14-16">
                                            <?= my_lang('テキストフィールドの追加') ?>
                                            <span class="glyphicon">
                                                 <?= $iconPlus ?>
                                            </span>
                                        </button>
                                    </a>
                                <?php } ?>
                            </div>
                            <?php if ($show_button_add_custom_field_password) { ?>
                                <div class="input_row  btn_row iblock input_box-new d-flex align-items-center" id="add_password_button">
                                    <?php if (isset($event_info->status) && $event_info->status != "in_preparation") { ?>
                                        <button class="black-button small-button application-button e_ajax_confirm form-end-button d-flex align-items-center justify-content-between gap-2 text-14-16"
                                                type="button"
                                                data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに') ?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は') ?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。') ?>"
                                                data-button-class=";e_ajax_link"
                                                data-button="<?= my_lang('閉じる;実行') ?>"
                                                id="new_field_btn"
                                                data-button-url="#;<?php echo site_url('manager/form_item/add_form_item_password/' . (isset($event_id) ? $event_id : '')); ?>">
                                            <?= my_lang('パスワード入力フィールドの追加') ?>
                                            <span class="glyphicon ">
                                                 <?= $iconPlus ?>
                                            </span>
                                        </button>
                                    <?php } else { ?>
                                        <a class="e_ajax_link new_event_btn iblock text-decoration-none w-100"
                                           href="<?php echo site_url('manager/form_item/add_form_item_password/' . (isset($event_id) ? $event_id : '')); ?>">
                                            <button class="black-button small-button application-button form-end-button fw-bold d-flex justify-content-between align-items-center gap-3 text-14-16">
                                                <?= my_lang('パスワード入力フィールドの追加') ?>
                                                <span class="glyphicon">
                                                     <?= $iconPlus ?>
                                                </span>
                                            </button>
                                        </a>
                                    <?php } ?>
                                </div>
                            <?php } ?>
                            <div class="last_row">
                                <!-- <div>
                                <input id="no-need-all" type="checkbox"
                                       name="not_application_form" <?php //echo $view; ?>
                                       value="1" <?php //echo (isset($count_subscriber_form) && !$count_subscriber_form && ($page_draft != $draft_step)) ? 'checked' : '' ?>>
                                <label class="normal_text" <?php //echo $view ? '' : 'for="no-need-all"'; ?>>
                                    申し込みボタンのみ掲載</label><br>
                                <span class="tab_text">(予約申し込みフォームで上記お申込者情報は一切問わずに参加人数のみを把握したい場合など。)</span>
                            </div> -->
                            </div>
                            <?php if (!$view) { ?>
                                <div class="button-container application-main-button-container mt-lg-5">
                                    <div class="txt-bot text-14-16">
                                        ＊<?= my_lang('下記のボタンから<br class="d-xl-none">お申込者情報の事前確認が可能です。') ?>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-lg-center gap-4 box-item-2 justify-content-center">
                                        <?php if (isset($event_info->status) && $event_info->status != "in_preparation") { ?>
                                            <button class="btn-general btn-yellow e_ajax_confirm fw-medium px-4 fw-xl-normal w-xl-maxcontent min-w-202-246"
                                                    type="button"
                                                    data-msg="<?= my_lang('予約開始後のご変更となりますが、ユーザー様がすでに') ?><br><?= my_lang('ご閲覧、又はお申込みを完了されている場合は') ?>、<br><?= my_lang('トラブルの原因となりますので、十分にご注意願います。') ?>"
                                                    data-button-class=";submit_application_link"
                                                    data-button="<?= my_lang('閉じる;実行') ?>"
                                                    data-button-url="#;#" id="big_button">
                                                <?= my_lang('予約フォームの一時保存') ?>
                                            </span>
                                            </button>
                                        <?php } else { ?>
                                            <input type='hidden' value='false' name='checkStep'>
                                            <button type="submit" onclick="$('input[name=checkStep]').val(true)"
                                                    class="btn-general btn-yellow d-flex align-items-center justify-content-center mx-auto btn_confimation-reservation w-xl-maxcontent fw-medium min-w-202-246" id="big_button">
                                                <?= my_lang('予約フォームの一時保存') ?>
                                            </button>
                                        <?php } ?>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php echo $fee_form_html; ?>
        </form>
    </div>
    <div id="i_event_application_form_confirm" class="soft_hide reservation_application-box">
        <form class="e_ajax_submit" method="post" action="<?php echo isset($save_link) ? $save_link : "#" ?>">
            <input type="hidden" name="event_id" value="<?php echo isset($event_id) ? $event_id : 0; ?>">
            <div class="form_area">
                <div class="guide_text">
                    <?= my_lang('イベントの予約フォームは下記のとおりとなります。') ?> <?= my_lang('ご確認ください。') ?>
                </div>

                <div id="i_form_confirm" class="i_form_confirm_admin">
                    <?php echo $confirm; ?>
                </div>

            </div>
            <div class="button-container nav_button_container box-item-2 d-flex justify-content-center gap-3 w-100">
                <button type="button" id="i_application_event_back"
                        class="btn-general btn-black w-maxcontent ms-0 d-flex align-items-center justify-content-center gap-3 min-xl-w-200">
                     <span class="position-relative" style="top: -2px">
                        <svg xmlns="http://www.w3.org/2000/svg" width="9.867" height="16" viewBox="0 0 9.867 16">
                          <path id="chevron_right_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M323.733-712l6.133-6.133L328-720l-8,8,8,8,1.867-1.867Z" transform="translate(-320 720)" fill="#fff"/>
                        </svg>
                    </span>
                    <?= my_lang('戻る') ?>
                </button>
                <button type="submit" class="btn-general btn-yellow w-maxcontent ms-0 d-flex align-items-center justify-content-center gap-3 min-xl-w-200" id="ok_btn">
                    <?= my_lang('確定する') ?>
                </button>
            </div>
        </form>
    </div>
</div>
<div id="check_is_pay_cash_payment_url" style="display: none;"
     data-url="<?php echo site_url("/manager/event_edit/check_is_pay_cash_payment_url") ?>"></div>
<?php if (isset($scrollToFee) && $scrollToFee) { ?>
    <script type="text/javascript">
        $(document).ready(function () {
            document.getElementById("i_free_form").scrollIntoView();
        });
    </script>
<?php } ?>
<?php if (isset($default_checked_field) && $default_checked_field) { ?>
    <script type="text/javascript">
        $(document).ready(function () {
            // Default checked 6 field in screen manager/event/application_form/
            $('.e_change_field_required').each(function () {
                let input = $(this);
                if (input.data('field-name') == 'first_name' ||
                    input.data('field-name') == 'email' ||
                    input.attr('id') == 'show_on_friend_form1' ||
                    input.attr('id') == 'show_on_friend_form20' ||
                    input.attr('id') == 'require1' ||
                    input.attr('id') == 'require20') {
                    input.prop('checked', true);
                    $(this).parents(".input_row").find("input[name='field_id[]']").each(function () {
                        $(this).siblings("textarea[name='default_value[]']").removeAttr("disabled");
                        $(this).siblings("input[type='hidden']").removeAttr("disabled");
                        if ($(this).parents(".input_row").find("input.e_field_required").is(":checked")) {
                            $(this).siblings("input[name='required[]']").removeAttr("disabled");
                        } else {
                            $(this).siblings("input[name='required[]']").attr("disabled", "true");
                        }

                        if ($(this).parents(".input_row").find("input.e_field_show_on_friend_form").is(":checked")) {
                            $(this).siblings("input[name='show_on_friend_form[]']").removeAttr("disabled");
                        } else {
                            $(this).siblings("input[name='show_on_friend_form[]']").attr("disabled", "true");
                        }
                    })
                }
            })
        });
    </script>
<?php } ?>

