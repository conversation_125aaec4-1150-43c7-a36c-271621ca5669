<div class="step_nav_container">
    <div class="guide_holder">
        <div class="nav__guide-holder--content-bold">
            <div class="guide_holder_content">
                <?php if (isset($notify_draft) && $notify_draft) {
                    echo $notify_draft;
                } elseif ($this->session->userdata('is_first_login')) { ?>
                    <?= my_lang('初めてのアクセスありがとうございます。')?><br>
                    <?= my_lang('まずは、サンプルイベントを自由に変更して、テストしてみてください。')?><br>
                    <?= my_lang('下記の「過去のイベントを複写」するボタンで一度作ったイベントをテンプレートのように複写し簡単に新規イベントを設定することができます。')?>
                <?php } else {
                    ?>
                    <?= my_lang('下記のステップで新規イベントをご登録ください。 内容の変更及び、必須項目以外は登録後にもご入力いただけます。')?>
                    <?= my_lang('「過去のイベントを複写」して追加する場合は、ワンステップで簡単にイベントを追加し編集することができます。')?>
                <?php } ?>
            </div>
        </div>
    </div>

    <div class="step-parents base__header--top">
        <div class="d-flex flex-wrap <?php echo (isset($notify_draft) ? '' : 'custom_step') ?>">
            <?php foreach ($list_step as $key => $step_text) { ?>
                <div class="col-6 col-lg-3 px-0 mb-3 mb-lg-0">
                    <?php
                    if (($key > 1) && (($step >= $key && $step != 2 && !empty($event_id)) || (!empty($draft_step) && $step != 2 && $draft_step >= $key))) { ?>
                        <a href="<?php echo site_url($step_link[$key] . $event_id . (isset($notify_draft) ? '?is_draft=1' : '')) ?>">
                            <div class="base__header--top-child item-step align-items-center <?php echo $step == $key ? "focus_step" : "" ?> ">
                                <?php echo my_lang($step_text) ?>
                            </div>
                        </a>
                    <?php } else { ?>
                        <div class="base__header--top-child item-step align-items-center <?php echo $step == $key ? "focus_step" : "" ?> ">
                            <?php echo my_lang($step_text) ?>
                        </div>
                    <?php } ?>
                </div>
            <?php } ?>
        </div>
    </div>
    <?php if (($step >= 5 && !empty($event_id)) || (!empty($draft_step) && $draft_step >= 5)) { ?>
    <div id="form-top-content" style="display: flex; align-items: center; justify-content: flex-end;">
        <div class="pull-left step_body top-guide">
            <div class="guide-line"></div>
            <span><?= my_lang('さらにイベントTOPで詳細設定ができます。')?></span>
            <div class="guide-line"></div><span class="glyphicon glyphicon-play guide-line-head"></span>
        </div>
        <a href="<?php echo site_url('manager/event_detail/index_top/' . $event_id) ?>">
            <div class="black-button small-button step-button to-event-top-button">
                <?= my_lang('イベントTop')?>
                <span class="glyphicon glyphicon-triangle-right"></span>
            </div>
        </a>
    </div>
    <?php } ?>
    <div class="clearfix"></div>
</div>