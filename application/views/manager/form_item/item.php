<div class="modal-dialog manager-modal medium-modal">
    <div class="modal-content event-edit-item-modal">
        <form class="form-horizontal e_ajax_submit manager-form"
              enctype="multipart/form-data"
              method="POST" role="form" action="<?php echo isset($save_link) ? $save_link : "#" ?>">
            <div class="modal-header no-padding">
                    <span type="button" class="close glyphicon glyphicon-remove" data-dismiss="modal"
                          aria-label="Close"></span>
            </div>
            <div class="modal-body">
                <h3><?= my_lang('個人情報の取り扱いについて')?> </h3>
                <?php if (!isset($type)) { ?>
                    <textarea name="default_value"
                              class="textarea-default-value"><?php echo $default_value ?></textarea>
                    <div class="button-container add-form-button-container">
                        <button type="submit" class="black-button small-button">
                            <?= my_lang('追 加')?>
                            <span class="glyphicon glyphicon-triangle-right"></span>
                        </button>
                    </div>
                <?php } else { ?>
                    <div class="preview_rules_container">
                        <?php echo $default_value; ?>
                    </div>
                    <div class="read_rules">
                        <input type="checkbox" value="1"
                               id="i_read_rules"/>
                        <label class="control-label" for="i_read_rules"
                               title="<?= my_lang('「個人情報の取扱いについて」に同意する')?>">
                            <?= my_lang('「個人情報の取扱いについて」に同意する')?>
                        </label>
                    </div>
                <?php } ?>

            </div>
        </form>
    </div>
</div>