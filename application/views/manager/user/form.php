<div class="add-form-title"><span> <?php echo $user_info ? my_lang('新規ご担当者の編集') : my_lang('新規ご担当者の追加') ?></span></div>
<div class="add-form-content px-0">
    <div class="form-group">
        <label class="label-title text-14-16"><?= my_lang('所属部署')?></label>
        <div class="add-user-input-container">
            <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="department"
                   value="<?php echo ($user_info && $user_info->department) ? $user_info->department : '' ?>">
        </div>
    </div>
    <div class="form-group">
        <div class="d-flex gap-3">
            <div class="d-flex flex-column gap-3 w-xl-50">
                <label class="label-title text-14-16 mb-0"><?= my_lang('氏')?></label>
                <div class="add-user-input-container">
                    <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="first_name"
                           value="<?php echo ($user_info && $user_info->first_name) ? $user_info->first_name : '' ?>">
                </div>
            </div>
            <div class="d-flex flex-column gap-3 w-xl-50">
                <label class="label-title text-14-16 right-title mb-0"><?= my_lang('名')?></label>
                <div class="add-user-input-container">
                    <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="last_name"
                           value="<?php echo ($user_info && $user_info->last_name) ? $user_info->last_name : '' ?>">
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="d-flex gap-3">
            <div class="d-flex flex-column gap-3 w-xl-50">
                <label class="label-title text-14-16 mb-0"><?= my_lang('氏フリガナ')?></label>
                <div class="add-user-input-container">
                    <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="first_name_furi"
                           value="<?php echo ($user_info && $user_info->first_name_furi) ? $user_info->first_name_furi : '' ?>">
                </div>
            </div>
            <div class="d-flex flex-column gap-3 w-xl-50">
                <label class="label-title text-14-16 right-title m-0"><?= my_lang('名フリガナ')?></label>
                <div class="add-user-input-container">
                    <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="last_name_furi"
                           value="<?php echo ($user_info && $user_info->last_name_furi) ? $user_info->last_name_furi : '' ?>">
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="label-title text-14-16">
            <?= my_lang('電話番号')?>
            <span class="text-12-14 text-666 fw-normal">（<?= my_lang('ハイフンは含みません')?>）</span>
        </label>
        <div class="add-user-input-container only_w-modalinput">
            <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="phone"
                   value="<?php echo ($user_info && $user_info->phone) ? $user_info->phone : '' ?>">
        </div>
    </div>
    <div class="form-group">
        <label class="label-title text-14-16"><?= my_lang('メールアドレス')?></label>
        <div class="add-user-input-container">
            <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="email"
                   value="<?php echo ($user_info && $user_info->email) ? $user_info->email : '' ?>">
        </div>
    </div>
    <div class="form-group">
        <label class="label-title text-14-16"><?= my_lang('再確認メールアドレス')?></label>
        <div class="add-user-input-container">
            <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" name="email_conf"
                   value="<?php echo ($user_info && $user_info->email) ? $user_info->email : '' ?>">
        </div>
    </div>
    <?php if (isset($form_type) && $form_type == 'edit') {?>
        <div class="form-group">
            <label class="label-title text-14-16"><?= my_lang('現在のパスワード')?></label>
            <div class="add-user-input-container only_w-modalinput">
                <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" type="password" name="current_password"
                       value="">
            </div>
        </div>
    <?php } ?>
    <div class="form-group">
        <label class="label-title text-14-16"><?= my_lang('新パスワード')?></label>
        <div class="d-flex flex-column align-items-xl-center flex-xl-row gap-3">
            <div class="add-user-input-container only_w-modalinput">
                <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" type="password" name="password"
                       value="">
            </div>
            <div class="add-user-input-container">
                <div class="input-notice txt-content pt-0">
                    <?= my_lang('6桁から20桁の半角英数字大文字小文字で区別。')?>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="label-title text-14-16"><?= my_lang('新パスワード再確認')?></label>
        <div class="add-user-input-container only_w-modalinput">
            <input class="add-user-input input_box-new text-14-16 w-100 bg-F5F5F5" type="password" name="password_conf"
                   value="">
        </div>
    </div>

    <div class="form-group">
        <label class="label-title text-14-16"><?= my_lang('権限')?></label>
        <div class="add-user-input-container">
            <div class="d-flex flex-column gap-2 mb-4">
                <div class="radio p-0">
                    <input type="radio" class="user_list_input user_list_input_large" name="role_id" id="role_id1"
                           value="1" <?php echo ($user_info && $user_info->role_id == 1) ? 'checked' : '' ?>>
                    <label for="role_id1" class="radio-label d-flex align-items-center gap-2 p-0 text-14-18 fw-bold"><?= my_lang('管理者【全権限】')?></label>
                </div>
                <div class="radio p-0">
                    <input type="radio" class="user_list_input user_list_input_large" name="role_id" id="role_id2"
                           value="2" <?php echo ($user_info && $user_info->role_id == 2) ? 'checked' : '' ?>>
                    <label for="role_id2" class="radio-label d-flex align-items-center gap-2 p-0 text-14-18 fw-bold"><?= my_lang('編集者【受付及びイベント作成・編集】')?></label>
                </div>
                <div class="radio p-0">
                    <input type="radio" class="user_list_input user_list_input_large" name="role_id" id="role_id3"
                           value="3" <?php echo ($user_info && $user_info->role_id == 3) ? 'checked' : '' ?>>
                    <label for="role_id3" class="radio-label d-flex align-items-center gap-2 p-0 text-14-18 fw-bold"><?= my_lang('閲覧者【受付及びイベント情報閲覧】')?></label>
                </div>
            </div>
            <div class="text-FD6600 text-14-16 text-center fw-bold">＊<?= my_lang('アプリは権限による利用制限はございません。')?></div>
        </div>
    </div>

    <div class="button-container user-button-container box-item-2 d-flex justify-content-center m-0">
        <button type="submit" class="btn-general btn-yellow ms-0 d-flex align-items-center justify-content-center text-center w-maxcontent only_wxl-modalinput">
            <?php echo $user_info ? my_lang('更新する') : my_lang('確認する') ?>
        </button>
    </div>
</div>