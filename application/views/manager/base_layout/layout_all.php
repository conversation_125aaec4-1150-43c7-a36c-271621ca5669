<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><?php echo ((isset($title) && $title) ? ($title . ' | ') : '') . (isset($site_name_plus) ? $site_name_plus : ''); ?></title>
    <meta name="description" content="<?php echo $description; ?>"/>
    <meta name="keywords" content="<?php echo $keywords; ?>"/>
    <link rel="canonical" href="<?php echo $canonical; ?>"/>
    <link type="image/x-icon" href="<?php echo $favicon; ?>" rel="shortcut icon"/>
    <script>
        const translate = <?php echo json_encode($this->lang->language); ?>;
    </script>
    <?php echo $assets_header; ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=AW-1003509414"></script>
    <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-1003509414'); </script>
    <style>
        <?php if($show_tip == 0) { ?>
            .tips {
                visibility: hidden;
            }
            .over_mark_admin {
                visibility: hidden;
            }
        <?php } else { ?>
            .tips {
                visibility: visible;
            }
            .over_mark_admin {
                visibility: visible;
            }
        <?php } ?>
    </style>
</head>
<body class="no-skin ace-custom" data-barrier="<?php echo $json_barrier; ?>">
<?php $this->load->view('partials/chatbot'); ?>
<?php echo $content; ?>
<?php echo $assets_footer; ?>
</body>
</html>