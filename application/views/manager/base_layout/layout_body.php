<!-- top_bar -->
<div>
    <?php echo $top_bar; ?>
</div>
<!-- /top_bar -->
<!-- Page container -->
<?php
    if (strpos($_SERVER['REQUEST_URI'], 'manager/event_list') !== false) {
        $prop_container = '';
    } else {
        $prop_container = 'container';
    }
?>
<div class="main-container body-container <?= $prop_container ?>" id="main-container">
    <?php echo $content; ?>
</div>
<div>
    <?php echo $footer; ?>
</div>
<div id="modals-container"></div>
<!-- /container -->
<!-- Loading -->
<div class="message-loading-overlay customer-overlay" id="loading-overlay" style="display: none;">
    <i class="fa-spin ace-icon fa fa-refresh big-spin"></i>
</div>
<!-- /Loading -->
<!-- Confirm modal -->
<div class="modal-dialog manager-modal notification_modal e_confirm_modal soft_hide" id="confirm-modal-template">
    <div class="modal-content">
        <div class="modal-header no-padding header-modal">
            <span type="button" data-dismiss="modal" class="close" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                  <path id="close_24dp_5F6368_FILL0_wght400_GRAD0_opsz24" d="M201.6-744l-1.6-1.6,6.4-6.4-6.4-6.4,1.6-1.6,6.4,6.4,6.4-6.4,1.6,1.6-6.4,6.4,6.4,6.4-1.6,1.6-6.4-6.4Z" transform="translate(-200 760)"/>
                </svg>
            </span>
        </div>
        <div class="modal-body">
            <div class="notification_popup_container">
                <div class="e_confirm_text">msg</div>
                <div class="button-container e_confirm_button_container">
                    <a class="black-button big-button soft_hide e_confirm_button_link"
                       data-dismiss="modal"
                       aria-label="Close" href="#">
                        <span class="e_confirm_button_text">button-text</span>
<!--                        <span class="glyphicon glyphicon-triangle-right"></span>-->
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /Confirm modal -->