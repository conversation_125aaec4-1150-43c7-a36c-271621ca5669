<div class="container site-map-container">
    <div class="title"><?= my_lang('サイトマップ')?></div>
    <div class="site-map-list kc">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('service') ?>"><?= my_lang('サービス概要')?></a>
            </div>
            <div class="text-name-content iblock"></div>
        </div>
        <div class="site-map-description">
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('ご利用シーン')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('各種多様なイベントで使用できる、電子チケット受付システム')?>
                </div>
            </div>
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('主催者の条件')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('特別な環境や機器に依存せずに受付システムを利用できる')?>
                </div>
            </div>
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('主催者側のメリット')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('簡単便利なチケット発券から受付まで多数のメリットがございます')?>
                </div>
            </div>
        </div>
    </div>
    <div class="site-map-list kc-list">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('fee'); ?>"><?= my_lang('ご利用料金')?></a>
            </div>
            <div class="text-name-content iblock"><?= my_lang('イベント設定料、利用料を掲載')?></div>
        </div>
        <div class="site-map-description">
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('お支払方法')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('月末締めでご請求書を発行')?>
                </div>
            </div>
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('オプション')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('設定おまかせプランやクリエイティブワークをご提供')?>
                </div>
            </div>
        </div>
    </div>
    <div class="site-map-list kc-list">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('feature'); ?>"><?= my_lang('機能一覧')?></a>
            </div>
            <div class="text-name-content iblock"><?= my_lang('主催者の情報画面')?></div>
        </div>
        <div class="site-map-description kc-list">
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('受付アプリ')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('QRコードリーダー機能でスマホで受付。 たとえアプリが機能しない事態にも対応 イベント登録・受付管理画')?>
                </div>
            </div>
            <div class="text-description">
                <div class="text-name-site iblock">
                    <?= my_lang('主催者専用アカウント')?>
                </div>
                <div class="text-content iblock">
                    <?= my_lang('イベント登録・受付管理画面')?>
                </div>
            </div>
        </div>
    </div>
    <div class="site-map-list kc-list">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('flow'); ?>"><?= my_lang('運用の流れ')?></a>
            </div>
            <div class="text-name-content iblock"><?= my_lang('イベントの登録から電子チケットの発券・受付までの手順を説明')?></div>
        </div>
    </div>
    <div class="site-map-list kc-list1">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('faq'); ?>"><?= my_lang('よくあるご質問')?></a>
            </div>
            <div class="text-name-content iblock"><?= my_lang('事前予約・当日受付・主催者専用アカウントに関するQ＆A')?></div>
        </div>
    </div>
    <div class="site-map-list kc-list1">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('company_information'); ?>"><?= my_lang('運営会社')?></a>
            </div>
            <div class="text-name-content iblock"></div>
        </div>
    </div>
    <div class="site-map-list kc-list1">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('term_of_service'); ?>"><?= my_lang('ご利用規約')?></a>
            </div>
            <div class="text-name-content iblock"></div>
        </div>
    </div>
    <div class="site-map-list kc-list1">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('requirement'); ?>"><?= my_lang('推奨動作環境')?></a>
            </div>
            <div class="text-name-content iblock"></div>
        </div>
    </div>
    <div class="site-map-list kc-list1">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo site_url('privacy'); ?>"><?= my_lang('個人情報保護方針')?></a>
            </div>
            <div class="text-name-content iblock"></div>
        </div>
    </div>
    <div class="site-map-list kc-list1">
        <div class="site-map-box">
            <span class="triangle iblock"></span>
            <div class="text-name iblock">
                <a href="<?php echo isset($promoter_site_url) ? $promoter_site_url . '/login?forget_password=true' : '#'; ?>"><?= my_lang('ログイン情報お忘れの場合')?></a>
            </div>
            <div class="text-name-content iblock"></div>
        </div>
    </div>
</div>