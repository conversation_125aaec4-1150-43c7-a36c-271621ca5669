<div class="bannerPc">
    <img src="<?php echo base_url("assets/images/flippa_operation_PC.png"); ?>">
    <div class="wrapTextBanner">
        <?= my_lang('Operation Flow') ?>
    </div>
</div>
<div class="bannerSp">
    <img src="<?php echo base_url("assets/images/flippa_operation_SP.jpg"); ?>">
</div>
<div class="container">
    <div class="wf-content-box img-content">
        <img src="<?php echo base_url("assets/images/flow_img.jpg"); ?>">
    </div>
    <div class="title">
        <span>
            <?= my_lang('スマホ受付の注意事項') ?>
        </span>
        <span class="title-small">（<?= my_lang('専用アプリご利用の場合')?>）</span>
    </div>
    <div class="list">
        <div class="number">
            <span>
                1
            </span>
        </div>
        <span class="screemPc">
            <?= my_lang('受付するスマホの充電確認')?>（<?= my_lang('会場でのコンセントの有無')?>）
        </span>
        <span class="screemMb">
            <?= my_lang('受付するスマホの充電確認')?><br>(<?= my_lang('会場でのコンセントの有無')?>）
        </span>
    </div>
    <div class="list">
        <div class="number">
            <span>
                2
            </span>
        </div>
        <div class="list-body">
            <span class="screemPc">
                <?= my_lang('受付場所の通信環境の確認')?>（<?= my_lang('WIFIもしくはモバイル通信')?>）
            </span><br>
            <span class="screemMb">
                 <?= my_lang('受付場所の通信環境の確認')?><br>（<?= my_lang('WIFIもしくはモバイル通信')?>）
            </span><br>
            <span>＊<?= my_lang('イベント会場の共有WIFIは混雑状況により通信障害になる可能性がございます。モバイル通信に切り替えるか専用のWIFIルーターをご用意ください。')?></span>
        </div>
    </div>
    <div class="list">
        <div class="number">
            <span>
                3
            </span>
        </div>
        <span class="screemPc">
            <?=  my_lang('受付場所の照明の確認')?> <?= my_lang('（QRコードが読める明るさかどうか）') ?>
        </span>
        <span class="screemMb">
            <?=  my_lang('受付場所の照明の確認')?> <br><?= my_lang('（QRコードが読める明るさかどうか）') ?>
        </span>
    </div>
    <div class="list">
        <div class="number">
            <span>
                4
            </span>
        </div>
        <div class="wrapText">
            <span class="screemPc">
                <?= my_lang('受付アプリの起動日時の設定確認')?>（<?= my_lang('主催者アカウントにログイン＞イベント一覧＞イベント情報')?>）
            </span>
            <span class="screemMb">
                <?= my_lang('受付アプリの起動日時の設定確認')?><br>(<?= my_lang('主催者アカウントにログイン＞イベント一覧＞イベント情報')?>）
            </span>
        </div>
    </div>
    <div class="list">
        <div class="number">
            <span>
                5
            </span>
        </div>
        <span class="screemPc">
            <?= my_lang('万が一に備えてリストを印刷しておく。')?>
        </span>
        <span class="screemMb">
            <?= my_lang('万が一に備えてリストを印刷しておく。')?>
        </span>
    </div>

    <div class="example">
        <div class="title">
            <p class="introduce">
                <?= my_lang('発券事例集')?>
            </p>
            <p>
                Example of ticketing
            </p>
        </div>

        <div class="wrapText">
            <div class="dot">
                ●
            </div>
            <div>
                <span class="smart">
                    <?= my_lang('イベント種別に関わらず複数の条件下でのチケット発券を以下に想定しましたので、ご参考ください')?><br><?= my_lang('イベントの条件に合わせてカスタマイズも可能ですので、お気軽にご相談ください。')?>
                </span>
            </div>
        </div>
    </div>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('無料イベントを開催')?></th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('自動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">
                <span class="red">
                    <?= my_lang('無料チケット')?>
                </span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約開始→予約状況管理→コンファームメール配信→当日受け付け→アンケート結果→お礼のメール配信')?></td>
        </tr>
    </table>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('有料イベントを開催(現金決済を採用する場合、自動発券が必須)')?></th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('自動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('決済方法')?></td>
            <td class="tdRight"><?= my_lang('クレジットカード、コンビニ、')?><span class="red"><?= my_lang('銀行振り込み')?></span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">
                <span class="red">
                    <?= my_lang('有料チケット')?>
                </span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約開始→予約・決済状況管理→コンファームメール配信→当日受け付け')?>→<span class="red"><?= my_lang('現金の場合は領収機能で処理')?></span>→<?= my_lang('アンケート結果→お礼のメール配信→売上金は月末締め翌末払いで御社口座に振り込まれます。')?></td>
        </tr>
    </table>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('有料イベントを開催。在庫数を超過する申込みが予想されるので、')?>
                <span class="red"><?= my_lang('当選者を抽選。')?></span>(<?= my_lang('銀行振り込み決済を採用する場合、手動発券が必須。')?>)
            </th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('自動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('決済方法')?></td>
            <td class="tdRight"><?= my_lang('クレジットカード、コンビニ、')?><span class="red"><?= my_lang('現金')?></span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">

                <?= my_lang('有料チケット')?>
                </span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約開始→予約・決済状況管理→コンファームメール配信→仮予約終了')?>→<span class="red"><?= my_lang('抽選')?></span>→<?= my_lang('本申込(決済)→当日受け付け')?>→<span class="red"><?= my_lang('銀行振込の場合は申し込み一覧で消込処理')?></span>→<?= my_lang('アンケート結果→お礼のメール配信→売上金は月末 締め翌末払いで御社口座に振り込まれます。')?>
            </td>
        </tr>
    </table>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('有料イベントを開催。在庫数を超過する申込みが予想されるので、当選者を抽選')?><span class="red"><?= my_lang('受付時に写真付き名札をつけてもらう')?></span>
            </th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('手動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('決済方法')?></td>
            <td class="tdRight"><?= my_lang('クレジットカード、コンビニ、銀行振り込み')?>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">
                <?= my_lang('有料チケット')?>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約フォーム設定→名札設定→予約開始→予約・決済状況管理→コンファームメール配信→仮予約終了→ 抽選→本申込(決済)')?>→<span class="red"><?= my_lang('名札印刷')?></span>→<?= my_lang('当日受け付け')?>→<span class="red"><?= my_lang('名札を手渡す')?></span>→<?= my_lang('アンケート結果→お礼のメール配信→売上金は月末締め翌末払いで御社口座に振り込まれます。')?>
            </td>
        </tr>
    </table>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('無料セミナーを開催。VIPの参加者にはチケットを印刷して郵送し')?><span class="red"><?= my_lang('紙チケットで受付。')?></span>
            </th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('手動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">
                <span class="red">
                    <?= my_lang('無料チケット')?>
                </span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約開始→予約状況管理→コンファームメール配信→仮予約終了→抽選→QRチケット一括印刷')?>→
                <span class="red">
                    <?= my_lang('紙チケットの版下制作')?>
                </span>→<span class="red"><?= my_lang('印刷')?></span>→<span class="red"><?= my_lang('郵送')?></span>→<?= my_lang('当日受け付け→アンケート結果→お礼のメール配信*青文字部分は、システム外での運用となります。')?>
            </td>
        </tr>
    </table>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('有料イベントを開催。在庫数を超過する申込みが予想されるので、当選者を抽選し会場は')?>
                <span class="red"><?= my_lang('座席指定')?></span><?= my_lang('とする。')?>
            </th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('手動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('決済方法')?></td>
            <td class="tdRight"><?= my_lang('クレジットカード、コンビニ、銀行振り込み')?>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">
                <span class="red">
                    <?= my_lang('有料チケット')?>
                </span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約開始→予約・決済状況管理→コンファームメール配信→仮予約終了→エクセルファイルダウンロード')?>→<span class="red"><?= my_lang('抽選・座席指定')?></span>→
                <?= my_lang('エクセルファイルアップロード→当選メール送信→本申込(決済)→当日受け付け→アンケート結果→お礼のメール配信→売上金は月末締め翌末払いで御社口座に振り込まれます。')?>
            </td>
        </tr>
    </table>
    <table class="table">
        <tr>
            <th class="left"><?= my_lang('条件')?></th>
            <th class="right"><?= my_lang('有料イベントを開催。在庫数を超過する申込みが予想されるので、当選者を抽選し会場は座席指定とする。さらに、')?>
                <span class="red"><?= my_lang('チケットとグッツをセットで受付。')?></span>
            </th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdRight"><?= my_lang('手動発券')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('決済方法')?></td>
            <td class="tdRight"><?= my_lang('クレジットカード、コンビニ、銀行振り込み')?>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('料金設定')?></td>
            <td class="tdRight">
                <span class="red">
                    <?= my_lang('有料チケット')?>
                </span>
            </td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('管理方法')?></td>
            <td class="tdRight"><?= my_lang('予約フォーム設定')?>→
                <span class="red">
                    <?= my_lang('料金1でチケット設定・料金2でグッツの設定')?>
                </span>→<?= my_lang('予約開始→予約・決済状況管理→コンファームメール配信→仮予約終了→エクセルファイルダウンロード→抽選・座席指定→エクセルファイルアップロード→当選メール送信→本申込(決済)→グッツの準備→当日受け付け')?>→
                <span class="red">
                    <?= my_lang('グッツを手渡す')?>
                </span>
                →<?= my_lang('アンケート結果→お礼のメール配信→売上金は月末締め翌末払いで御社口座に振り込まれます。')?>
            </td>
        </tr>
    </table>
    <div class="example">
        <div class="title">
            <p id="condition">
                <?= my_lang('発券方法による条件表')?>
            </p>
        </div>
    </div>
    <table class="table" id="forPc">
        <tr>
            <th class="left"><?= my_lang('発券方法')?></th>
            <th class="thTeble"><?= my_lang('利用可能な決済方法')?></th>
            <th class="thTeble"><?= my_lang('在庫管理')?></th>
            <th class="thTeble"><?= my_lang('一人申し込み上限設定')?></th>
            <th class="thTeble"><?= my_lang('抽選')?></th>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('自動発券')?></td>
            <td class="tdTeble"><?= my_lang('クレジットカード、コンビニ、銀行振込、現金')?></td>
            <td class="tdTeble"> ○</td>
            <td class="tdTeble"> ○</td>
            <td class="tdTeble">X</td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('手動発券')?></td>
            <td class="tdTeble"><?= my_lang('クレジットカード、コンビニ、銀行振込')?></td>
            <td class="tdTeble">X(<?= my_lang('自動では不可')?>)</td>
            <td class="tdTeble"> ○</td>
            <td class="tdTeble"> ○</td>
        </tr>
    </table>
    <table class="table" id="smartPhone">
        <tr>
            <th class="left"><?= my_lang('発券方法')?></th>
            <th class="thTeble"><?= my_lang('自動発券')?></th>
            <th class="thTeble"><?= my_lang('手動発券')?></th>
        </tr>
        <tr>
            <th class="thTeble"><?= my_lang('利用可能な決済方法')?></th>
            <td class="tdTeble"><?= my_lang('クレジットカード、コンビニ、銀行振込、現金')?></td>
            <td class="tdTeble"><?= my_lang('クレジットカード、コンビニ、銀行振込')?></td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('在庫管理')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">X(<?= my_lang('自動では不可')?>)</td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('一人申し込み上限設定')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">○</td>
        </tr>
        <tr>
            <td class="tdLeft"><?= my_lang('抽選')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
        </tr>
    </table>

    <div class="example">
        <div class="title">
            <p id="condition2"><?= my_lang('メール送信条件表')?></p>
        </div>
        <div>
            <?= my_lang('○=自動送信　▲ 手動送信　X=未送信')?>
        </div>
    </div>
    <table class="table" id="forPc2">
        <tr>
            <th class="thTeble" width="10%" rowspan="3"><?= my_lang('発券方法')?></th>
        </tr>
        <tr>
            <th class="thTeble"><?= my_lang('決済方法')?></th>
            <th class="thTeble">①<br><?= my_lang('お申し込み確認メール')?></th>
            <th class="thTeble">②<br><?= my_lang('当選通知メール')?></th>
            <th class="thTeble">③<br><?= my_lang('登録内容のご確認メール')?></th>
            <th class="thTeble">④<br><?= my_lang('支払方法のお知らせメール')?></th>
            <th class="thTeble">⑤<br><?= my_lang('決済完了メール')?></th>
            <th class="thTeble">⑥<br><?= my_lang('お申し込み完了メール')?><br>（<?= my_lang('電子チケット添付')?>）</th>
        </tr>
        <tr>
            <th class="thTeble"><?= my_lang('宛先')?></th>
            <th class="thTeble"><?= my_lang('申込者')?></th>
            <th class="thTeble"><?= my_lang('申込者')?></th>
            <th class="thTeble"><?= my_lang('申込者')?></th>
            <th class="thTeble"><?= my_lang('申込者')?></th>
            <th class="thTeble"><?= my_lang('申込者')?></th>
            <th class="thTeble"><?= my_lang('申込者・同伴者')?></th>
        </tr>
        <tr>
            <td class="tdLeft" rowspan="6"><?= my_lang('自動発券')?></td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('無料')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('クレジットカード')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('コンビニ')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
            <td class="tdTeble"><?= my_lang('○コンビニ別支払い方法')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('銀行振込')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">○<?= my_lang('振込みのご案内')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('現金')?></td>
            <td class="tdTeble">○<?= my_lang('支払用チケット添付')?></td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○<?= my_lang('領収メール申込者のみ')?></td>
        </tr>
        <tr>
            <td class="tdLeft" rowspan="5"><?= my_lang('手動発券')?></td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('無料')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">▲​</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('クレジットカード')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">X</td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲​</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('コンビニ')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲</td>
            <td class="tdTeble">○</td>
            <td class="tdTeble"><?= my_lang('○コンビニ別支払い方法')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲​</td>
        </tr>
        <tr>
            <td class="tdTeble"><?= my_lang('銀行振込')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲</td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">○<?= my_lang('振込みのご案内')?></td>
            <td class="tdTeble">○</td>
            <td class="tdTeble">▲​</td>
        </tr>
    </table>

    <div style="width: 100%; overflow-x: auto">
        <table class="table smartPhone2Custom" id="smartPhone2">
            <tr>
                <th class="tdLeft"><?= my_lang('発券方法')?></th>
                <th class="thTeble text-center" colspan="5"><?= my_lang('自動発券')?></th>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('決済方法')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('無料')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('クレジットカード')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('コンビニ')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('銀行振込')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('現金')?></td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('宛先')?></td>
            </tr>
            <tr>
                <td class="tdLeft">①<?= my_lang('お申し込み')?><br><?= my_lang('確認メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">○<?= my_lang('支払用チケット添付')?></td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">②<?= my_lang('当選通知')?><br><?= my_lang('メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">③<?= my_lang('登録内容の')?><br><?= my_lang('ご確認メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○<?= my_lang('お振込情報')?></td>
                <td class="tdTeble" rowspan="2">X</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">④<?= my_lang('支払方法のお知らせメール')?></td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">○<?= my_lang('コンビ<br>ニ別支払<br>い方法')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">⑤<?= my_lang('決済完了メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">⑥<?= my_lang('お申し込み完<br>了メール（電子<br>チケット添付）')?></td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○<?= my_lang('領収メ')?><br><?= my_lang('ール申込')?><br><?= my_lang('者のみ')?></td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者・同伴者')?></td>
            </tr>

            <tr>
                <th class="tdLeft"><?= my_lang('発券方法')?></th>
                <th class="thTeble text-center" colspan="4"><?= my_lang('手動発券')?></th>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('決済方法')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('無料')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('クレジットカード')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('コンビニ')?></td>
                <td class="tdTeble" rowspan="2"><?= my_lang('現金')?></td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('宛先')?></td>
            </tr>
            <tr>
                <td class="tdLeft">①<?= my_lang('お申し込み')?><br><?= my_lang('確認メール')?></td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">②<?= my_lang('当選通知')?><br><?= my_lang('メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">▲</td>
                <td class="tdTeble" rowspan="2">▲</td>
                <td class="tdTeble" rowspan="2">▲</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">③<?= my_lang('登録内容の')?><br><?= my_lang('ご確認メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">④<?= my_lang('支払方法のお知らせメール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">○<?= my_lang('コンビ<br>ニ別支払<br>い方法')?></td>
                <td class="tdTeble" rowspan="2">○<?= my_lang('お振り')?><br><?= my_lang('込み情報')?></td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">⑤<?= my_lang('決済完了メール')?></td>
                <td class="tdTeble" rowspan="2">X</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者')?></td>
            </tr>
            <tr>
                <td class="tdLeft">⑥<?= my_lang('お申し込み完<br>了メール（電子<br>チケット添付）')?></td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
                <td class="tdTeble" rowspan="2">○</td>
            </tr>
            <tr>
                <td class="tdLeft"><?= my_lang('申込者・同伴者')?></td>
            </tr>
        </table>
    </div>

    <div class="buttonFlow">
        <button class="register-button no-outline e_ajax_link no-padding-button" href="<?php echo site_url("register/temp_form_get"); ?>">
            <?= my_lang('主催者専用アカウント新規登録')?>
        </button>
    </div>
</div>