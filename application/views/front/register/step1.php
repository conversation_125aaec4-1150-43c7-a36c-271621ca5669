<?php echo $header_step; ?>
<div class="regist-body">
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('法人登記名')?>: <br>
            <?= my_lang('無登記の場合屋号又は個人名')?></label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-long" name="company_name">
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('フリガナ')?>:</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input regist-input-medium" name="company_name_furi">
            <span class="noti_text"><?= my_lang('スペースは無しでご記入ください')?></span>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold"><?= my_lang('所属部署')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input regist-input-medium" name="department">
            <span class="noti_text"><?= my_lang('任意となります')?></span>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('郵便番号')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-short" name="zip_code">
            <button type="button" class="black-button address-button"
                    onclick="AjaxZip3.zip2addr( 'zip_code', '', 'address1', 'address2', '', 'address3' );">
                <?= my_lang('住所の転記')?>
                <span class="glyphicon glyphicon-triangle-right icon-right-yellow"></span>
            </button>
            <span class="">（<?= my_lang('ハイフンは含みません')?>）</span>
        </div>
        <!--        <label class="col-sm-offset-3 zip_code_noti">ハイフン（－）無しで半角数字のみご記入ください</label>-->
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('都道府県')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-long" name="address1">
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('市町村')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-long" name="address2">
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('番地')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-long" name="address3">
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold "><?= my_lang('ビル名 部屋番号')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-long" name="address4">
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('電話番号')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input regist-input-medium" name="phone">
            <span class="">（<?= my_lang('ハイフンは含みません')?>）</span>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('氏名')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left label-tow-element">
            <div class="label-text-input">
                <span>姓：</span>
                <input class="shadow_input input-short" name="first_name" value="<?php echo $user->first_name; ?>">
            </div>
            <div class="label-text-input">
                <span>名：</span>
                <input class="shadow_input input-short" name="last_name" value="<?php echo $user->last_name; ?>">
            </div>

        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('フリガナ')?>:</label>
        </div>
        <div class="col-sm-9 no-padding-left  label-tow-element">
            <div class="label-text-input">
                <span><?= my_lang('姓')?>：</span>
                <input class="shadow_input input-short" name="first_name_furi">
            </div>
            <div class="label-text-input">
                <span><?= my_lang('名')?>：</span>
                <input class="shadow_input input-short" name="last_name_furi">
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('メールアドレス')?> ：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <input class="shadow_input input-long" disabled value="<?php echo $user->email; ?>" name="email">
        </div>
    </div>
    <div class="form-group bottom-border-none">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('パスワード')?> ：</label>
        </div>
        <div class="col-sm-9 no-padding-left clearfix">
            <div class="col-sm-5 no-padding-left">
                <input class="shadow_input regist-input-medium" name="password" type="password">
            </div>
            <div class="col-sm-7 no-padding-left">
                <label class="text-decription no-padding no-wrap-text"><?= my_lang('6桁から20桁の半角英数字大文字小文字で区別。')?></label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('パスワード')?><span class="small_font_size">(<?= my_lang('確認')?>)</span>：</label>
        </div>
        <div class="col-sm-9 no-padding-left password_conf">
            <input class="shadow_input regist-input-medium" name="password_conf" type="password">
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-3 align-right no-padding-left">
            <label class="control-label no-bold required-field"><?= my_lang('証明証')?>：</label>
        </div>
        <div class="col-sm-9 no-padding-left">
            <div class="form-group legal_entity_1">
                <input name="company_registration" type="radio" value="1"> <?= my_lang('法人登記有り')?> <br>
                <div>
                    <div class="col-sm-7">
                        <?= my_lang('法人番号')?> <input type="text" name="legal_entity">
                    </div>
                    <div class="col-sm-5">
                        <?= my_lang('半角数字13桁')?>
                    </div>
                </div>
            </div>
            <div class="proof legal_entity_2">
                <div class="form-group legal_entity_request">
                    <input name="company_registration" type="radio" value="2"> <?= my_lang('法人登記無し')?>
                    <p><?= my_lang('下記①②いずれかの本人確認証明証を添付してくさだい。')?></p>
                    <p>※<?= my_lang('ファイル種別（拡張子）   「jpg,jpeg,png,bmp」')?></p>
                    <p>※<?= my_lang('ファイルサイズ最大3MB')?></p>
                </div>
                <div class="form-group legal_entity_file1">
                    <p>①<?= my_lang('マイナンバーカード（表面の１枚)')?></p>
                    <div class="file">
                        <div class="col-sm-3 text_file">
                            <?= my_lang('表面(顔写真面)')?>
                        </div>
                        <div class="col-sm-9 photo">
                            <div class="photo_file col-sm-8">
                                <input type="file" id="photo" name="photo" accept="image/png, image/jpg, image/jpeg, image/bmp">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group legal_entity_file2">
                    <p>②<?= my_lang('運転免許証（表面裏面の２枚)')?></p>
                    <div class="form-group file">
                        <div class="col-sm-3 text_file">
                            <?= my_lang('表面(顔写真面)')?>
                        </div>
                        <div class="col-sm-9 front_photo">
                            <div class="front_photo_file col-sm-8">
                                <input type="file" id="front_photo" name="front_photo" accept="image/png, image/jpg, image/jpeg, image/bmp">
                            </div>
                        </div>
                    </div>
                    <div class="form-group file">
                        <div class="col-sm-3 text_file">
                            <?= my_lang('裏面')?>
                        </div>
                        <div class="col-sm-9 back_photo">
                            <div class="back_photo_file col-sm-8">
                                <input type="file" id="back_photo" name="back_photo" accept="image/png, image/jpg, image/jpeg, image/bmp">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group legal_entity_text">
                    <?= my_lang('登録後、直ぐにフリッパをご利用いただけますが、上記の確認ができない場合、規定によりアカウント登録が無効となる場合がございます。')?>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="more-padding"></div>
<div class="align-center register-button-container">
    <span class="title-notice">＊<?= my_lang('必須項目')?></span>
    <button type="submit" class="submit_button gray_button last_triangle big"
            id="register_temp_step1">
        <?= my_lang('送　信')?>
    </button>
</div>
