<div class="bannerPc">
    <img src="<?php echo base_url("assets/images/banner_feature.png") ?>">
    <div class="bannerText"><?= my_lang('Features List') ?></div>
</div>

<div class="bannerSp">
    <img src="<?php echo base_url("assets/images/banner_featureSp.png") ?>">
</div>
<div class="container">
    <div class="title">
        <div class="wrap">
            <span class="horizontal">
                <?= my_lang('受付アプリ') ?>
            </span><br>
            <p class="usage">
                Reception app
            </p>
        </div>
    </div>
    <div class="wrapTutorial">
        <div class="tutorial">
            <div class="itemNumber">
                1
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('迅速なスキャン機能') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('電子チケットの連続スキャンが可能で、迅速な受付を処理することができます。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                2
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('入場ボタン') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('チケットをスキャンし入場を決定するボタンです。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                3
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('受付総数カウンター') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('予約時の所属・氏名・同伴者数が表示されます。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                4
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('キャンセル数表示') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('タイムリーにキャンセル状況が表示されます。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                5
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('予約変更ボタン') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('予約情報の変更に対応することができます。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                6
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('使用期限設定機能') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('使用期限を管理画面より設定する事で、個人所有のスマホも受付に利用可能です。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                7
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('検索機能') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('ＱＲコードが利用できなかった場合、予約者の検索結果から入場処理が可能です。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                8
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('予約リスト表示') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('ＱＲコードが利用できなかった場合、全体リストからも入場処理が可能です。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                9
            </div>
            <div class="itemText">
                <span class="textBig">
                    <?= my_lang('新規受付機能') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('当日の受付も入力フォームにて迅速に対応できます。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                10
            </div>
            <div class="itemText">
                <span class="textBig">
                    VIP<?= my_lang('表示')?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('ＶＩＰ受付時にVIP表示と共に事前登録したメールアドレスに通知いたします。') ?>
                </span>
            </div>
        </div>
        <div class="tutorial">
            <div class="itemNumber">
                11
            </div>
            <div class="itemText">
                <span class="textBig">
                <?= my_lang('スキャナー番号') ?>
                </span><br>
                <span class="textSmall">
                    <?= my_lang('受付するスマホにスキャナー番号を付与することで受付場所の特定が可能です。') ?>
                </span>
            </div>
        </div>
        <div class="wrapImg">
            <div class="item">
                <div class="itemImg">
                    <img class="img-div" src="<?php echo base_url('assets/images/login.jpg') ?>">
                </div>
                <div class="itemText">
                    <span class="title">
                        <?= my_lang('ログイン画面') ?>
                    </span><br>
                    <span>
                        <?= my_lang('アプリをご使用いただくにはフリッパの主催者アカウントログイン情報をご入力いただく必要がございます。') ?>
                    </span>
                </div>
            </div>
            <div class="triangleSp">

            </div>
            <div class="item">
                <div class="itemImg triangle">
                    <img class="img-div" src="<?php echo base_url('assets/images/scan.png') ?>">
                </div>
                <div class="itemText">
                    <span class="title">
                        <?=  my_lang('スキャン初期画面')?>
                    </span><br>
                    <span>
                        <?= my_lang('主催者アカウント内にて予め設定した期間内に限り起動可能となります。') ?>
                    </span>
                </div>
            </div>
            <div class="triangleSp">

            </div>
            <div class="item">
                <div class="itemImg">
                    <img class="img-div" src="<?php echo base_url('assets/images/scan_result.png') ?>">
                </div>
                <div class="itemText">
                    <span class="title">
                        <?= my_lang('スキャン結果画面') ?>
                    </span><br>
                    <span>
                        <?= my_lang('電子チケットの持ち主情報が表示されます。「入場」をクリックして受付処理を実行いたします。') ?>
                    </span>
                </div>
            </div>
            <div class="triangleSp">

            </div>
        </div>
        <div class="wrapImg">
            <div class="item">
                <div class="itemImg">
                    <img class="img-div" src="<?php echo base_url('assets/images/list_apps.png') ?>">
                </div>
                <div class="itemText">
                    <span class="title">
                        <?= my_lang('予約申込者のリスト画面')?>
                    </span><br>
                    <span>
                        <?= my_lang('お申し込みフォームからの申請者一覧が表示され、お申込者毎に再発券や変更等の各種対応処理を実行していただけます。') ?>
                    </span>
                </div>
            </div>

            <div class="triangleSp">

            </div>
            <div class="item">
                <div class="itemImg triangle">
                    <img class="img-div" src="<?php echo base_url('assets/images/detail_apps.png') ?>">
                </div>
                <div class="itemText">
                    <span class="title">
                        <?= my_lang('対処画面') ?>
                    </span><br>
                    <span>
                        <?= my_lang('お申し込み者に対しての各種対応処理を実行していただけます。') ?>
                    </span>
                </div>
            </div>
            <div class="triangleSp">

            </div>
            <div class="item">
                <div class="itemImg">
                    <img class="img-div" src="<?php echo base_url('assets/images/sign_up.png') ?>">
                </div>
                <div class="itemText">
                    <span class="title">
                        <?= my_lang('予約申し込みフォーム画面') ?>
                    </span><br>
                    <span>
                        <?= my_lang('当日参加の予約お申し込みもこちらの画面からお客様に代わって迅速にご対応いただけます。') ?>
                    </span>
                </div>
            </div>
        </div>
        <div class="title">
            <div class="wrap">
                <span class="horizontal">
                    <?= my_lang('主催者専用アカウント') ?>
                </span><br>
                <p class="usage">
                    Organizer account
                </p>
            </div>
        </div>
        <div class="wrapTutorial">
            <div class="tutorial">
                <div class="itemNumber">
                    1
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('主催者登録') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('担当者の追加や登録情報の編集・削除が可能です。') ?>
                    </span>
                </div>
            </div>
            <div class="tutorial">
                <div class="itemNumber">
                    2
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('イベント登録') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('新規登録・編集・削除が何度でもでき実際に運用されるまでは無料でお試しいただけます。') ?>
                    </span>
                </div>
            </div>
            <div class="tutorial">
                <div class="itemNumber">
                    3
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('申し込みフォーム設定') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('フォーム項目の編集・新規登録・削除・埋め込みスクリプトタグ発行と タグのメール送信が可能です。') ?>
                    </span>
                </div>
            </div>
            <div class="tutorial">
                <div class="itemNumber">
                    4
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('チケット設定') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('編集・再発券・取り消し・テスト発券が可能です。') ?>
                    </span>
                </div>
            </div>
            <div class="tutorial">
                <div class="itemNumber">
                    5
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('チケット発券') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('自動発券・手動発券・発券不要で幅広い受付に対応できます。') ?>
                    </span>
                </div>
            </div>
            <div class="tutorial">
                <div class="itemNumber">
                    6
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('申込者リスト') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('新規追加・編集・削除・ダウンロード・予約のキャンセルは自動で更新されます。') ?>
                    </span>
                </div>
            </div>
            <div class="tutorial">
                <div class="itemNumber">
                    7
                </div>
                <div class="itemText">
                    <span class="textBig">
                        <?= my_lang('受付アプリ起動期間設定') ?>
                    </span><br>
                    <span class="textSmall">
                        <?= my_lang('アプリの起動期間を設定できるので、安心して個人のアプリも仕様することができます。') ?>
                    </span>
                </div>
            </div>
            <div class="title">
                <div class="wrap">
                    <span class="horizontal">
                        <?= my_lang('システム') ?>
                    </span><br>
                    <p class="usage">
                        System
                    </p>
                </div>
            </div>
            <div class="wrapTutorial">
                <div class="tutorial">
                    <div class="itemNumber">
                        1
                    </div>
                    <div class="itemText">
                        <span class="textMini">
                            <?= my_lang('大規模イベントでも規模にあわせた専用サーバーが低価格で迅速に構築可能です。') ?>
                        </span>
                    </div>
                </div>
                <div class="tutorial">
                    <div class="itemNumber">
                        2
                    </div>
                    <div class="itemText">
                        <span class="textMini">
                            <?= my_lang('大規模のイベントは、一時的に発生する膨大な同時アクセスによるサーバー負荷を分散型サーバーで対応しております。') ?>
                        </span>
                    </div>
                </div>
                <div class="tutorial">
                    <div class="itemNumber">
                        3
                    </div>
                    <div class="itemText">
                        <span class="textMini">
                            <?= my_lang('強固なセキュリティー環境を構築し安全に顧客情報を保管いたします。') ?>
                        </span>
                    </div>
                </div>
                <div class="wrapButton">
                    <a href="<?php echo site_url("register/temp_form_get"); ?>" class="register-button no-outline e_ajax_link not-hover no-padding-button">
                        <?= my_lang('主催者専用アカウント新規登録') ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>