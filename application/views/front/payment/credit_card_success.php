<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<style>
    .success-form-title {
        color: #c22d2d;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 26px;
        text-align: center;
    }
    .success-form-content {
        text-align: center;
    }
    .success-img-box {
        background-color: #fff000;
        height: 80px;
        width: 80px;
        margin: 0 auto;
        margin-top: 15px;
        border-radius: 63px 63px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    @media (min-width: 1024px) {
        .success-img-box {
            height: 126px;
            width: 126px;
        }
    }
    .success-img-box img {
        height: 30px;
    }
    @media (min-width: 1024px) {
        .success-img-box img {
            height: 57px;
        }
    }
    .success-msm {
        padding: 24px;
        font-size: 14px;
        line-height: 19px;
        font-weight: 700;
    }
    @media (min-width: 1024px) {
        .success-msm {
            padding: 35px 40px 0;
            font-size: 16px;
            line-height: 21px;
        }
    }
    .red-word {
        color: #c22d2d;
    }
    .manager-modal .modal-content, .rule_modal .modal-content {
        background-color: #fff;
        border-radius: 32px;
        padding: 40px 16px;
    }
    @media (min-width: 1024px) {
        .success-form-title{
            font-size: 28px;
            margin-bottom: 42px;
        }
        .manager-modal .modal-content, .rule_modal .modal-content {
            padding: 66px 64px;
        }
        .manager-big-modal.modal-dialog {
            width: 100%;
            max-width: 960px;
        }
    }
    @media (min-width: 1200px) {
        .manager-big-modal.modal-dialog {
            max-width: 1140px;
        }
    }
    @media (min-width: 1440px) {
        .manager-big-modal.modal-dialog {
            max-width: 1200px;
        }
    }
    body{
        display: flex;
        justify-content: center;
        font-family: "Noto Sans CJK JP", sans-serif;
        background-color: #FDBC00;
        align-items: center;
    }
</style>
<div class="modal-dialog manager-modal manager-big-modal">
    <div class="modal-content sent-mail-modal e_application_notify">
	    <div class="box_content-bg-fff has-back-to-top">
		    <form class="form-horizontal e_ajax_submit manager-form"
		          enctype="multipart/form-data"
		          method="POST" role="form">
			    <div class="modal-body">
				    <div class="success-form-title"><?= my_lang('予約お申し込みの完了',$lang)?></div>
				    <div class="embedded_mail_success_holder">
					    <div class="success-form-content">
						    <div class="success-img-box">
							    <img src="<?php echo base_url('assets/images/email.png'); ?>" alt="予約お申し込みの完了">
						    </div>
						    <div class="success-msm"><?= my_lang('お申し込みが完了しました。ご登録のメールアドレスに',$lang)?><br>
							    <?= my_lang('<span class="red-word">予約お申し込みの完了通知</span> を送信いたしました。',$lang)?></div>
					    </div>
				    </div>
			    </div>
		    </form>
	    </div>
    </div>
</div>