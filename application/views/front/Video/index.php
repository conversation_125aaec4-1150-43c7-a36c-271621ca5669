<div class="wrapVideo" onclick="getTime()">
    <div id="player" width="1000" height="700"></div>
</div>
<input id="urlVideo" type="hidden" value="<?php echo isset($url_video) ? $url_video : '' ?>">
<form method="POST" action="<?php echo isset($save_link) ? $save_link : "#" ?>" enctype="multipart/form-data">
    <input type="hidden" name="dateTime" id="dateTime">
</form>
<style>
    .wrapVideo {
        position: relative;
        padding-bottom: 56.25%;
        /* proportion value to aspect ratio 16:9 (9 / 16 = 0.5625 or 56.25%) */
        height: 0;
        overflow: hidden;
    }

    .wrapVideo iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
</style>
<script>

    function getId(url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);

    return (match && match[2].length === 11)
    ? match[2]
    : null;
    }
    const urlVideo = document.getElementById("urlVideo").value;
    const videoId = getId(urlVideo);
    const iframeMarkup = '<iframe width="560" height="315" src="//www.youtube.com/embed/' 
        + videoId + '" frameborder="0" allowfullscreen></iframe>';
    console.log('Video ID:', videoId)
    var tag = document.createElement('script');
    tag.src = "https://www.youtube.com/iframe_api";
    var firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    var player;
    function onYouTubeIframeAPIReady() {
        player = new YT.Player('player', {
            videoId: videoId,
            playerVars: {
                controls: 1,
                rel: 0,
                theme: 'dark',
                showinfo: 0,
                autoplay: 1,
                autohide: 2,
                iv_load_policy: 3,
                modestbranding: 1,
                loop: 1,
            },
            events: {
                'onReady': onPlayerReady,
                'onStateChange': onPlayerStateChange
            }
        });

    }

    var playerReady = false;
    // 4. The API will call this function when the video player is ready.
    function onPlayerReady(event) {
        playerReady = true;

    }


    // 5. The API calls this function when the player's state changes.
    //    The function indicates that when playing a video (state=1),
    //    the player should play for six seconds and then stop.
    function onPlayerStateChange(event) {
        if (event.data == YT.PlayerState.PLAYING) {
            var dateTime = new Date();
            document.getElementById("dateTime").value = dateTime;

        }
        if (event.data == YT.PlayerState.ENDED) {


        }
    }
        
</script>