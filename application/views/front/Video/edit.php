<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width" />
</head>

<body>
    <div class="wrapVideo">
        <div class="wrapTitle">
            <h3><?php echo isset($event_name) ? $event_name : "" ?></h3>
        </div>
        <form class="form_change" method="POST" action="<?php echo isset($save_link) ? $save_link : "#" ?>" enctype="multipart/form-data">
            <div class="wrapInput">
                <label for="code"><?= my_lang('お申し込みのメールアドレスに認証コードをお送りしました。 ご確認の上、認証コードを下記より承認してください。')?></label><br><br>
                <center><input type="text" name="code"></center>
            </div>
            <div class="wrapButton">
                <button type="submit"><?= my_lang('承認')?></button>
            </div>
        </form>
    </div>
    <style>
        .wrapVideo {
            width: 50%;
            margin: 80px auto 0 auto;
            background-color: #ECECEC;
            padding-bottom: 1px;
        }

        .wrapVideo .wrapTitle {
            width: 100%;
            background-color: #ECECEC;
            font-size: 21px;
            font-weight: bold;
        }

        .wrapVideo .wrapTitle h3 {
            margin: 0;
            padding: 30px 30px;
            word-wrap: break-word;
        }

        .wrapVideo .form_change {
            max-width: 98%;
            margin: auto auto 10px auto;
            padding: 70px 0px;
            background-color: #FFFFFF;
        }

        .wrapVideo .form_change .wrapInput {
            margin-bottom: 30px;
            text-align: center;
        }

        .wrapVideo .form_change label {
            font-size: 15px;
            width: 50%;
            white-space: -webkit-nowrap;
            margin-right: 30px;
            white-space: pre-line;
        }

        .wrapVideo .form_change input {
            width: 70%;
            line-height: 30px;
        }

        .wrapVideo .form_change label::before {
            position: relative;
            left: -30px;
            font-size: 15px;
            color: red;
        }

        .wrapButton {
            text-align: center;
            margin: auto;
            white-space: nowrap;
        }

        .wrapButton button {
            padding: 10px 40px;
            cursor: pointer;
            color: #ffffff;
            border: none;
            border-bottom: 1px solid #6f6c64;
            border-right: 1px solid #6f6c64;
            background: rgba(152, 148, 139, 1);
            background: -moz-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
            background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(152, 148, 139, 1)), color-stop(100%, rgba(128, 123, 112, 1)));
            background: -webkit-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
            background: -o-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
            background: -ms-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
            background: linear-gradient(to bottom, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#98948b', endColorstr='#807b70', GradientType=0);
            -webkit-box-shadow: 2px 2px 0px rgb(213 212 212), 0px 0px 0px rgb(0 0 0 / 0%);
            -moz-box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);
            box-shadow: 2px 2px 0px rgb(213 212 212), 0px 0px 0px rgb(0 0 0 / 0%);
        }

        @media (min-width: 320px) and (max-width: 992px) {
            .wrapVideo {
                width: 100%;
            }

            .wrapVideo .form_change {
                max-width: 90%;
                padding: 50px 10px;
            }

            .wrapVideo .wrapTitle h3 {
                font-size: 20px;
                padding: 20px;
            }

            .wrapVideo .form_change label {
                font-size: 12px;
            }

            .wrapVideo .form_change input {
                width: 80%
            }
        }
    </style>
</body>

</html>