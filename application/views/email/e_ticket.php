<?php echo "$event->e_ticket_mail". "\r\n"; ?>
<?php echo "\r\n" ?>
<?php if($event->url_video != null || $event->url_video != '') {?>
<?php echo my_lang("■動画視聴URL：下記のURLへは必ずお申し込みを送信したブラウザーからアクセスする必要がございます。", $event->lang); ?>
<?php echo my_lang("異なるブラウザーでご視聴の場合、下記の再視聴申請URLからの申請と認証が必要です。", $event->lang); ?>
<?php echo site_url('manager/video/index/' . $event->id . "?subscriber_id={$data_subscriber->id}\r\n"); ?>
<?php echo "\r\n" ?>
<?php echo my_lang("■再視聴申請URL："); ?>
<?php echo site_url('manager/video/check/' . $event->id . "?subscriber_id={$data_subscriber->id}\r\n"); ?>
<?php echo "\r\n" ?>
<?php } ?>
<?php echo my_lang("■お申し込み内容", $event->lang); ?>
<?php echo "\r\n" ?>
<?php $application_form = str_replace(my_lang("ご利用規約 :", $event->lang), "", $application_form);?>
<?php echo $application_form . "\r\n"; ?>

<?php if (isset($event->is_fee_form) && !empty($fee_form)) { ?>
<?php echo $fee_form . "\r\n"; ?>
<?php } ?>
<?php if (isset($application_url) && !empty($application_url)) { ?>
■<?php echo isset($event_type) && !$event_type ? my_lang('お申し込み情報の変更', $event->lang) : my_lang('予約変更', $event->lang) ?><?php echo my_lang("につきましては下記のＵＲＬよりお申し込みいただけます。", $event->lang); ?>
<?php echo isset($application_url) ? $application_url . "\r\n" : '' ?>
<?php } ?>
<?php if (isset($cancel_url) && !empty($cancel_url)) { ?>
<?php echo "\r\n"; ?>
<?php echo my_lang("■キャンセルにつきましては下記のＵＲＬよりお申し込みいただけます。", $event->lang); ?>
<?php echo isset($cancel_url) ? $cancel_url . "\r\n" : '' ?>
<?php } ?>

<?php
$data_event = [];
if (!is_array($event)) {
    $data_event = [
        'data_event' => $event,
    ];
} else {
    $data_event = $event;
}
echo $this->load->view("email/email_footer_organizer", $data_event, TRUE);
?>
<?php $this->load->view("email/email_footer", $data_event); ?>
