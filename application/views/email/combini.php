<?php echo isset($mail_content) ? $mail_content : '' ?>
<?php echo "\r\n" ?>
<?php if (isset($event->type_ticket) && $event->type_ticket > 0 && !empty($ticket_form)) { ?>
<?php echo my_lang("■ 電子チケット", $event->lang); ?>
<?php echo $ticket_form; ?>
<?php } ?>
<?php echo "\r\n" ?>
<?php echo my_lang("■お申し込み内容", $event->lang); ?>
<?php echo "\r\n" ?>
<?php $application_form = str_replace(my_lang("ご利用規約 :", $event->lang), "", $application_form);?>
<?php echo $application_form . "\r\n"; ?>

<?php echo isset($fee_form) ? $fee_form : ''; ?>
<?php if (!empty($event->url_video) && !empty($application->transaction_status)) {?>
<?php echo my_lang("■動画視聴URL：", $event->lang); ?>
<?php echo my_lang("下記のURLへは必ずお申し込みを送信したブラウザーからアクセスする必要がございます。", $event->lang); ?>
<?php echo my_lang("異なるブラウザーでご視聴の場合、下記の再視聴申請URLからの申請と認証が必要です。", $event->lang); ?>
<?php echo site_url('manager/video/index/' . $event->id . "?subscriber_id={$application->id}\r\n"); ?>
<?php echo "\r\n" ?>
<?php echo my_lang("■再視聴申請URL：", $event->lang); ?>
<?php echo site_url('manager/video/check/' . $event->id . "?subscriber_id={$application->id}\r\n"); ?>
<?php echo "\r\n" ?>
<?php } else { ?>
<?php echo my_lang("■ お支払い方法: ", $event->lang); ?><?php echo isset($event->payment_method) ? $event->payment_method : ($application->transaction_type ? $application->transaction_type : '') ?> <?php echo "\r\n" ?>
<?php if (isset($combini_info)) {
    echo my_lang("受付場所：", $event->lang) . $combini_info->reception_place == 1 ? my_lang('イベント会場', $event->lang) . "\r\n" : $combini_info->reception_place . "\r\n";
} ?>
<?php if (!$application->transaction_status) {
echo my_lang("■ お支払い期限：", $event->lang) ."\r\n";
echo my_lang("コンビニ決済のお支払い有効期間はお申し込みから４日間となります。", $event->lang) ."\r\n";
echo my_lang("詳細は「コンビニ決済お支払番号のお知らせ」メールにてご確認ください。", $event->lang) ."\r\n";
} ?>
<?php if (empty($ticket_form)) { ?>
<?php echo '\r\n'; ?>
<?php echo my_lang("※こちらのメールは登録内容のご確認メールです。", $event->lang); ?>
<?php echo my_lang("チケットではございません。", $event->lang); ?>
<?php } ?>
<?php } ?>

<?php
$data_event = [];
if (!is_array($event)) {
    $data_event = [
        'data_event' => $event,
    ];
} else {
    $data_event = $event;
}
echo $this->load->view("email/email_footer_organizer", $data_event, TRUE);
?>
<?php echo "\r\n" ?>
<?php $this->load->view("email/email_footer", $data_event); ?>
