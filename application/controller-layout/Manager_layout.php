<?php
/**
 * Created by PhpStorm.
 * User: ha
 * Date: 06/27/2016
 * Time: 1:59 PM
 */
if (!defined('BASEPATH'))
    exit('No direct script access allowed');

abstract class Manager_layout extends Base_layout {


    function __construct() {
        parent::__construct();
        //Required login manager
        $config =& get_config();
        $is_maintain = (isset($config['is_maintain'])) ? $config['is_maintain'] : FALSE;
        $this->config->load('site_settings');
        $home_url = $this->config->item('home_site_url');
        $promoter_site_url = $this->config->item('promoter_site_url');

        if ($is_maintain) {
            if ($this->session->userdata('user_id')) {
                $this->session->unset_userdata('user_id', '');
            }
            if ($this->uri->uri_string() != 'notice_maintain') {
                redirect(site_url('notice_maintain'));
            }
        }

        if (!$is_maintain && $this->uri->uri_string() == 'notice_maintain') {
            redirect(site_url('/'));
        }

        if (!$this->session->userdata('user_id')) {
            $list_not_required_login = [
                'login', 'reset_password', 'faq',
                'company_information', 'term_of_service', 'site_map',
                'requirement', 'privacy', 'contact', 'form_survey', 'privacy_modal',
                'trade_info', 'notice_maintain', 'error_info', 'invoice',
            ];
            if (!in_array(strtolower($this->router->class), $list_not_required_login)) {
                if ($this->input->is_ajax_request()) {
                    $data_return["state"] = 0;
                    $data_return['callback'] = 'required_login';
                    $data_return['msg'] = my_lang('セッションを終了しました。');
                    $data_return['redirect'] = site_url('manager/login');

                    echo json_encode($data_return);
                    exit;
                } else {
                    $this->session->set_userdata('accessed_url', $this->uri->uri_string());
                    redirect(site_url('manager/login'));
                }
            }
        } else {
            $cla = strtolower($this->router->class);
            $met = strtolower($this->router->method);
            $after_login = (empty($_SERVER['HTTPS']) ? 'http://' : 'https://').$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];

            // 閲覧者（閲覧のみ）がアクセス不可
            if ($this->session->userdata('role_id') == 3) {
                // controller のみ
                $list_not_access = array(
                    'user' => array('user_list', 'form_edit_user_save'),
                    'event' => array('create'),
                    'event_edit' => array('index', 'edit_event_save', 'edit_time_subscriber', 'check_validate_time_subscriber', 'edit_time_checkin', 'ticket', 'stop'),
                    'event_detail' => array('embedded_code', 'overview'),
                    'application' => array('add_save', 'cancel_save', 'checkin_save', 'delete', 'edit_save', 'export_excel', 'import_winner_status', 'note_modal_html', 'send_ticket', 'temp_add_save', 'ticket_img_confirm', 'view_subscriber_image', 'vip_save', 'winner_handle', 'winner_handle_person'),
                    'application_form' => array(),
                    'email_template' => array(),
                    'user_list' => array('index', 'check'),
                );
                if (isset($list_not_access[$cla]) && (empty($list_not_access[$cla]) || in_array($met, $list_not_access[$cla]))) {
                    $this->session->set_flashdata('after_login', $after_login);
                    redirect(site_url('error_info/no_access'), 'refresh');
                }
            }

            // 編集者（閲覧者＋編集可）
            if ($this->session->userdata('role_id') == 2) {
                // controller のみ
                $list_not_access = array(
                    'user' => array('user_list', 'form_edit_user_save'),
                    'user_list' => array('index', 'check'),
                );
                if (isset($list_not_access[$cla]) && (empty($list_not_access[$cla]) || in_array($met, $list_not_access[$cla]))) {
                    $this->session->set_flashdata('after_login', $after_login);
                    redirect(site_url('error_info/no_access'), 'refresh');
                }
            }

            if ($this->session->userdata('role_id') == 1) {
                // controller のみ
                $list_not_access = array(
                    'user_list' => array('index', 'check'),
                );
                if (isset($list_not_access[$cla]) && (empty($list_not_access[$cla]) || in_array($met, $list_not_access[$cla]))) {
                    $this->session->set_flashdata('after_login', $after_login);
                    redirect(site_url('error_info/no_access'), 'refresh');
                }
            }

            $last_domain = $this->session->userdata('current_domain');
            if (!$last_domain || $last_domain != $promoter_site_url) {
                $this->session->set_userdata('accessed_url', $this->uri->uri_string());
                $this->session->unset_userdata('user_id', '');
                redirect(site_url('manager/login'));
            }
        }

        $this->set_layout_all("manager/base_layout/layout_all");
        $this->set_layout_body("manager/base_layout/layout_body");

        $this->set_data_part("title", "manager page title ", FALSE);
        $this->set_data_part("description", "誰もが自由に電子チケットを発券できるイベント管理システム。", FALSE);
        $this->set_data_part("favicon", base_url("assets/manager/images/flippa.ico"), FALSE);
        $this->set_data_part("keywords", "front keyword", FALSE);
        $this->set_data_part("canonical", "", FALSE);
        $this->set_data_part("breadcrumb", Array("view_file" => "manager/base_layout/breadcrumb"));

        $this->load->model('m_event_subscriber');
        $this->load->model('m_event');

        //Unset before get
        $this->m_event->unset_before_get('custom_select_count_subscriber');
        $this->m_event->unset_before_get('custom_format_select');
        $this->set_data_part("top_bar",
            Array(
                "view_file"         => "manager/base_layout/top_bar",
                'active'            => 'none',
                "total_application" => !empty($this->session->userdata('total_application')) ? $this->session->userdata('total_application') : 0 ,
                "total_event"       => !empty($this->session->userdata('total_event')) ? $this->session->userdata('total_event') : 0 ,
                "list_event"       => !empty($this->session->userdata('list_event')) ? $this->session->userdata('list_event') : [] ,
                "home_url"          => $home_url,
                'site_name_plus'    => 'Flippa | フリッパ ',
            )
        );
        //Set before get
        $this->m_event->set_before_get('custom_select_count_subscriber');
        $this->m_event->set_before_get('custom_format_select');

        $this->set_data_part("side_bar_left", Array("view_file" => "manager/base_layout/side_bar_left"));
        $this->set_data_part("side_bar_right", Array("view_file" => "manager/base_layout/side_bar_right"));
        $this->set_data_part("side_bar_absolute", Array("view_file" => "manager/base_layout/side_bar_absolute"));
        $this->set_data_part("footer", Array("view_file" => "manager/base_layout/footer"));
        $this->set_data_part("assets_footer", Array("view_file" => "manager/base_layout/assets_footer"));
        $this->set_data_part("assets_header", Array("view_file" => "manager/base_layout/assets_header"));

        $this->load_more_css("assets/manager/css/search_result.css");
        $this->load_more_js("assets/manager/js/search_result.js", TRUE);
        $this->load_more_js("assets/plugins/ajaxzip3.js", TRUE);
        $this->load_more_js("assets/plugins/mouse_pointer.js", TRUE);
    }

}
