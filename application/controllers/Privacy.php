<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 16/06/2016
 * Time: 3:18 SA
 */

/**
 * Class Home
 */
class Privacy extends Front_layout {

    function __construct() {
        parent::__construct();
        $this->load_more_css("assets/front/css/privacy.css");
        $this->set_data_part("title", my_lang('プライバシーポリシー'), FALSE);
    }

    /**
     * check user are logged in before jump into Master admin
     */
    public function index() {
        $data = Array();
        $content = $this->load->view("front/privacy/privacy", $data, TRUE);
        $this->show_page($content);
    }


}