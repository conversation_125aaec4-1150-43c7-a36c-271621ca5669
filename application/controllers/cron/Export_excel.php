<?php

/**
 * Class Export_excel
 *
 * @property M_export                       m_export
 * @property M_event                        m_event
 * @property S3_upload                      s3_upload
*/

class Export_excel extends CI_Controller
{
    public function __construct() {
        parent::__construct();
        $this->load->model("m_export");
        $this->load->library("Export");
        $this->load->library("S3_upload");
    }

    //type 0:new, 1:processing, 2:done
    public function export_excel() {
        ini_set('memory_limit', '250M');
        set_time_limit(0);
        $type_processing = 1;
        $event_processing = $this->m_export->get_event_processing($type_processing);
        if(empty($event_processing)) {
            $type_new = 0;
            $type_done = 2;
            $type_error = 3;
            $event_processing = $this->m_export->get_event_processing($type_new);
            if(isset($event_processing)) {
                try {
                    $this->m_export->update($event_processing->id, ['type' => $type_processing]);
                    $data_return = $this->export->export_excel($event_processing->event_id);
                    $data = [
                        'type' => $type_done,
                        'updated_at' => date('Y-m-d H:i:s'),
                        'code' => $data_return,
                        'processing' => 100
                    ];
                    $this->m_export->update($event_processing->id, $data);
                } catch (Exception $e) {
                    $result = $event_processing->id . ",Exception," . $e->getMessage();
                    $this->s3_upload->log_insert($result, 'export_excel');
                    $data = [
                        'type' => $type_error,
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                    $this->m_export->update($event_processing->id, $data);
                }
            }
        }
    }

    protected function log_insert($result, $name_file = 'export_excel') {
        $folderLog = APPPATH . 'logs';
        if (!is_dir($folderLog)) {
            $old = umask(0);
            mkdir($folderLog, 0755);
            umask($old);
        }
        $myFile = APPPATH . "logs/log-" . $name_file . "-" . date('Y-m-d') . ".txt";
        $fh = fopen($myFile, 'a');
        fwrite($fh, date('Y-m-d H:i:s') . "," . $result . PHP_EOL);
        fclose($fh);
    }
}