<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 16/06/2016
 * Time: 2:32 CH
 */
class Feature extends Front_layout {

    function __construct() {
        parent::__construct();
        $this->load_more_css("assets/front/css/feature.css");
        $this->set_data_part("title", my_lang('機能一覧'), FALSE);
    }

    function index() {
        $data = Array();
        $content = $this->load->view("front/feature/feature", $data, TRUE);
        $this->show_page($content);
    }
}