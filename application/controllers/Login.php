<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 16/06/2016
 * Time: 3:18 SA
 */

/**
 * Login
 *
 * @property M_user m_user
 */
class Login extends Manager_layout {

    function __construct() {
        parent::__construct();
        $this->load_more_css("assets/manager/css/login.css");
        $this->load->model('m_user');
        $this->load->library('email');
        $this->load_more_js("assets/manager/js/login.js", TRUE);
    }

    private $admin_page = 'manager/event_list/graph/double';

    /**
     * check user are logged in before jump into Master admin
     */
    public function index() {
        $this->config->load('site_settings');
        $organizer = $this->config->item('organizer');
        if (empty($organizer)) {
            $data = Array();
            $data["heading"] = my_lang('404 ページが見つかりません！');
            $data["message"] = "<p>". my_lang('あなたが要求したページは見つかりませんでした')."</p>";
            $this->output->set_status_header('404');
            $this->load->view('errors/html/error_404' , $data); 
            return;
        }
        if ($this->session->userdata("user_id"))
            redirect(site_url('manager/event_list'));

        $data = Array();
        $data['login_url'] = site_url('manager/login/check');
        $data['register_url'] = site_url('manager/login');
        $this->load_more_css("assets/manager/css/forget_password_modal.css");
        $this->load_more_css("assets/manager/css/event.css");
        $this->set_data_part("title", "Login", FALSE);
        $content = $this->load->view("manager/login/login", $data, TRUE);
        $this->show_page($content);
    }

    /**
     * check login
     */
    public function check() {
        if ($this->input->is_ajax_request() && $this->input->post()) {
            $dataReturn = Array();
            $dataReturn["callback"] = "login_response";
            $email = $this->input->post("username");
            $pass = $this->input->post("password");
            $this->config->load('site_settings');
            $current_domain = $this->config->item('promoter_site_url');
            $user = $this->ion_auth->get_user($email);
            $login = $this->ion_auth->login($email, $pass);
            if ($login) {
                //check first login
                $last_login = isset($user->last_login) ? $user->last_login : 0;
                if ($last_login) {
                    $accessed_url = $this->session->userdata('accessed_url');
                    if (isset($accessed_url)) {
                        $redirect = site_url($this->session->userdata('accessed_url'));
                    } else {
                        $redirect = site_url($this->admin_page);
                    }
                } else {
                    $this->session->set_userdata('is_first_login', TRUE);
                    $redirect = site_url('manager/event_list/graph/double');
                }
                $this->session->set_userdata('old_last_login', $last_login);
                //Get all children of user
                $current_parent_path = isset($user->parent_path) ? $user->parent_path : '';
                $this->session->set_userdata('current_parent_path', $current_parent_path);
                $this->session->set_userdata('current_domain', $current_domain);

                $dataReturn["state"] = 1;
                $dataReturn["msg"] = "Login success";
                $dataReturn["redirect"] = $redirect;
            } else {
                $dataReturn["state"] = 0;
                $dataReturn["msg"] = my_lang('メールアドレス / パスワードが一致いたしません。');
            }
            echo json_encode($dataReturn);
        } else {
            redirect();
        }
    }

    public function form() {
        $this->config->load('site_settings');
        $home_url = $this->config->item('home_site_url');
        redirect($home_url . '?action=createnewaccount');
    }

    public function logout() {
        $lang = get_cookie('lang');
        if ($lang) {
            set_cookie('lang', $lang, 865000);
        }
        $this->session->sess_destroy();
        if (!$this->input->is_ajax_request()) {
            redirect("/");
        } else {
            $redirect = site_url('/');
            $dataReturn["msg"] = my_lang('ログアウトしました。');
            $dataReturn["callback"] = "login_default_ajax_link";
            $dataReturn["redirect"] = $redirect;
            echo json_encode($dataReturn);
        }

    }

}
