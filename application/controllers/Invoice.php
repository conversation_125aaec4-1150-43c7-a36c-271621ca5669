<?php
/**
 * Application event
 *
 * @property M_admin_settlement             m_admin_settlement
 * @property M_admin_settlement_history     m_admin_settlement_history
 */
class Invoice extends Front_layout {
    function __construct() {
        parent::__construct();
        $this->load->model('m_admin_settlement');
        $this->load->model('m_admin_settlement_history');
        $this->set_data_part("title", "View pdf invoice", FALSE);
    }

    public function view($id, $file_name) {
        $settlement = $this->m_admin_settlement->get($id);
        if($file_name == null){
            $last_pdf = $this->m_admin_settlement_history->order_by("id", "DESC")->get_by("admin_settlement_id", $id);
            $pdfFilePath = APPPATH . "data-export/admin/$settlement->user_id/$settlement->year/$settlement->month/$last_pdf->file_name";
        }else{
            $this->set_data_part("title", "$file_name", FALSE);
            $file_name = urldecode($file_name);
            $pdfFilePath = APPPATH . "data-export/admin/$settlement->user_id/$settlement->year/$settlement->month/$file_name";
        }

        if (file_exists($pdfFilePath)) {
            $mime = mime_content_type($pdfFilePath);
            
            header('Content-Type: ' . $mime);
            header('Content-Disposition: inline; filename="' . basename($pdfFilePath) . '"');
            header('Content-Length: ' . filesize($pdfFilePath));
            header('Accept-Ranges: bytes');

            readfile($pdfFilePath);
        } else {
            show_404();
        }
    }
}