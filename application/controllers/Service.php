<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 16/06/2016
 * Time: 2:32 CH
 */
class Service extends Front_layout {

    function __construct() {
        parent::__construct();
        $this->set_data_part("title", my_lang('サービス概要'), FALSE);
        $this->load_more_css("assets/front/css/contact.css");
        $this->load_more_css("assets/front/css/service.css");
    }

    function index() {
        $data = Array();
        $this->config->load('site_settings');
        $data['promoter_site_url'] = $this->config->item('promoter_site_url');
        $content = $this->load->view("front/service/service", $data, TRUE);
        $this->show_page($content);
    }
}