<?php

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/10/2016
 * Time: 4:31 CH
 *
 * @property Contact_lib     contact_lib
 * @property Application_lib application_lib
 */
class Contact extends Manager_layout {

    function __construct() {
        parent::__construct();
        $this->load->model("m_user");
        $this->load->library("Email_lib");
        $this->load->library("Contact_lib");
        $this->load->library("Application_lib");
        $this->set_data_part("title", my_lang('お問い合わせ'), FALSE);
    }

    function index() {
        $data_return = $this->contact_lib->index();
        if (isset($data_return['state']) && !$data_return['state']) {
            $data_return['callback'] = 'action_response';
        }
        echo json_encode($data_return);
    }

    public function contact_form_check() {
        $data = $this->input->post();
        $data_return = $this->contact_lib->contact_form_check($data);
        if (isset($data_return['state']) && !$data_return['state']) {
            $data_return['callback'] = "temple_contact_check";
        }
        echo json_encode($data_return);
    }

    public function is_phone($str) {
        return $this->application_lib->is_phone($str);
    }

    public function is_zip_code($str) {
        return $this->application_lib->is_zip_code($str);
    }
}