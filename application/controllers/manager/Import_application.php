<?php

/**
 * Created by IntelliJ IDEA.
 * User: hana
 * Date: 07/06/2017
 * Time: 16:07
 *
 * @property M_event           m_event
 * @property M_event_form_data m_event_form_data
 * @property Application_lib   application_lib
 * @property S3_upload         s3_upload
 *
 */
class Import_application extends CI_Controller {

    function __construct() {
        parent::__construct();
        $this->load->model('m_event');
        $this->load->model('m_event_form_data');
        $this->load->library('Application_lib');
        $this->load->library('S3_upload');
    }

    public function index() {
        $data = Array();
        $data['list_event'] = $this->m_event->get_all();
        $data['submit_link'] = site_url('manager/import_application/upload_excel');
        $this->load->view("manager/upload_excel/index", $data);
    }

    public function upload_excel() {
        $data_post = $this->input->post();
        $event_id = isset($data_post['event_id']) ? $data_post['event_id'] : 0;
        if (!$event_id) {
            echo 'not valid event!';
            return;
        }
        // Check csv file

        if (isset($_POST["submit"])) {
            $file = $_FILES['csv_file']['tmp_name'];
            if ($file == NULL) {
                echo "Please select a file to import";
            } else {
                $handle = fopen($file, "r");
                $inputFileType = 'CSV';
                $inputFileName = $_FILES["csv_file"]["tmp_name"];

                echo 'Loading file ', pathinfo($inputFileName, PATHINFO_BASENAME), ' using IOFactory with a defined reader type of ', $inputFileType, '<br />';
                /**  Create a new Reader of the type defined in $inputFileType  **/
                $objReader = PHPExcel_IOFactory::createReader($inputFileType);
                $objPHPExcel = $objReader->load($inputFileName);

                $sheet = $objPHPExcel->setActiveSheetIndex(0);
                $highestRow = $sheet->getHighestRow();

                //set field_id and position of value
                $event_fields = $this->m_event_form_data->get_many_by(['event_id' => $event_id]);
                $arr_field_id = [];
                $arr_position = [];
                foreach ($event_fields as $field) {
                    $arr_field_id[] = $field->field_id;
                    $arr_position[] = $field->position;
                }
                $list_data_error = [];
                $list_column = [
                    '0' => 'first_name',
                    '1' => 'email',
                    '2' => 'company_name',
                    '3' => 'department_name',
                    '4' => 'phone',
                ];
                for ($row = 3; $row < $highestRow + 1; $row++) {
                    $data_set = array();
                    foreach ($list_column as $column => $column_value) {
                        $value = $sheet->getCellByColumnAndRow($column, $row)->getValue();
                        if ($column_value == 'phone') {
                            $value = str_replace('-', '', $value);
                        }
                        $data_set[$column_value] = $value;
                        if ($column_value == 'email') {
                            $data_set['email_confirm'] = $sheet->getCellByColumnAndRow($column, $row)->getValue();
                        }

                    }
                    $data_set['privacy_rule'] = 1;
                    $data_return = $this->application_lib->add_save($event_id, $data_set, [] ,TRUE);
                    if (!isset($data_return['state']) || $data_return['state'] != 1){
                        $list_data_error[] = $data_return;
                    }
                }
                if (is_array($list_data_error) && count($list_data_error)){
                    $log_insert_msg = json_encode($list_data_error);
                    $this->s3_upload->log_insert($log_insert_msg, "log-insert-list-application");
                }
            }

        }
    }

    /**
     * @param $msg
     */
    protected function log_insert($msg) {
        $folderLog = APPPATH . 'logs';
        if (!is_dir($folderLog)) {
            $old = umask(0);
            mkdir($folderLog, 0755);
            umask($old);
        }
        $myFile = APPPATH . "logs/log-insert-list-application-" . date('Y-m-d') . ".txt";
        $fh = fopen($myFile, 'a');
        fwrite($fh, date('Y-m-d H:i:s') . ": " . $msg . PHP_EOL);
        fclose($fh);
    }
}