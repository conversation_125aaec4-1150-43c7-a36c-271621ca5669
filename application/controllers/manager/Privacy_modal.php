<?php

/**
 * Created by PhpStorm.
 * User: li
 * Date: 13/07/2016
 * Time: 18:32
 *
 * @property Rule_lib rule_lib
 */
class Privacy_modal extends Manager_layout {

    function __construct() {
        parent::__construct();
        $this->load->library("Rule_lib");
    }

    /**
     * check user are logged in before jump into Master admin
     */
    public function index() {
        $data_return = $this->rule_lib->privacy();
        echo json_encode($data_return);
    }


}