<?php

/**
 * Created by IntelliJ IDEA.
 * User: LOCBKIT
 * Date: 12/28/2016
 * Time: 5:21 PM
 *
 * @property Event_lib                     event_lib
 * @property Application_lib               application_lib
 * @property M_event_fee                   m_event_fee
 * @property M_event_fee_item              m_event_fee_item
 * @property M_event_fee_item_data         m_event_fee_item_data
 * @property M_event_form_data             m_event_form_data
 * @property M_event_fee_banking           m_event_fee_banking
 * @property M_event_fee_cashier           m_event_fee_cashier
 * @property M_event_fee_combini           m_event_fee_combini
 * @property M_city_shipping               m_city_shipping
 * @property M_event_fee_item_shipping     m_event_fee_item_shipping
 * @property M_event_subscriber_fee        m_event_subscriber_fee
 * @property M_event_subscriber_fee_detail m_event_subscriber_fee_detail
 * @property M_event_subscriber            m_event_subscriber
 */
class Event_fee extends Manager_layout {
    public $_type_fee = "fee";
    public $_type_free_fee = "free_fee";

    function __construct() {
        parent::__construct();
        $this->load->library("Event_lib");
        $this->load->library("Application_lib");
        $this->load->model('m_event_fee');
        $this->load->model('m_event_fee_item');
        $this->load->model('m_event_fee_data');
        $this->load->model('m_event_fee_item_data');
        $this->load->model('m_event_form_data');
        $this->load->model('m_event_fee_banking');
        $this->load->model('m_event_fee_cashier');
        $this->load->model('m_event_fee_combini');
        $this->load->model('m_city_shipping');
        $this->load->model('m_event_fee_item_shipping');
        $this->load->model('m_event_subscriber_fee');
        $this->load->model('m_event_subscriber_fee_detail');
        $this->load->model('m_event_subscriber');
    }

    public function fee_item($id) {
        $data = [];
        $check_event = $this->event_lib->check_event_active($id);
        $data['type_ticket'] = $check_event['data']->type_ticket;
        if (!($check_event['state'])) {
            $this->m_event_fee->set_soft_delete(FALSE);
            $data['fee_info'] = $this->m_event_fee->get_by(['event_id' => $id]);
            if (!empty($data['fee_info']->id) && !$data['fee_info']->is_fee) {
                $data_popup = [];
                $data_popup['message'] = my_lang('料金フォームの追加はできません。');
                $data_popup['button_text'] = my_lang('閉じる');

                $html = $this->load->view("manager/notification_popup/notification_popup", $data_popup, TRUE);
                echo json_encode([
                    'state' => 1,
                    'html'  => $html,
                ]);
                return TRUE;
            }
        }
        $data['temp_link'] = site_url("manager/event_fee/temp_fee_item_save/" . $id);
        $data['save_link'] = site_url("manager/event_fee/temp_fee_item_save/" . $id);
        $data['html_type'] = $html_type = $this->_type_fee;
        $data['html_fee_item'] = $this->get_content_fee_item($id, [], $html_type);
        $data['html_title'] = my_lang('料金設定');
        $data['html_description'] = '<p><b style="color: black">' . my_lang('チケット') . ' : </b>'
            . my_lang('電子チケット発券の有無を選択可能です。共同購入が可能でチケットの分散送信が可能です。') . '</p>'
            . '<p>' . my_lang('「発券不要」を選択した場合はチケットは発券されませんがお申し込み数量は発券手数料として課金されます。') . '</p><br>'
            . '<p><b style="color: black">' . my_lang('物販') . ' ： </b>'
            . my_lang('番号毎に送料設定が可能です。さらにチケット発券と併せた物販以外のサービス料金等でご利用が可能です。') . '</p>'
            . '<p>' . my_lang('発券はされませんが、チケットと同様に発券手数料が課金されます。') . '</p>';

        $html = $this->load->view("manager/event_fee/fee_modal", $data, TRUE);

        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function free_fee_item($id) {
        $data = [];
        //check event_fee type
        $check_event = $this->event_lib->check_event_active($id);
        $data['is_hide_stock'] = $check_event['data']->is_hide_stock;
        $data['type_ticket'] = $check_event['data']->type_ticket;
        if (!($check_event['state'])) {
            $this->m_event_fee->set_soft_delete(FALSE);
            $data['fee_info'] = $this->m_event_fee->get_by(['event_id' => $id]);
            if (!empty($data['fee_info']->id) && $data['fee_info']->is_fee) {
                $data_popup = [];
                $data_popup['message'] = my_lang('無料チケットは設定できません。');
                $data_popup['button_text'] = my_lang('閉じる');

                $html = $this->load->view("manager/notification_popup/notification_popup", $data_popup, TRUE);
                echo json_encode([
                    'state' => 1,
                    'html'  => $html,
                ]);
                return TRUE;
            }
        }

        $data['temp_link'] = site_url("manager/event_fee/temp_free_fee_item_save/" . $id);
        $data['save_link'] = site_url("manager/event_fee/temp_free_fee_item_save/" . $id);
        $data['html_type'] = $html_type = $this->_type_free_fee;
        $data['html_fee_item'] = $this->get_content_fee_item($id, [], $html_type);
        $data['html_title'] = my_lang('無料チケット設定');
        $data['html_description'] = '<div class="box-item" >
        <p>'. my_lang('項目１から項目７まであり最大7種の送料と140種類の設定が可能となっています。</br> 項目１: 電子チケット発券の有無を選択可能です。チケットの分散送信が可能です。「発券不要」を選択した場合はチケットは発券されませんがお申し込み数量は発券手数料として課金されます。 </br> 項目２以降: チケット以外のサービス等でのご利用が可能です。発券はされませんが、お申し込み数量は発券手数料として課金されます。').'</p>
        </p>';
       
        $checked = $check_event['data']->is_childcare_education ? 'checked' : '';
        $data['html_is_childcare_education'] = '
            <p class="">＊'. my_lang('育児・教育関連の無料イベントに限り発券料は無料となります。該当する場合、下記にチェックを入れてください。').'</p>
            <p class="">'. my_lang('チェックがない場合、又は調査後に不適格の場合、正規の発券料をご請求いたします。').'</p>
            <div class="checkbox checkbox-custom mb-0">
                <label class="d-flex align-items-start align-items-lg-center gap-3 ps-0 fw-bold w-xl-maxcontent">
                    <input name="is_childcare_education" type="checkbox" '. $checked .'> '. my_lang('育児・教育関連のイベントに間違いございません。').'
                </label>
            </div>
        </div> ';

        $html = $this->load->view("manager/event_fee/fee_modal", $data, TRUE);

        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function fee_item_change_deleted($id) {
        $data = $this->input->post();
        $this->m_event_fee_item->set_soft_delete(FALSE);
        $fee_item = $this->m_event_fee_item->get($data['item_id']);
        $this->m_event_fee_item->set_soft_delete(TRUE);
        if (empty($fee_item)) {
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
            $data_return["error"] = "error";
            $data_return["callback"] = 'deleted_fee_response';
            echo json_encode($data_return);
            return FALSE;
        }

        $ticket_events = $this->m_event_subscriber->get_total_ticket_event_by_id($fee_item->event_id);
        if ($ticket_events->all_ticket != 0) {
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
            $data_return["error"] = "error";
            $data_return["callback"] = 'deleted_fee_response';
            echo json_encode($data_return);
            return FALSE;
        }
        // if($fee_item->deleted == "1") {
        //     $this->m_event_fee_item->update($fee_item->id, ['deleted' => 0], TRUE);
        // } else {
        if ($fee_item->deleted == "0") {
            $this->m_event_fee_item->update($fee_item->id, ['deleted' => 1], TRUE);
            // $current_fee_data = $this->m_event_fee_data->get_list_filter([
            //     'm.event_id'    => $id,
            //     'i.item_type'   => $fee_item->item_type,
            //     'm.fee_item_id' => $fee_item->id
            // ], [], [], 0, 0, []);
            // $this->m_event_fee_data->update($current_fee_data[0]->id, ['deleted' => 1], TRUE);
        }

        echo json_encode([
            'state'         => 1,
            'max_position'  => 7,
            'msg'           => my_lang('操作が成功しました。'),
            'callback'      => "deleted_fee_response",
            'html'          => $this->get_content_fee_item($id, $data, isset($data['view_type']) ? $data['view_type'] : $this->_type_fee),
            'item_position' => isset($data['item_position']) ? $data['item_position'] : 1,
        ]);
    }

    public function temp_fee_item_save($id = '', $item_type = '') {
        $data = $this->input->post();
        $data['checkbox_change'] = 1;
        if (empty($item_type)) {
            $item_type = $this->_type_fee;
            $data_return['save_link'] = site_url("manager/event_fee/fee_item_save/" . $id);
            $data_return['title'] = my_lang('料金設定の確認画面');
        } else {
            $data_return['save_link'] = site_url("manager/event_fee/free_fee_item_save/" . $id);
            $data_return['title'] = my_lang('無料チケット設定の確認画面');
        }
        $data['event_id'] = intval($id);
        $event_fee_item = $this->m_event_fee_item->get_by_id($data['fee_item_id']);
        $data["is_hide_stock"] = $event_fee_item->is_hide_stock;
        $data["is_hide_price"] = $event_fee_item->is_hide_price;
        $data["is_hide_registration_limit"] = $event_fee_item->is_hide_registration_limit;
        $data_return["state"] = 1;
        $data_return["callback"] = 'save_event_fee_item_response';
        $data_return["action"] = "confirm";
        $data_return["error"] = [];
        $validate = $this->validate_event_fee_item($data, $item_type);
        if (!$validate['state']) {
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
            $data_return["error"] = $validate['error'];
            echo json_encode($data_return);
            return FALSE;
        }

        $this->session->set_userdata('temp_session_fee_item', $validate['data']);
        $data = $validate['data'];
        $data['items_data'] = [];
        $data['max_choice_text'] = $this->m_event_form_data->get_max_choice_text($data['max_choice']);
        foreach ($data['name_display'] as $key => $value) {
            $temp = [
                'fee_item_id'   => 0,
                'name_display'  => $value,
                'price'         => isset($data['price'][$key]) ? mb_convert_kana(trim($data['price'][$key]), 'n') : 0,
                'unit'          => $data['unit'][$key],
                'total_limit'   => mb_convert_kana(trim($data['total_limit'][$key]), 'n'),
                'total_stock'   => mb_convert_kana(trim($data['current_stock'][$key]), 'n'),
                'current_stock' => mb_convert_kana(trim($data['current_stock'][$key]), 'n'),
                'position'      => $data['position'][$key],
                'deleted'       => 1,
                'description'   => isset($data['fee_item_data_description'][$key]) ? $data['fee_item_data_description'][$key] : '',
            ];

            if ($temp['current_stock'] == '') {
                $temp['is_infinitive'] = 1;
            } else {
                $temp['is_infinitive'] = 0;
            }
            $data['items_data'][] = (object)$temp;
        }
        $fee_item = (object)$data;
        if(!$data['position']) {
            $data['not_checked'] = 'checked';
        }
        $data['fee_items'][] = $fee_item;
        $data['fee_view'] = 'confirm';
        $data['html_detail'] = $this->load->view("manager/event_fee/detail", $data, TRUE);
        $html = $this->load->view("manager/event_fee/confirm", $data, TRUE);
        $data_return["msg"] = my_lang('操作が成功しました。');
        $data_return['html'] = $html;

        echo json_encode($data_return);

    }

    public function temp_free_fee_item_save($id) {
        $item_type = $this->_type_free_fee;
        $this->temp_fee_item_save($id, $item_type);
    }

    public function free_fee_item_save($id) {
        $item_type = $this->_type_free_fee;
        $this->fee_item_save($id, $item_type);
    }

    /**
     * @param        $id
     * @param string $item_type
     *
     * @return bool
     */
    public function fee_item_save($id, $item_type = '') {
        if (empty($item_type)) {
            $item_type = $this->_type_fee;
        }
        $data_post = $this->input->post();
        $data = $this->session->userdata('temp_session_fee_item');
        $action = $this->input->get('action') ? $this->input->get('action') : 'next_step';
        $data['event_id'] = intval($id);
        $data_return["callback"] = 'save_event_fee_item_response';
        $data_return["action"] = $action;
        $data_return["error"] = [];

        $list_old_data = [];
        if (!empty($data['fee_item_id'])) {
            $list_old_data = $this->m_event_fee_item_data->get_list_filter([
                'fee_item_id' => $data['fee_item_id'],
            ], [], []);
        }
        $list_old_data = is_array($list_old_data) ? $list_old_data : [];
        $list_data_by_id = [];
        foreach ($list_old_data as $key_old => $value_old) {
            if (!empty($value_old->id)) {
                $list_data_by_id[$value_old->id] = $value_old;
            }
        }
        $validate = $this->validate_event_fee_item($data, $item_type);
        if (!$validate['state']) {
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
            $data_return["error"] = $validate['error'];
            echo json_encode($data_return);
            return FALSE;
        }
        $data = $validate['data'];
        if ($item_type == $this->_type_fee) {
            $data['item_type'] = 1;
        } else {
            $data['item_type'] = 0;
        }
        if (empty($data['fee_item_id'])) {
            $this->m_event_fee_item->set_soft_delete(FALSE);
            if($item_type == $this->_type_fee) {
                $count_position = $this->m_event_fee_item->count_by(['event_id' => $id, 'item_type' => 1]);
            } else if ($item_type == $this->_type_free_fee) {
                $count_position = $this->m_event_fee_item->count_by(['event_id' => $id, 'item_type' => 0]);
            }
            // $count_position = $this->m_event_fee_item->count_by(['event_id' => $id]);
            $this->m_event_fee_item->set_soft_delete(TRUE);
            $data['item_position'] = isset($count_position) ? ($count_position + 1) : 1;
        }
        //Insert, update event fee item
        $data['user_created'] = $this->session->userdata('user_id');
        if (isset($data['fee_item_id']) && $data['fee_item_id'] > 0) {
            $fee_item_id = $data['fee_item_id'];
            if (isset($data['is_deleted']) && $data['is_deleted']) {
                $this->m_event_fee_item->update($fee_item_id, ['deleted' => 0], TRUE);
                // $this->m_event_fee_data->set_soft_delete(FALSE);
                // $current_fee_data = $this->m_event_fee_data->get_list_filter([
                //     'm.event_id'    => $data['event_id'],
                //     'i.item_type'   => $data['item_type'],
                //     'm.fee_item_id' => $fee_item_id
                // ], [], [], 0, 0, []);
                // $this->m_event_fee_data->set_soft_delete(TRUE);
                // $this->m_event_fee_data->update($current_fee_1000data[0]->id, ['deleted' => 0], T`RUE);
            }
            $state = $this->m_event_fee_item->update($fee_item_id, $data);
        } else {
            $state = $fee_item_id = $this->m_event_fee_item->insert($data);
        }
        if ($state === FALSE) {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
            $data_return["error"] = $this->m_event_fee_item->get_validate_error();

            return $data_return;
        }
        $item_position = $data['item_position'];
        $data['id'] = $data['fee_item_id'] = $fee_item_id;
        //Insert, update event fee item data
        $fee_item_data_insert = [];
        $fee_item_data_update = [];
        $list_id = [];
        $is_stock_item = 1;
        foreach ($data['name_display'] as $key => $item) {
            $old_total_stock = 0;
            if (isset($data['fee_item_data_id'][$key]) && isset($list_data_by_id[$data['fee_item_data_id'][$key]])) {
                $old_data = $list_data_by_id[$data['fee_item_data_id'][$key]];
                if (!empty($old_data->is_infinitive) && !empty($data['current_stock'][$key])) {
                    $old_total_stock = $this->m_event_subscriber_fee_detail->get_total_stock_data($old_data->id);
                }
            }

            $current_stock = $data['current_stock'][$key] ? trim($data['current_stock'][$key]) : 0;
            if (empty($old_data->is_infinitive)) {
                $total_data_stock = !empty($old_total_stock) ? $old_total_stock + $current_stock : $current_stock;
            }

            $temp = [
                'fee_item_id'   => $fee_item_id,
                'name_display'  => $item,
                'price'         => isset($data['price'][$key]) ? $data['price'][$key] : 0,
                'unit'          => $data['unit'][$key],
                'total_limit'   => $data['total_limit'][$key],
                'total_stock'   => !empty($total_data_stock) ? $total_data_stock : '',
                'current_stock' => $current_stock,
                'position'      => $data['position'][$key],
                'deleted'       => 0,
                'description'   => isset($data['fee_item_data_description'][$key]) ? $data['fee_item_data_description'][$key] : '',
                'required_fields'   => isset($data['required_fields'][$key]) ? $data['required_fields'][$key] : 0,
            ];
            if ($data['current_stock'][$key] == '') {
                $is_stock_item = 0;
                $temp['is_infinitive'] = 1;
            } else {
                $temp['is_infinitive'] = 0;
            }
            if ($data['fee_item_data_id'][$key] > 0) {
                $list_id[$data['fee_item_data_id'][$key]] = $data['fee_item_data_id'][$key];
                $fee_item_data_update[$data['fee_item_data_id'][$key]] = $temp;
            } else {
                $fee_item_data_insert[] = $temp;
            }
        }
        //Delete form field data
        $this->m_event_fee_item_data->set_soft_delete(FALSE);//Get all row deleted + no delete
        $list_fee_item_data = $this->m_event_fee_item_data->get_many_by(['fee_item_id' => $fee_item_id]);
        $list_id_data = [];
        if (count($list_fee_item_data)) {
            foreach ($list_fee_item_data as $item) {
                if (!isset($fee_item_data_update[$item->id])) {
                    $this->m_event_fee_item_data->set_soft_delete(TRUE);
                    $this->m_event_fee_item_data->delete($item->id);
                }
                $list_id_data[$item->id] = $item;
            }
        }
        //Add form field data
        if (count($fee_item_data_insert)) {
            $state = $this->m_event_fee_item_data->insert_many($fee_item_data_insert, TRUE);
        }
        if (count($fee_item_data_update) && ((is_array($state) && count($state)) || $state !== FALSE)) {
            $state = [];
            foreach ($list_id as $tmp_id) {
                if (isset($list_id_data[$tmp_id]) && empty($list_id_data[$tmp_id]->is_infinitive)) {
                    if (!empty($fee_item_data_update[$tmp_id]['is_infinitive'])) {
                        $fee_item_data_update[$tmp_id]['total_stock'] = '';
                    } else {
                        $fee_item_data_update[$tmp_id]['total_stock'] = $list_id_data[$tmp_id]->total_stock + ($fee_item_data_update[$tmp_id]['current_stock'] - $list_id_data[$tmp_id]->current_stock);
                    }
                }
                $fee_item_data_update[$tmp_id]['total_stock'] = empty($fee_item_data_update[$tmp_id]['total_stock']) ? '' : $fee_item_data_update[$tmp_id]['total_stock'];
                if ($this->m_event_fee_item_data->update_many($tmp_id, $fee_item_data_update[$tmp_id], TRUE) === FALSE) {
                    $state = [];
                    break;
                }
                $state[] = $tmp_id;
            }
        }

            $this->session->unset_userdata("temp_session_fee_item");//Reset data
            $this->session->unset_userdata('temp_session_fee_shipping');//Reset data
            //Insert, update data shipping and và kiểm tra xem có data để update cho loại fee_shipping_type có phí mới vào còn ko là cho loại fee_shipping_type miễn phí
            if (isset($data['list_shipping_data']) && $data['list_shipping_data']) {
                $arr_shipping_data = json_decode($data['list_shipping_data'], TRUE);
                $this->m_event_fee_item_shipping->update_many($arr_shipping_data, ['fee_item_id' => $fee_item_id], TRUE);
            }
            //update data is_stock item_fee
            if (isset($is_stock_item)) {
                $this->m_event_fee_item->update($fee_item_id, ['is_stock_item' => $is_stock_item], TRUE);
            }
            if (!empty($is_stock_item)) {
                $this->m_event->update($id, ['is_stock' => 0], TRUE);
            }
            if(isset($data_post['is_hide_stock'])) {
                $this->m_event_fee_item->update($data['fee_item_id'], ['is_hide_stock' => 1], TRUE);
            } else {
                $this->m_event_fee_item->update($data['fee_item_id'], ['is_hide_stock' => 0], TRUE);
            }
            if(isset($data_post['is_hide_price'])) {
                $this->m_event_fee_item->update($data['fee_item_id'], ['is_hide_price' => 1], TRUE);
            } else {
                $this->m_event_fee_item->update($data['fee_item_id'], ['is_hide_price' => 0], TRUE);
            }
            if(isset($data_post['is_hide_registration_limit'])) {
                $this->m_event_fee_item->update($data['fee_item_id'], ['is_hide_registration_limit' => 1], TRUE);
            } else {
                $this->m_event_fee_item->update($data['fee_item_id'], ['is_hide_registration_limit' => 0], TRUE);
            }

            $data_return["data"] = $data;
            $fee_items = $this->event_lib->get_data_event_fee_items($id, ['is_get_del' => TRUE]);
            $item_ids = [];
            if (count($fee_items)) {
                foreach ($fee_items as $item) {
                    $item_ids[] = $item->fee_item_id;
                }
            }
            $data_return["item_ids"] = json_encode($item_ids);
            if ($action == 'next_step') {
                ksort($fee_items);
                if (count($fee_items)) {
                    foreach ($fee_items as $item) {
                        $item_positions[$item->item_position] = $item->deleted;
                    }
                    foreach ($item_positions as $key => $position) {
                        if($key > $item_position && $position == "1") {
                            $item_position = $key;
                        } else {
                            continue;
                        };
                    }
                }
                $data_return["html"] = $this->get_content_fee_item($id, [
                    'item_position' => $data_post['item_position'] + 1,
                ], $item_type);
            }
            if (!empty($data['is_childcare_education'])) {
                $this->m_event->update($id, ['is_childcare_education' => 1], TRUE);
            }else{
                $this->m_event->update($id, ['is_childcare_education' => 0], TRUE);
            }
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
        echo json_encode($data_return);

    }

    private function validate_event_fee_item($data, $item_type = '') {
        if (empty($item_type)) {
            $item_type = $this->_type_fee;
        }
        $data_return['state'] = 1;
//        $event_id = isset($data['event_id']) ? $data['event_id'] : 0;
//        $check_event = $this->event_lib->check_event_active($event_id);
//        if (!($check_event['state'])) {
//            $data_return["state"] = 0;
//            $data_return["msg"] = $check_event['msg'];
//            $data_return["error"] = [];
//            return $data_return;
//        }
        $schema = $this->m_event_fee_item->schema;
        $schema_item_data = $this->m_event_fee_item_data->schema;
        if ($item_type == $this->_type_fee) {
            $rules = isset($schema["is_fee_shipping"]["rules"]) ? $schema["is_fee_shipping"]["rules"] : '';
            $schema["is_fee_shipping"]["rules"] = empty($rules) ? 'required' : '|required';
            $schema_item_data["price"]["rules"] = "required|regex_match[/^[0-9]*$/]";
        }
        if (!$this->m_event_fee_item->validate($data, $schema)) {
            $data_return["state"] = 0;
            if (isset($data_return['error'])) {
                $data_return['error'] = array_merge($data_return['error'], $this->m_event_fee_item->get_validate_error());
            } else {
                $data_return['error'] = $this->m_event_fee_item->get_validate_error();
            }
        }
        foreach ($data as $key => $item) {
            if (is_array($item) || is_object($item)) {
                if ($key == 'name_display') {
                    for ($k = 0; $k < count($item); $k++) {
                        if ($item_type == $this->_type_fee) {
                            $list_name_display_validate = $data['name_display'];
                            unset ($list_name_display_validate[$k]);
                            $schema_item_data["name_display"]["rules"] = "required|callback_check_not_in_list" . json_encode(array_values($list_name_display_validate));
                        }
                        $required_fields = 0;
                        $required_fields  = trim($data['total_limit'][$k]) != 1 ? 1 : 0;
                        $data['required_fields'][$k] = $required_fields;
                        $state_validate = $this->m_event_fee_item_data->validate([
                            'name_display'      => $data['name_display'][$k] ? trim($data['name_display'][$k]) : '',
                            'price'             => isset($data['price'][$k]) ? trim($data['price'][$k]) : 0,
                            'unit'              => $data['unit'][$k] ? trim($data['unit'][$k]) : '',
                            'total_stock'       => $data['current_stock'][$k] ? trim($data['current_stock'][$k]) : '',
                            'current_stock'     => $data['current_stock'][$k] ? trim($data['current_stock'][$k]) : '',
                            'total_limit'       => $data['total_limit'][$k] ? trim($data['total_limit'][$k]) : '',
                            'required_fields'   => $required_fields,
                            'position'          => $data['position'][$k],
                        ], $schema_item_data);
                        if (!$state_validate) {
                            $data_return["state"] = 0;
                            $errors = $this->m_event_fee_item_data->get_validate_error();
                            $data_return['error']['position_' . $data['position'][$k]] = $errors;
                        }
                    }
                }
            } else {
                $data[$key] = $this->event_lib->format_data_textarea($item);
            }
        }

        $data_return['data'] = $data;
        if (isset($data['exists_in_detail']) && is_array($data['exists_in_detail'])) {
            foreach ($data['exists_in_detail'] as $key => $item) {
                $dataItem = explode("_", $item);
                if (isset($dataItem[0]) && isset($dataItem[1]) && isset($dataItem[2]) && in_array($dataItem[2],$data['position']) && $data['price'][$key]
                    && $dataItem[1] != $data['price'][$key]) {
                    $data_return['error']['position_' . ($dataItem[2])]['price'] = my_lang('すでに予約が入っており金額変更ができません。左のチェックを外し、新チケットを設定ください。');
                    $data_return['state'] = 0;
                }
            }
        }
        return $data_return;
    }

    private function get_content_fee_item($id = 0, $data = [], $html_type = '') {
        //Get content custom field in DB
        $event = $this->m_event->get_by(['id' => $id]);
        if (isset($event->type_ticket) && $event->type_ticket == '2') {
            $data['is_manual_event'] = true;
        } else {
            $data['is_manual_event'] = false;
        }

        $default_data = [];
        $default_data['item_position'] = 1;
        $data['is_get_del'] = TRUE;
        $data['is_get_del_from_event_edit'] = FALSE;
        $fee_items = $this->event_lib->get_data_event_fee_items($id, $data, $html_type);
        ksort($fee_items);
        $list_fee_position = array_keys($fee_items);
        $total_fee_item = (is_array($list_fee_position) && count($list_fee_position)) ? count($list_fee_position) : 0;
        if (empty($data['item_position'])) {
            $data['item_position'] = isset($list_fee_position[0]) ? $list_fee_position[0] : 1;
        }
        $data = array_merge($default_data, $data);
        $data['fee_items'] = $fee_items;
        // $this->m_event_fee_item->set_soft_delete(FALSE);
        $this->m_event_fee_item->set_soft_delete(FALSE);
        if($html_type == "fee") {
            $count_position = $this->m_event_fee_item->count_by(['event_id' => $id, 'item_type' => 1]);
        } else {
            $count_position = $this->m_event_fee_item->count_by(['event_id' => $id, 'item_type' => 0]);
        }
        // $count_position = $this->m_event_fee_item->count_by(['event_id' => $id]);
        $this->m_event_fee_item->set_soft_delete(TRUE);
        // if ($total_fee_item >= 7) {
        //     $data['item_position'] = $list_fee_position[6];
        // } else {
            $key = $total_fee_item - 1;
            for ($i = 0; $i <= (6 - $total_fee_item); $i++) {
                $list_fee_position[] = $count_position + 1;
                $key++;
                $count_position++;
            }
        // }
        $data['list_fee_position'] = $list_fee_position;
        // $data['item_first'] = $data['item_position'] == $list_fee_position[0] ? true : false;
        $data['fee_item_info'] = isset($data['fee_items'][$data['item_position']]) ? $data['fee_items'][$data['item_position']] : [];
        $fee_item_data = count($data['fee_item_info'])
            ? $this->db->select('m.*, i.item_position, i.ship_type, f.tax')
                ->select("CASE WHEN EXISTS (
                    SELECT 1 
                    FROM event_subscribers_fee_detail AS d 
                    JOIN event_subscribers ON d.subscriber_id = event_subscribers.id
                    WHERE d.fee_data_id = m.id 
                    AND d.total_price > 0 
                    AND d.quantity > 0
                    AND event_subscribers.deleted = 0
                  ) THEN TRUE ELSE FALSE END AS exists_in_detail", false) // check đã mua hàng hay chưa
                ->from('event_fee_items_data AS m')
                ->join('event_fee_items AS i', 'i.id = m.fee_item_id', 'left')
                ->join('event_fee AS f', 'f.id = i.fee_id', 'left')
                ->where_in('m.fee_item_id', $data['fee_item_info']->fee_item_id)
                ->order_by('m.position', 'ASC')
                ->order_by('m.id', 'ASC')
                ->get()
                ->result()
            : [];

        $data['fee_item_data'] = [];
        foreach ($fee_item_data as $item) {
            $data['fee_item_data'][$item->position] = $item;
        }
        $data['list_max_choice'] = $this->m_event_fee_item->get_max_choice($html_type);
        $data['fee_item_id'] = isset($data['fee_item_info']->fee_item_id) ? $data['fee_item_info']->fee_item_id : 0;
        $data['ship_type'] = isset($data['fee_item_info']->ship_type) ? $data['fee_item_info']->ship_type : 0;
        $list_shipping = [];
        if ($data['fee_item_id']) {
            $list_shipping_data = $this->m_event_fee_item_shipping->get_many_by(['fee_item_id' => $data['fee_item_id']]);
            foreach ($list_shipping_data as $item) {
                $list_shipping[] = $item->id;
            }
        } else {
            $data_session = $this->session->userdata('temp_session_fee_shipping');
            if (isset($data_session[$id][$data['item_position']])) {
                $list_shipping = $data_session[$id][$data['item_position']];
            }
        }
        //Check event
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!($check_event['state'])) {
//            $data['is_view'] = TRUE;
        $data['is_view'] = '';
//        }
        $data['list_shipping_data'] = count($list_shipping) ? json_encode($list_shipping) : '';
        $data['list_fee_shipping_choice'] = $this->m_event_fee_item->get_fee_shipping();
        $data['url_shipping'] = site_url("manager/event_fee/shipping_item/" . $id . '/' . $data['fee_item_id'] . '/' . $data['item_position']);
        $data['url_next'] = isset($data['url_next']) ? $data['url_next'] : site_url("manager/event_fee/view_fee_item/" . $id);
        $data['view_type'] = $html_type;
        return $this->load->view("manager/event_fee/fee_item", $data, TRUE);
    }

    public function view_fee_item($id) {
        $data = $this->input->post();
        echo json_encode([
            'state'         => 1,
            'max_position'  => 7,
            'callback'      => "change_fee_item",
             'html'          => $this->get_content_fee_item($id, $data, isset($data['view_type']) ? $data['view_type'] : $this->_type_fee),
            'item_position' => isset($data['item_position']) ? $data['item_position'] : 1,

        ]);

    }

    public function shipping_item($id, $item_id = 0, $position = 1) {
        $isShow = $this->input->get('isShow');
        if(isset($isShow) && $isShow != 2){
            echo json_encode([
                'state' => 0,
                'callback' => 'show_error_when_select_shipping_item',
                'msg' => $isShow == 1 ? my_lang('「送料込み」が選択されています。') : my_lang('「送料無し」が選択されています。'),
            ]);
            return FALSE;
        }
        $data = [];
        $data['event_id'] = $id;
        $data['fee_item_id'] = $item_id;
        $data['fee_item_info'] = [];
        $this->m_event_fee_item_shipping->set_soft_delete(FALSE);
        if ($item_id) {
            $data['fee_item_info'] = $this->m_event_fee_item->get($item_id);
            $fee_item_shipping = $this->m_event_fee_item_shipping->get_many_by([
                'event_id'    => $id,
                'fee_item_id' => $item_id,
            ]);
        } else {
            $fee_item_shipping = [];
            $data_session = $this->session->userdata('temp_session_fee_shipping');
            if (isset($data_session[$id][$position])) {
                $fee_item_shipping = $this->m_event_fee_item_shipping->get_many($data_session[$id][$position]);
            }
        }
        $shipping_data = [];
        $type = '';
        if (count($fee_item_shipping)) {
            foreach ($fee_item_shipping as $item) {
                if (!$item->deleted) {
                    $type = $item->type;
                }
                if (isset($shipping_data[$item->type])) {
                    $i = count($shipping_data[$item->type]) + 1;
                    if ($item->type == 2 && $item->max_cost == -1) {
                        $i = 5;
                    }
                } else {
                    $i = 1;
                }
                if ($item->city_id) {
                    $shipping_data[$item->type][$item->city_id] = $item;
                } else {
                    $shipping_data[$item->type][$i] = $item;
                }
            }
        }

        $data['shipping_data'] = $shipping_data;
        $data['type_current'] = $type;
        $data['list_city'] = $this->m_city_shipping->get_all();

//        $check_event = $this->event_lib->check_event_active($id);
//        if (!($check_event['state'])) {
        $data['is_view'] = '';
//            $data['is_view'] = TRUE;
//        }
        $data['save_link'] = site_url("manager/event_fee/shipping_item_save/" . $id . '/' . $item_id . '/' . $position);
        $html = $this->load->view("manager/event_fee/form_shipping", $data, TRUE);

        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function shipping_item_save($id, $item_id = 0, $position = 1) {
        $data = $this->input->post();
        $data_return["callback"] = 'save_event_fee_item_shipping_response';
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!$check_event['state']) {
//            $data_return["msg"] = $check_event['msg'];
//            $data_return["state"] = 0;
//            $data_return["error"] = [];
//
//            echo json_encode($data_return);
//            return FALSE;
//        }
        $validate = $this->m_event_fee_item_shipping->validate_fee_item_shipping($data);
        if (!$validate['state'] || !isset($validate['data'])) {
            $data_return['state'] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode(array_merge($data_return, $validate));
            return FALSE;
        }
        $type = $data['type'];
        $data = $validate['data'];
        $fee_item_id = $item_id;
        if (!$item_id) {
            $fee_item = $this->m_event_fee_item->get_by([
                'event_id'      => $id,
                'item_position' => $position,
            ]);
            $fee_item_id = (isset($fee_item->fee_item_id)) ? $fee_item->fee_item_id : 0;
        }
        if ($fee_item_id) {
            $schema = $this->m_event_fee_item_shipping->schema;
            $this->m_event_fee_item_shipping->schema = [];
            $this->m_event_fee_item_shipping->update_by([
                'event_id'    => $id,
                'fee_item_id' => $fee_item_id,
            ], ['deleted' => 1]);
            $this->m_event_fee_item_shipping->schema = $schema;
        }
        $list_id = [];
        if (count($data)) {
            foreach ($data as $item) {
                $item['fee_item_id'] = $fee_item_id;
                if ($item['id']) {
                    $list_id[] = $item['id'];
                    $this->m_event_fee_item_shipping->update($item['id'], $item, TRUE);
                } else {
                    $list_id[] = $this->m_event_fee_item_shipping->insert($item, TRUE);
                }
            }
        }
        $data_session = $this->session->userdata('temp_session_fee_shipping');
        $data_session[$id][$position] = $list_id;
        $this->session->set_userdata('temp_session_fee_shipping', $data_session);
        $data_return['state'] = 1;
        $data_return['type'] = $type;
        $data_return['list_shipping_id'] = json_encode($list_id);
        $data_return['error'] = $this->m_event_fee_item_shipping->get_validate_error();
        $data_return["msg"] = my_lang('操作が成功しました。');

        echo json_encode($data_return);
    }

    public function form_receiver_banking($id) {
        $data = [];
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!($check_event['state'])) {
//            $data['content'] = $this->detail_receiver_banking_content($id);
//        } else {
        $data['content'] = $this->form_receiver_banking_content($id);
//        }
        $html = $this->load->view("manager/event_fee/fee_form_modal", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    protected function form_receiver_banking_content($id) {
        $data = [];
        $data['receiver_banking'] = $this->m_event_fee_banking->get_by(['event_id' => $id]);
        $data['save_link'] = site_url('manager/event_fee/form_receiver_banking_save/' . $id);
        return $this->load->view("manager/event_fee/form_receiver_banking", $data, TRUE);
    }

    public function receiver_banking_detail($id) {
        $data_return = [];
        $data_return['content'] = $this->detail_receiver_banking_content($id);
        $html = $this->load->view("manager/event_fee/fee_form_modal", $data_return, TRUE);

        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    protected function detail_receiver_banking_content($id) {
        $data = [];
        $data['receiver_banking'] = $this->m_event_fee_banking->get_by(['event_id' => $id]);

        if (!empty($data['receiver_banking']->id)) {
            $data['receiver_banking']->type = $this->m_event_fee_banking->get_tranfer_type_text(
                $data['receiver_banking']->type
            );
        }
        return $this->load->view("manager/event_fee/detail_receiver_banking", $data, TRUE);
    }

    public function form_receiver_banking_save($id) {
        $data = $this->input->post();
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!$check_event['state']) {
//            echo json_encode($check_event);
//            return FALSE;
//        }
        $validate = $this->m_event_fee_banking->validate($data);
        if (!$validate) {
            $data_return = [];
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作は失敗しました。');
            $data_return['error'] = $this->m_event_fee_banking->get_validate_error();
            echo json_encode($data_return);
            return FALSE;
        }
        $is_receiver_banking = $this->m_event_fee_banking->get_by(['event_id' => $id]);
        $data['event_id'] = $id;
        if (!$is_receiver_banking) {
            $data['user_created'] = $this->session->userdata('user_id');
            $data['time_created'] = date('Y-m-d H:i:s');
            $insert = $this->m_event_fee_banking->insert($data);
            if (!$insert) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                $data_return['error'] = $this->m_event_fee_banking->get_validate_error();
                echo json_encode($data_return);
                return FALSE;
            }
        } else {
            $data['user_modified'] = $this->session->userdata('user_id');
            $data['time_modified'] = date('Y-m-d H:i:s');
            $update = $this->m_event_fee_banking->update_by(['event_id' => $id], $data);
            if (!$update) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                $data_return['error'] = $this->m_event_fee_banking->get_validate_error();
                echo json_encode($data_return);
                return FALSE;
            }
        }

        echo json_encode([
            'callback'  => 'form_setting_fee_response',
            'state'     => 1,
            'form_type' => 'BANKING_CARD',
            'msg'       => my_lang('操作が成功しました。'),
        ]);
    }

    public function form_receiver_cashier($id) {
        $data = [];
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!($check_event['state'])) {
//            $data['content'] = $this->detail_receiver_cashier_content($id);
//        } else {
        $data['content'] = $this->form_receiver_cashier_content($id);
//        }
        $html = $this->load->view("manager/event_fee/fee_form_modal", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function form_receiver_combini($id) {
        $data = [];
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!($check_event['state'])) {
//            $data_detail['receiver_combini'] = $this->m_event_fee_combini->get_by(['event_id' => $id]);
//            $data['content'] = $this->load->view("manager/event_fee/detail_receiver_combini", $data_detail, TRUE);
//        } else {
        $data['content'] = $this->form_receiver_combini_content($id);
//        }
        $html = $this->load->view("manager/event_fee/fee_form_modal", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    protected function form_receiver_cashier_content($id) {
        $data = [];
        $data['receiver_cashier'] = $this->m_event_fee_cashier->get_by(['event_id' => $id]);
        $data['list_hours'] = $this->event_lib->get_data_hours();
        $data['list_minutes'] = $this->event_lib->get_data_minutes();
        $data['save_link'] = site_url('manager/event_fee/form_receiver_cashier_save/' . $id);
        return $this->load->view("manager/event_fee/form_receiver_cashier", $data, TRUE);
    }

    protected function form_receiver_combini_content($id) {
        $data = [];
        $data['receiver_combini'] = $this->m_event_fee_combini->get_by(['event_id' => $id]);
        $data['save_link'] = site_url('manager/event_fee/form_receiver_combini_save/' . $id);
        return $this->load->view("manager/event_fee/form_receiver_combini", $data, TRUE);
    }

    public function receiver_cashier_detail($id) {
        $data_return = [];
        $data_return['content'] = $this->detail_receiver_cashier_content($id);
        $html = $this->load->view("manager/event_fee/fee_form_modal", $data_return, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function receiver_combini_detail($id) {
        $data_return = [];
        $data['receiver_combini'] = $this->m_event_fee_combini->get_by(['event_id' => $id]);
        $data_return['content'] = $this->load->view("manager/event_fee/detail_receiver_combini", $data, TRUE);
        $html = $this->load->view("manager/event_fee/fee_form_modal", $data_return, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    protected function detail_receiver_cashier_content($id) {
        $data = [];
        $data['receiver_cashier'] = $this->m_event_fee_cashier->get_by(['event_id' => $id]);
        $data['list_hours'] = $this->event_lib->get_data_hours();
        $data['list_minutes'] = $this->event_lib->get_data_minutes();
        return $this->load->view("manager/event_fee/detail_receiver_cashier", $data, TRUE);
    }

    public function form_receiver_combini_save($id) {
        $data = $this->input->post();
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!$check_event['state']) {
//            echo json_encode($check_event);
//            return FALSE;
//        }
        $validate = $this->m_event_fee_combini->validate($data);
        if (!$validate) {
            $data_return = [];
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作は失敗しました。');
            $data_return['error'] = $this->m_event_fee_combini->get_validate_error();
            echo json_encode($data_return);
            return FALSE;
        } else {
            // max length reception_place
            $max_length_reception_place = 42;
            if($data['reception_place'] && strlen($data['reception_place']) > $max_length_reception_place) {
                $data_return = [];
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                echo json_encode($data_return);
                return FALSE;
            }
        }
        $is_receiver_combini = $this->m_event_fee_combini->get_by(['event_id' => $id]);
        $data['event_id'] = $id;
        if (!$is_receiver_combini) {
            $data['user_created'] = $this->session->userdata('user_id');
            $data['time_created'] = date('Y-m-d H:i:s');
            $insert = $this->m_event_fee_combini->insert($data);
            if (!$insert) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                $data_return['error'] = $this->m_event_fee_combini->get_validate_error();
                echo json_encode($data_return);
                return FALSE;
            }
        } else {
            $data['user_modified'] = $this->session->userdata('user_id');
            $data['time_modified'] = date('Y-m-d H:i:s');
            $update = $this->m_event_fee_combini->update_by(['event_id' => $id], $data);
            if (!$update) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                $data_return['error'] = $this->m_event_fee_combini->get_validate_error();
                echo json_encode($data_return);
                return FALSE;
            }
        }
        echo json_encode([
            'callback'  => 'form_setting_fee_response',
            'state'     => 1,
            'form_type' => 'COMBINI',
            'msg'       => my_lang('操作が成功しました。'),
        ]);
    }

    public function form_receiver_cashier_save($id) {
        $data = $this->input->post();
        if (isset($data['reception_place']) && $data['reception_place'] != 1) {
            $data['reception_place'] = $data['reception_place_value'];
        }
        unset($data['reception_place_value']);
        if (isset($data['time_begin'][0]) && $data['time_begin'][0]) {
            $data = $this->process_date_receiver_cashier($data);
        }
//        $check_event = $this->event_lib->check_event_active($id);
//        if (!$check_event['state']) {
//            echo json_encode($check_event);
//            return FALSE;
//        }
        $validate = $this->m_event_fee_cashier->validate($data);
        if (!$validate) {
            $data_return = [];
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作は失敗しました。');
            $data_return['error'] = $this->m_event_fee_cashier->get_validate_error();
            echo json_encode($data_return);
            return FALSE;
        }
        $is_receiver_banking = $this->m_event_fee_cashier->get_by(['event_id' => $id]);
        $data['event_id'] = $id;
        if (!$is_receiver_banking) {
            $data['user_created'] = $this->session->userdata('user_id');
            $data['time_created'] = date('Y-m-d H:i:s');
            $insert = $this->m_event_fee_cashier->insert($data);
            if (!$insert) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                $data_return['error'] = $this->m_event_fee_cashier->get_validate_error();
                echo json_encode($data_return);
                return FALSE;
            }
        } else {
            $data['user_modified'] = $this->session->userdata('user_id');
            $data['time_modified'] = date('Y-m-d H:i:s');
            $update = $this->m_event_fee_cashier->update_by(['event_id' => $id], $data);
            if (!$update) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作は失敗しました。');
                $data_return['error'] = $this->m_event_fee_cashier->get_validate_error();
                echo json_encode($data_return);
                return FALSE;
            }
        }

        echo json_encode([
            'callback'  => 'form_setting_fee_response',
            'state'     => 1,
            'form_type' => 'CASHIER',
            'msg'       => my_lang('操作が成功しました。'),
        ]);
    }

    public function check_time_receiver_cashier($str, $field) {
        return $this->event_lib->check_subscribe_time_event($str, $field);
    }

    public function process_date_receiver_cashier($data = array()) {
        $arr_time = [
            'time_begin',
            'time_end',
        ];
        foreach ($data as $key => $item) {
            if (in_array($key, $arr_time) && is_array($item) && count($item)) {
                $string = '';
                $count = count($item);
                foreach ($item as $key_i => $value) {
                    $value = trim($value);
                    $value = $value ? $value : (($count > 2 && $key_i == 0) ? '0000/00/00' : '00');
                    $delimiter = (strlen($value) <= 2) ? ":" : " ";
                    $delimiter = ($key_i == (count($item) - 1)) ? "" : $delimiter;
                    $string .= $value . $delimiter;
                }
                $data[$key] = $string;
            }
        }

        return $data;
    }

    public function check_range_cost($str, $field) {
        return isset($this->form_validation->validation_data[$field])
            ? ($str <= $this->form_validation->validation_data[$field])
            : FALSE;
    }

    public function is_check_set_ship_fee($str) {
        return $this->event_lib->is_check_set_ship_fee($str);
    }

    public function check_not_in_list($str, $arr) {
        return $this->event_lib->check_not_in_list($str, $arr);
    }

    public function is_check_limit_less_than_equal_to_total_stock($str, $field) {
        return $this->event_lib->is_check_limit_less_than_equal_to_total_stock($str, $field);
    }

    public function is_phone($str) {
        return $this->application_lib->is_phone($str);
    }
}
