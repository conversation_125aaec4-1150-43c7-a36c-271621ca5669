<?php

/**
 * Created by PhpStorm.
 * User: ha
 * Date: 06/28/2016
 * Time: 2:06 PM
 *
 * @property M_user            $model
 */
class User_list extends Manager_base
{

    function __construct()
    {
        parent::__construct();
        $this->load_more_css("assets/manager/css/event_list.css");
        $this->load_more_js("assets/manager/js/event_list.js", TRUE);
        $this->set_data_part("title", mysqli_error_list('イベント一覧'), FALSE);
        $this->set_data_part("top_bar", ['active' => 'offset2']);
    }

    /**
     * Setting $this->name variable
     *
     */
    function setting_class()
    {
        $this->name = array(
            "class"  => "manager/user_list",
            "view"   => "manager/event_list",
            "model"  => "m_user",
            "object" => "User",
        );
    }

    public $graph_per_page = 2; // the number of graphs displayed per page
    public $graph_time_interval = 3; // display the number of subscribers in 5 days by default
    private $admin_page = 'manager/event_list/index';
    public $item_per_page = 100;

    public function index()
    {
        $data["action_header"] = $this->_load_function_buttons(3);
        $data["view_file"] = $this->path_theme_view . "event_list/manager_container";
        $data["ajax_data_link"] = site_url($this->name["class"] . "/ajax_list_data?active=1");
        $this->manager($data);
    }

    protected function get_filter_raw_condition()
    {
        $data = parent::get_filter_raw_condition();
        $data_get = $this->input->get();
        if ($data_get) {
            foreach ($data_get as $key => $value) {
                $data['filter'][$key] = $value;
            }
        }
        return $data;
    }

    /**
     * Add column to manager table
     * default this function will add 2 column (check at head and action button at last)
     *
     * @param array $columns Array of column
     *
     * @return array array of list column
     */
    protected function get_column_data($columns = array())
    {
        $columns = array();
        $schema_column = $this->model->get_table_field();
        $add_column = array('id' => array(
            'field' => 'id',
            'db_field' => 'm.id',
            'label' => my_lang('アカウントID'),
            'rules' =>'required',
            'table' => array(),
        ));
        $schema_column = $add_column + $schema_column;
        $columns = array_merge($columns, $schema_column);

        if (isset($columns['email'])) {
            $columns["email"]['table'] = array(
                'class'                => 'sort',
            );
            $columns["email"]['url'] = 'manager/event_detail/index_top/';
        }

        if (isset($columns['created_on'])) {
            $columns['created_on']['label'] =  "作成日";
        }
        /* Thêm cột action */
        $columns["custom_action_edit"] = array(
            'label' => my_lang('ログイン'),
            'class'       => 'e_ajax_confirm',
            'confirm_msg' => my_lang('[削除] 処理いたしますか？'),
            'table' => array(
                'callback_render_data' => 'add_login_button',
                'class'                => 'no-wrap center disable_sort',
            ),
            'url'   => 'manager/event_detail/index_top/',
        );
        unset($columns['email_conf']);
        return $columns;
    }

    protected function add_login_button($origin_column_value, $column_name, &$record, $column_data, $caller)
    {
        $primary_key = $this->model->get_primary_key();
        $custom_action = "<div class='action-buttons'>";
        $custom_action .= "<a class='" . $column_data['class'] . " ' 
                       data-msg='". my_lang('ログインしますか？')."'
                       data-button='". my_lang('実行')."'
                       data-button-url='" . site_url("manager/user_list/check/" . $record->$primary_key) . "'
                       data-button-class='e_ajax_link'
                       href='#'>" . $column_data['label'] . "</a>";
        $custom_action .= "</div>";
        return $custom_action;
        return $custom_action;
    }

    /**
     * Load the view of function buttons into a new page
     *
     * @param $page : for index is 1, graph single is 2, graph double is 3
     *
     * @return: html code of the buttons
     */
    function _load_function_buttons($page)
    {
        $graph_btn_status = "unfocus_btn";
        $list_btn_status = "unfocus_btn";
        $list_user_btn_status = "unfocus_btn";

        if ($page == 1) {
            // when list button is focus
            $list_btn_status = "focus_btn";
        } else if ($page == 2) {
            // when graph button is focus
            $graph_btn_status = "focus_btn";
        } else if ($page === 3) {
            // when graph button is focus
            $list_user_btn_status = "focus_btn";
        }


        $data = array(
            "graph_btn_status" => $graph_btn_status,
            "list_btn_status"  => $list_btn_status,
            "list_user_btn_status"  => $list_user_btn_status,
            "page"             => $page,
        );
        $buttons = $this->load->view("manager/event_list/function_button", $data, TRUE);
        return $buttons;
    }

    /**
     * parse datetime from string returned by mysql
     *
     * @param $date_str
     *
     * @return DateTime|null
     */
    protected function parse_date($date_str)
    {
        if (strtotime($date_str) > 0) {
            try {
                $date = new DateTime($date_str);
            } catch (Exception $e) {
                $date = NULL;
            }
        } else {
            $date = NULL;
        }

        return $date;
    }

    /**
     * Return an array of label of fields, which have table field inside, in schema Event
     *
     * @return array
     */
    private function get_column_names()
    {
        $schemas = $this->model->schema;
        $header = array();

        foreach ($schemas as $key => $schema) {
            if (array_key_exists('table', $schema)) {
                $header[$schema['field']] = $schema['label'];
            }
        }

        return $header;
    }

    // AnhLD
    protected function get_order_data($condition)
    {
        $order = isset($condition["order"]) ? $condition["order"] : NULL;
        if (!$order || $order == "") {
            $order_session = $this->session->userdata("user_list_index_order_data");
            if ($order_session) {
                $order = $order_session;
            }
        } else {
            $this->session->set_userdata("user_list_index_order_data", $condition['order']);
        }
        $condition['order'] = $order;
        return parent::get_order_data($condition);
    }


    public function check($id = 0)
    {
        if (isset($id)) {
            $dataReturn = array();
            $dataReturn["callback"] = "login_response_remake";
            $user = $this->ion_auth->get_user_by_id($id);
            $pass = true;
            $this->config->load('site_settings');
            $current_domain = $this->config->item('promoter_site_url');
            $email = $user->email;
            $login = $this->ion_auth->login_special($email, $pass);
            if ($login) {
                //check first login
                $last_login = isset($user->last_login) ? $user->last_login : 0;
                if ($last_login) {
                    $accessed_url = $this->session->userdata('accessed_url');
                    if (isset($accessed_url)) {
                        $redirect = site_url($this->session->userdata('accessed_url'));
                    } else {
                        $redirect = site_url($this->admin_page);
                    }
                } else {
                    $this->session->set_userdata('is_first_login', TRUE);
                    $redirect = site_url('manager/event_list/graph/double');
                }
                $this->session->set_userdata('old_last_login', $last_login);
                $this->session->set_userdata('login_special', true);
                //Get all children of user
                $current_parent_path = isset($user->parent_path) ? $user->parent_path : '';
                $this->session->set_userdata('current_parent_path', $current_parent_path);
                $this->session->set_userdata('current_domain', $current_domain);

                $dataReturn["state"] = 1;
                $dataReturn["msg"] = "Login success";
                $dataReturn["redirect"] = $redirect;
            } else {
                $dataReturn["state"] = 0;
                $dataReturn["msg"] = my_lang('メールアドレス / パスワードが一致いたしません。');
            }
            echo json_encode($dataReturn);
        } else {
            redirect();
        }
    }

    public function login_admin_special()
    {
        $email = $this->session->userdata('email_admin');
        if ($email) {
            $dataReturn = array();
            $dataReturn["callback"] = "login_response_remake";
            $user = $this->ion_auth->get_user($email);
            $pass = true;
            $this->config->load('site_settings');
            $current_domain = $this->config->item('promoter_site_url');
            $email = $user->email;
            $login = $this->ion_auth->login_special($email, $pass);
            if ($login) {
                //check first login
                $last_login = isset($user->last_login) ? $user->last_login : 0;
                if ($last_login) {
                    $accessed_url = $this->session->userdata('accessed_url');
                    if (isset($accessed_url)) {
                        $redirect = site_url($this->session->userdata('accessed_url'));
                    } else {
                        $redirect = site_url($this->admin_page);
                    }
                } else {
                    $this->session->set_userdata('is_first_login', TRUE);
                    $redirect = site_url('manager/event_list/graph/double');
                }
                $this->session->set_userdata('old_last_login', $last_login);
                $this->session->unset_userdata('login_special');
                //Get all children of user
                $current_parent_path = isset($user->parent_path) ? $user->parent_path : '';
                $this->session->set_userdata('current_parent_path', $current_parent_path);
                $this->session->set_userdata('current_domain', $current_domain);

                $dataReturn["state"] = 1;
                $dataReturn["msg"] = "Login success";
                $dataReturn["redirect"] = $redirect;
            } else {
                $dataReturn["state"] = 0;
                $dataReturn["msg"] = my_lang('メールアドレス / パスワードが一致いたしません。');
            }
            echo json_encode($dataReturn);
        } else {
            redirect();
        }
    }
}
