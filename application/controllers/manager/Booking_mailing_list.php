<?php

/**
 * Class Booking_mailing_list
 *
 * @property M_event                      m_event
 * @property Mailing_lib                  mailing_lib
 * @property Email_lib                    email_lib
 * @property M_mailing_detail             m_mailing_detail
 * @property M_mail_template              m_mail_template
 * @property M_mailing_subscriber_history m_mailing_subscriber_history
 */
class Booking_mailing_list extends Manager_base {

    /**
     * constructor
     */
    function __construct() {
        parent::__construct();

        // load libraries
        $this->load->library("Mailing_lib");
        $this->load->library("Email_lib");
        $this->load_more_css("assets/manager/css/event.css");
        $this->load_more_css("assets/manager/css/mailing.css");
        $this->load_more_css("assets/manager/css/booking_mailing_detail.css");
        $this->load->model("m_event_form_field");
        $this->load->model("m_mailing_detail");
        $this->load->model("m_mail_template");
        $this->load->model("m_mailing_subscriber_history");
        $this->load->model("m_event");
        $this->set_data_part("top_bar", ['active' => 'offset3']);
        $this->set_data_part("title", my_lang('送信済みメール'), FALSE);
        $this->model->set_before_get('_mail_table');
    }

    /**
     * Setting $this->name variable
     *
     */
    function setting_class() {
        $this->name = Array(
            "class"  => "manager/Booking_mailing_list",
            "view"   => "manager/Booking_mailing_list",
            "model"  => "m_booking_mailing",
            "object" => "Booking mailing",
        );
    }

    /**
     * Set up schedule to send email to a group of customers
     */
    function index() {
        $data = array();
        // load step nav
        $step_nav = $this->mailing_lib->load_step_nav(4);
        $data['step_nav'] = $step_nav;
        $data["view_file"] = $this->path_theme_view . "email_template/manager_container";
        $this->manager($data);
    }

    protected function get_column_data($columns = Array()) {
        $columns = Array();
        $schema_column = $this->model->get_table_field();
        $columns = array_merge($columns, $schema_column);
        $columns["custom_action_detail"] = Array(
            'label' => my_lang("詳細"),
            'class' => '',
            'table' => Array(
                'callback_render_data' => 'add_detail_button',
                'class'                => 'no-wrap center disable_sort',
            ),
            'url'   => 'manager/booking_mailing_detail/index/',
        );
        return $columns;
    }


    protected function add_detail_button($origin_column_value, $column_name, &$record, $column_data, $caller) {
        $primary_key = $this->model->get_primary_key();
        $custom_action = "<div class='action-buttons'>";
        $custom_action .= "<a class='text-decoration-none'
                                href='" . site_url($column_data['url'] . $record->$primary_key) . "'>
                              <svg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'>
							    <g id='Group_1511' data-name='Group 1511' transform='translate(-912 -1397)'>
							    <rect id='Rectangle_852' data-name='Rectangle 852' width='20' height='20' transform='translate(912 1397)' fill='#fff' opacity='0'/>
							    <path id='description_24dp_5F6368_FILL0_wght400_GRAD0_opsz24' d='M164-864h8v-2h-8Zm0-4h8v-2h-8Zm-2,8a1.926,1.926,0,0,1-1.413-.588A1.926,1.926,0,0,1,160-862v-16a1.926,1.926,0,0,1,.587-1.412A1.926,1.926,0,0,1,162-880h8l6,6v12a1.926,1.926,0,0,1-.587,1.412A1.926,1.926,0,0,1,174-860Zm7-13v-5h-7v16h12v-11Zm-7-5v0Z' transform='translate(754 2277)'/>
							    </g>
							    </svg>
							    <span class='position-relative' style='top:3px'>" . $this->mailing_lib->format_event_name(my_lang('詳細'), '') . "</span></a>";
        $custom_action .= "</div>";
        return $custom_action;
    }

    public function export_excel() {
        $data = [];
        // load library
        $this->load->library('Excel');

        // get the list of events
        $events = $this->model->get_list_filter(
            [],
            NULL, NULL
        );

        $columns = $this->get_column_names();
        foreach ($events as $event) {
            $tmp = array();
            foreach ($columns as $column_name => $column_label) {
                if (isset($event->$column_name)) {
                    // if the value of a column is set
                    if ($column_name === "status") {
                        // if the column_name is status
                        // translate status value to japanese before push it into data array
                        array_push($tmp, $this->model->get_status_text($event->status));
                    } else {
                        array_push($tmp, $event->$column_name);
                    }
                } else {
                    array_push($tmp, '');
                }
            }

            $data[] = $tmp;
        }

        $this->excel->write_excel($columns, $data);
    }

    /**
     * Return an array of label of fields, which have table field inside, in schema Event
     *
     * @return array
     */
    private function get_column_names() {
        $schemas = $this->model->schema;
        $header = Array();
        foreach ($schemas as $key => $schema) {
            if (array_key_exists('table', $schema)) {
                $header[$schema['field']] = $schema['label'];
            }
        }

        return $header;
    }
}