<?php

/**
 * Class Mailing Fee
 *
 * @property Mailing_lib   mailing_lib
 * @property M_mailing_fee m_mailing_fee
 */
class Mailing_fee extends Manager_layout {

    function __construct() {
        parent::__construct();
        $this->load->model('m_mailing_fee');
        $this->load_more_css("assets/manager/css/event.css");
        $this->load_more_css("assets/manager/css/mailing.css");
        $this->load_more_css("assets/manager/css/mailing_fee.css");

        // load libraries
        $this->load->library("Mailing_lib");
        $this->set_data_part("top_bar", ['active' => 'offset3']);
        $this->set_data_part("title", my_lang('メール作成'), FALSE);
    }

    /**
     * create new email template
     */
    function index() {
        $data = array();
        $this->set_data_part("title", my_lang('メール作成'), FALSE);
        $step_nav = $this->mailing_lib->load_step_nav(5);
        $data['step_nav'] = $step_nav;
        $data['mailing_fees'] = $this->m_mailing_fee->get_all();
        $content = $this->load->view("manager/mailing_fee/fee", $data, TRUE);
        $this->show_page($content);
    }
}