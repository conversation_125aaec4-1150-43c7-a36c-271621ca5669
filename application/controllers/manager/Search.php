<?php

/**
 * Created by PhpStorm.
 * User: li
 * Date: 12/07/2016
 * Time: 14:09
 * @property M_event_subscriber m_event_subscriber
 * @property M_event            m_event
 */
class Search extends Manager_layout {

    function __construct() {
        parent::__construct();
        $this->load->model('m_event');
    }

    public function result() {
        $data = Array(
            'searching_cat' => ' ',
            'keyword'       => ' ',
        );
        $event_id = $this->input->post('event_id');
        $keyword = $this->input->post('keyword');

        $data['searching'] = my_lang('全てのイベント');
        if ($keyword != '') {
            $keyword = trim($keyword);
            $data['keyword'] = $keyword;
            if(preg_match("@[ 　]@u", $keyword, $match)) {
                $keyword   = trim(preg_replace('@[ 　]@u', '', $data['keyword']));
                $keyword_2 = trim(preg_replace('@[ 　]@u', '[　 ]+', $data['keyword']));
                $this->session->set_userdata('keyword_search_2', $keyword_2);
            } else {
                $len = mb_strlen($keyword, 'UTF-8');
                $keyword_2 = "";
                for ($i = 0; $i < $len; $i++) {
                    if($i < $len - 1) {
                        $keyword_2 .= mb_substr($keyword, $i, 1, 'UTF-8') . "[　 ]*"; 
                    } else if ($i == $len - 1) {  
                        $keyword_2 .= mb_substr($keyword, $i, 1, 'UTF-8');
                    }
                }
                $this->session->set_userdata('keyword_search_2', $keyword_2);
            }
            $this->session->set_userdata('keyword_search', $keyword);
        }

        $events = $this->result_search_event($this->input->post('keyword'));
        $where = [];
        $this->m_event->unset_before_get('custom_select_count_subscriber');
        $this->m_event->unset_before_get('custom_format_select');
        if ($event_id) {
            $event = $this->m_event->get($event_id);
            $data['searching'] = isset($event->name) ? $event->name : my_lang('全てのイベント');
            $where['m.id'] = $event_id;
        }
        $this->m_event->unset_before_get('custom_select_count_subscriber');
        $this->m_event->unset_before_get('custom_format_select');
        $this->m_event->set_before_get('custom_search_events');
        $search_result = $this->m_event->get_list_filter($where, [], []);
        $data['search_result'] = array_merge($events, $search_result);
        $html = $this->load->view("manager/search/result", $data, TRUE);
        echo json_encode([
            'callback' => 'default_ajax_link',
            'state'    => 1,
            'html'     => $html,
        ]);
    }

    public function result_search_event($keyword) {
        if (empty($keyword) || !is_string($keyword)) {
            return [];
        }

        $keyword = mb_convert_kana(trim($keyword), 'ASKV', 'UTF-8');
        if ($keyword === '') {
            return [];
        }

        $events = $this->m_event->search_utf_convert();
        if (!is_array($events)) {
            return [];
        }

        $matchedEvents = array_filter($events, function($event) use ($keyword) {
            $normalizedName = mb_convert_kana($event['name'], 'ASKV', 'UTF-8');
            return mb_stripos($normalizedName, $keyword) !== false;
        });

        $objectEvents = array_map(function($event) {
            return (object)[
                'id' => $event['id'],
                'name' => $event['name'],
                'no_ticket' => ""
            ];
        }, array_values($matchedEvents));

        return $objectEvents;
    }
}