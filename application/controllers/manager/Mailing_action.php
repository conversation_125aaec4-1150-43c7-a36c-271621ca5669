<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 16/06/2016
 * Time: 3:18 SA
 */

/**
 * Class Mailing_action
 *
 * @property Email_lib                    email_lib
 * @property Mailing_lib                  mailing_lib
 * @property S3_upload                    s3_upload
 * @property M_mailing_subscriber_history m_mailing_subscriber_history
 * @property M_mail_template              m_mail_template
 * @property M_mailing_detail             m_mailing_detail
 * @property M_booking_mailing            m_booking_mailing
 * @property M_event_subscriber           m_event_subscriber
 * @property M_user                       m_user
 */
class Mailing_action extends CI_Controller {
    protected $_promoter_site_url = '';
    protected $_data_get = [];

    function __construct() {
        parent::__construct();
        $this->load->model("m_mailing_subscriber_history");
        $this->load->model("m_mail_template");
        $this->load->model("m_mailing_detail");
        $this->load->model("m_booking_mailing");
        $this->load->model("m_event_subscriber");
        $this->load->model('m_mail_black_list');
        $this->load->model('m_user');
        //Load library
        $this->load->library("Email_lib");
        $this->load->library("Mailing_lib");
        $this->load->library("S3_upload");

    }

    public function insert_open() {
        $file_name = 'open-mail';
        $data = $this->input->get();
        $booking_id = $this->input->get('booking_id');
        $subscriber_id = $this->input->get('subscriber_id');
        $checksum_open = $this->input->get('checksum_open');
        $checksum_type = 'checksum_open';
        if (!$subscriber_id || !$checksum_open || !$booking_id) {
            $text_error = json_encode($data) . ' is Invalid!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $subscriber = $this->check_subscriber_exits($booking_id, $subscriber_id, $checksum_open, $checksum_type, $file_name);

        if (!empty($subscriber->time_open)) {
            $text_error = 'Subscriber_id:" ' . $subscriber_id . ' opened!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return TRUE;
        }
        $data_subs = [
            'time_open' => date('Y-m-d H:i:s'),
        ];
        if(empty($subscriber->id)){
            $text_error = 'Subscriber_id:" ' . $subscriber->id . ' not exits';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $update_status = $this->m_mailing_subscriber_history->update($subscriber->id, $data_subs, TRUE);
        if (!$update_status) {
            $text_error = 'Subscriber_id: ' . $subscriber_id . ' insert time_open error!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }

        $booking_mailing = $this->m_booking_mailing->get($subscriber->booking_id);
        if (empty($booking_mailing->id)) {
            $text_error = 'Booking email id: ' . $booking_id . ' not exits!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $number_open_old = $booking_mailing->number_open;
        $number_open = $number_open_old + 1;
        $update_id = $this->m_booking_mailing->update($subscriber->booking_id, ['number_open' => $number_open], TRUE);
        if (!$update_id) {
            $text_error = 'Booking email id: ' . $booking_id . ', subscriber_id:' . $subscriber_id . '  update error';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $text_success = 'Booking email id: ' . $booking_id . ', subscriber_id:' . $subscriber_id . ' update success!';
        $this->s3_upload->log_insert($text_success, $file_name);
        return TRUE;
    }

    protected function check_subscriber_exits($booking_id, $subscriber_id = 0, $checksum_code = '', $checksum_type, $file_name = '') {
        $subscriber = $this->m_mailing_subscriber_history->get_by([
            'subscriber_id' => $subscriber_id,
            'booking_id'    => $booking_id,
            $checksum_type  => $checksum_code,
        ]);
        if (empty($subscriber->id)) {
            $text_error = 'not exits subscriber';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        return $subscriber;
    }


    /**
     * @param string $time
     *
     * @return bool
     */
    public function send_mail($time = '') {
        $file_name = 'sent-mail';
        //not time, set time now
        if (!$time) {
            $time = date('Y-m-d H:i:s');
        }
        $this->config->load('site_settings');
        $this->_promoter_site_url = trim($this->config->item('promoter_site_url'), "/");
        //get list booking mailing
        $this->m_booking_mailing->unset_all_before_get();
        $this->m_booking_mailing->set_before_get('custom_send_mail_booking');
        //get list subs
        $list_subs = $this->m_booking_mailing->get_list_filter(['booking_time <=' => $time], [], []);
        $subs_insert = [];
        if (is_array($list_subs) && count($list_subs)) {
            $list_user = [];
            //not booking mail
            foreach ($list_subs as $subs) {
                if(isset($subs->email) && $subs->email ){
                    //Set data insert event subscriber
                    $checksum_open = sha1(md5(uniqid()));
                    $checksum_click = sha1(md5(uniqid()));
                    $temp = [
                        'booking_id'     => $subs->id,
                        'subscriber_id'  => $subs->subscriber_id,
                    'checksum_open'  => $checksum_open,
                    'checksum_click' => $checksum_click,
                    'checksum_code'  => $subs->checksum_code,
                    'time_created'   => date('Y-m-d H:i:s'),
                    ];
                    $subs_info = array_merge(json_decode(json_encode($subs), TRUE), $temp);
                    $mail_template = $this->m_mail_template->get($subs->mail_template_id);
                    $sent_mail_return = $this->send_email_template($file_name, $mail_template, $subs_info, $subs->email);
                    unset($temp['checksum_code']);
                    $temp = array_merge($temp, $sent_mail_return);
                    $subs_insert = $this->m_mailing_subscriber_history->insert($temp, TRUE);  
                    if (isset($subs_insert)) {
                        $this->check_booking_completed($subs->id);
                    }
                    $list_user[$subs->user_created] += 1;
                }
            }
            foreach ($list_user as $user_id => $total) {
                $this->m_user->add_mailing_history([
                    "submission"  =>  $total,
                    "user_id"     =>    $user_id
                ]);
            }
        }
        return TRUE;
    }

    protected function check_booking_completed($id) {
        $this->m_booking_mailing->unset_all_before_get();
        $this->m_booking_mailing->set_before_get('check_booking_done');
        $list_subs = $this->m_booking_mailing->get_many_by(['id' => $id]);
        if (is_array($list_subs) && !count($list_subs)) {
            $this->m_booking_mailing->update($id, ['status' => 2], TRUE);
        } else {
            $this->m_booking_mailing->update($id, ['status' => 1], TRUE);
        }
    }

    public function resend_mail($time = '') {
        $file_name = 'resent-mail';
        //not time, set time now
        if (!$time) {
            $time = date('Y-m-d H:i:s');
        }
        $this->config->load('site_settings');
        $this->_promoter_site_url = trim($this->config->item('promoter_site_url'), "/");
        //get list booking mailing
        $this->m_booking_mailing->unset_all_before_get();
        $this->m_booking_mailing->set_before_get('custom_resend_mail_booking');
        //get list subs
        $list_subs = $this->m_booking_mailing->get_list_filter(['booking_time <=' => $time], [], []);
        $list_subs_update = [];
        if (is_array($list_subs) && count($list_subs)) {
            $list_user = [];
            //not booking mail
            foreach ($list_subs as $subs) {
                if(isset($subs->email) && $subs->email ){
                    //Set data insert event subscriber
                    $mail_template = $this->m_mail_template->get($subs->mail_template_id);
                    $id = $subs->id;
                    $booking_id = $subs->booking_id;
                    $subs = json_decode(json_encode($subs), TRUE);
                    $temp = $this->send_email_template($file_name, $mail_template, $subs);
                    $temp['time_modified'] = date('Y-m-d H:i:s');
                    $subs_update = $this->m_mailing_subscriber_history->update($id, $temp, TRUE);
                    if (isset($subs_update)) {
                        $this->check_booking_completed($booking_id);
                    }
                    $list_user[$subs->user_created] += 1;
                }
            }
            foreach ($list_user as $user_id => $total) {
                $this->m_user->add_mailing_history([
                    "submission"  =>  $total,
                    "user_id"     =>    $user_id
                ]);
            }
        }
        return TRUE;
    }

    protected function send_email_template($file_name, $mail_template, $subscriber, $email_to = '', $flag_test = false) {
        //Set data send mail
        $mailtype = "html";
        $subject = isset($mail_template->mail_name) ? $mail_template->mail_name : '';
        if($flag_test){
            $content = $this->_setting_mail_content_test($mail_template, $subscriber);
        }else{
            $content = $this->_setting_mail_content($mail_template, $subscriber);
        }
        if ($email_to == '') {
            $email_to = isset($subscriber['email']) ? $subscriber['email'] : '';
        }
        //Do send mail
        $send_email_state = $this->email_lib->send_email($subject, $content, $email_to, '', '', '', [], [], null, $mailtype);

        //Check state send mail
        $data_return = [
            'resent' => (!empty($subscriber['resent']) ? $subscriber['resent'] : 0) + 1,
        ];

        if (is_object($send_email_state) && $send_email_state->state) {
            $data_return['status'] = 1;
            $text_error = 'Booking mailing id:' . $subscriber['booking_id'] . ' to ' . $email_to . ' :send success ';
            $this->s3_upload->log_insert($text_error, $file_name);
        } else if (is_object($send_email_state) && !$send_email_state->state) {
            $data_return['status'] = 0;
            $text_error = 'Booking mailing id:' . $subscriber['booking_id'] . ' to ' . $email_to . ' :send error';
            $this->s3_upload->log_insert($text_error, $file_name);
        }
        return $data_return;
    }

    protected function _setting_mail_content($mail_template, $subscriber) {
        $subscriber_id = isset($subscriber['subscriber_id']) ? $subscriber['subscriber_id'] : 0;
        $booking_id = isset($subscriber['booking_id']) ? $subscriber['booking_id'] : 0;
        $checksum_open = isset($subscriber['checksum_open']) ? $subscriber['checksum_open'] : '';
        $checksum_click = isset($subscriber['checksum_click']) ? $subscriber['checksum_click'] : '';

        //link
        $open_url = $this->_promoter_site_url . "/manager/mailing_action/insert_open?booking_id=" . $booking_id .
            "&subscriber_id=" . $subscriber_id . "&checksum_open=" . $checksum_open;

        $url_del_application = $this->_promoter_site_url . "/manager/mailing_action/del_subscriber_mailing?booking_id=" .
            $booking_id . "&subscriber_id=" . $subscriber_id . "&checksum_code=" . $subscriber['checksum_code'];

        //link url in mail
        $link_open = '<img src="' . $open_url . '" style="display: none">';

        $link_del_application = '<a target="_blank" href="' . $url_del_application . '">' . $url_del_application . '</a>';
        $mail_content = isset($mail_template->mail_content) ? $mail_template->mail_content : '';
        $mail_content = "<p style='margin: 0'>" . implode( "<br/></p>\n\n<p style='margin: 0'>", explode( '<br/>', $mail_content ) ) . "</p>";
        $mail_content = $this->mailing_lib->progress_data_mail($mail_content, $subscriber,$mail_template->lang);
        if (!empty($mail_template->count_link)) {
            $url_click_redirect = $mail_template->count_link;
            $click_url = $this->_promoter_site_url . "/manager/mailing_action/update_click?booking_id=" . $booking_id .
                "&subscriber_id=" . $subscriber_id . "&checksum_click=" . $checksum_click . "&url=" . $url_click_redirect;
            $link_click = '<a href="' . $click_url . '">' . $url_click_redirect . '</a>';
            $mail_content = str_replace($url_click_redirect, $link_click, $mail_content);

        }
        $mail_content = str_replace(my_lang('[メール配信停止URL]',$mail_template->lang), $link_del_application, $mail_content);
        $mail_template_return = $mail_content ? $mail_content . '<br>' . $link_open : $link_open;

        return $mail_template_return;
    }

    protected function _setting_mail_content_test($mail_template, $subscriber) {
        $subscriber_id = isset($subscriber['subscriber_id']) ? $subscriber['subscriber_id'] : 0;
        $booking_id = isset($subscriber['booking_id']) ? $subscriber['booking_id'] : 0;
        $checksum_open = isset($subscriber['checksum_open']) ? $subscriber['checksum_open'] : '';
        $checksum_click = isset($subscriber['checksum_click']) ? $subscriber['checksum_click'] : '';

        //link
        $open_url = $this->_promoter_site_url . "/manager/mailing_action/insert_open?booking_id=" . $booking_id .
            "&subscriber_id=" . $subscriber_id . "&checksum_open=" . $checksum_open;

        $url_del_application = $this->_promoter_site_url . "/manager/mailing_action/del_subscriber_mailing?booking_id=" .
            $booking_id . "&subscriber_id=" . $subscriber_id . "&checksum_code=" . $subscriber['checksum_code'];

        //link url in mail
        $link_open = '<img src="' . $open_url . '" style="display: none">';

        $link_del_application = '<a target="_blank" href="' . $url_del_application . '">' . $url_del_application . '</a>';
        $mail_content = isset($mail_template->mail_content) ? $mail_template->mail_content : '';
        $mail_content = "<p style='margin: 0'>" . implode( "<br/></p>\n\n<p style='margin: 0'>", explode( '<br/>', $mail_content ) ) . "</p>";
        $mail_content = $this->mailing_lib->progress_data_mail($mail_content, $subscriber,$mail_template->lang);
        if (!empty($mail_template->count_link)) {
            $url_click_redirect = $mail_template->count_link;
            $link_click = '<a href="' . $url_click_redirect . '">' . $url_click_redirect . '</a>';
            $mail_content = str_replace($url_click_redirect, $link_click, $mail_content);

        }
        $mail_content = str_replace(my_lang('[メール配信停止URL]', $mail_template->lang), $link_del_application, $mail_content);
        $mail_template_return = $mail_content ? $mail_content . '<br>' . $link_open : $link_open;

        return $mail_template_return;
    }

    public function update_click() {
        $file_name = 'update_click';
        $data = $this->input->get();
        $url = isset($data['url']) && $data['url'] ? $data['url'] : '#';
        $booking_id = $data['booking_id'];
        $subscriber_id = $data['subscriber_id'];
        $checksum_click = $data['checksum_click'];
        if (!$booking_id || !$subscriber_id || !$checksum_click || !$url) {
            $text_error = json_encode($data) . ' is incorrect!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $subscriber = $this->check_subscriber_exits($booking_id, $subscriber_id, $checksum_click, 'checksum_click');
        if (empty($subscriber->id)) {
            $text_error = json_encode($data) . 'not exits subscriber';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        if (empty($subscriber->time_click)) {
            $data_update = [
                'time_click' => json_encode([date('Y-m-d H:i:s')]),
            ];
        } else {
            $time_click_old = json_decode($subscriber->time_click, TRUE);
            $time_click_old[] = date('Y-m-d H:i:s');
            $data_update = [
                'time_click' => json_encode($time_click_old),
            ];
        }

        $update_status = $this->m_mailing_subscriber_history->update($subscriber->id, $data_update, TRUE);
        if (!$update_status) {
            $text_error = 'subscriber_id: ' . $subscriber_id . ' insert time_click error!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }

        $booking_mailing = $this->m_booking_mailing->get($booking_id);
        if (empty($booking_mailing->id)) {
            $text_error = 'Booking mailing: ' . $booking_id . ' not exits!';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $number_click_old = $booking_mailing->number_click;
        $number_click = $number_click_old + 1;
        $update_id = $this->m_booking_mailing->update($booking_id, ['number_click' => $number_click], TRUE);
        if (!$update_id) {
            $text_error = 'Booking mailing: ' . $booking_id . ' update number click of subscriber_id:' . $subscriber_id . ' error';
            $this->s3_upload->log_insert($text_error, $file_name);
            return FALSE;
        }
        $text_success = 'Booking mailing: ' . $booking_id . ' update data click link of subscriber_id:' . $subscriber_id . ' success!';
        $this->s3_upload->log_insert($text_success, $file_name);
        $file_headers = @get_headers($url);
        if (!$file_headers || $file_headers[0] == 'HTTP/1.1 404 Not Found') {
            $this->config->load('site_settings');
            $this->_promoter_site_url = trim($this->config->item('promoter_site_url'), "/");
            redirect($this->_promoter_site_url . '/manager/mailing_action/count_link_page');
        } else {
            redirect($url);
        }
        return TRUE;
    }

    public function count_link_page() {
        $this->load->view("manager/email_template/count_link_index", []);
    }

    public function del_subscriber_mailing() {
        $data_input = $this->input->get();
        $this->config->load('site_settings');
        $this->_promoter_site_url = $this->config->item('promoter_site_url');
        $booking_id = isset($data_input['booking_id']) ? $data_input['booking_id'] : '';
        $subscriber_id = isset($data_input['subscriber_id']) ? $data_input['subscriber_id'] : '';
        $checksum_code = isset($data_input['checksum_code']) ? $data_input['checksum_code'] : '';
        if (!$booking_id || !$subscriber_id || !$checksum_code) {
            $this->load->view("manager/mailing_del_subscriber/error");
        }else{
            $data = [];
            $subs = $this->m_mailing_subscriber_history->search_by_checksum_code($booking_id, $subscriber_id, $checksum_code);
            if (!empty($subs->id)) {
                $data['subs'] = $subs;
            }
            $data['save_link'] = $this->_promoter_site_url . '/manager/mailing_action/del_subs?booking_id=' . $booking_id . '&subscriber_id=' . $subscriber_id . '&checksum_code=' . $checksum_code;
            $this->load->view("manager/mailing_del_subscriber/index", $data);
        }
    }

    public function del_subs() {
        $name_file = 'del-mail';
        $data_input = $this->input->get();
        $booking_id = isset($data_input['booking_id']) ? $data_input['booking_id'] : '';
        $subscriber_id = isset($data_input['subscriber_id']) ? $data_input['subscriber_id'] : '';
        $checksum_code = isset($data_input['checksum_code']) ? $data_input['checksum_code'] : '';
        if (!$booking_id || !$subscriber_id || !$checksum_code) {
            echo json_encode([
                'state' => 0,
                'html'  => '',
            ]);
            die();
        }
        $data = [];
        $subs = $this->m_mailing_subscriber_history->search_by_checksum_code($booking_id, $subscriber_id, $checksum_code);
        if (empty($subs->id) || $subs->id <= 0) {
            $data['is_del'] = 0;
        }
        $data['subs'] = $subs;
        $this->m_mailing_detail->unset_all_before_get();
        $this->m_mailing_detail->set_before_get('get_list_mail_cancel');
        $where = [
            'es.email'   => isset($subs->email) ? $subs->email : '',
            'm.status <'   => 2,
        ];
        $list_subs_cancel = $this->m_mailing_detail->get_list_filter($where, [], []);
        if (!is_array($list_subs_cancel)) {
            $list_subs_cancel = [];
        }
        /*   
        // 購読解除 event_subscribers
        if (isset($subs->email)) {
            $delete_status = $this->m_event_subscriber->set_canceled_mailing($subs->email);
        }
        
        $data = [];
        $data['is_del'] = 1;
        */
        /**
         * update status = 2 của mailing_detail 
         */
        foreach ($list_subs_cancel as $sub_del) {
            $mailing_subs_id = isset($sub_del->id) ? $sub_del->id : "";
            $delete_status = $this->m_mailing_detail->update($mailing_subs_id, ['status' => 2], TRUE);
            if (!$delete_status) {
                $data['is_del'] = ($sub_del->id == $subs->mailing_subs_id) ? 2 : $data['is_del'];
                $log_msg = 'mailing_id, ' . isset($sub_del->mailing_id) ? $sub_del->mailing_id : 0 . 'subs_id, ' . $sub_del->subscriber_id . ', mailing_subs_id, ' . $sub_del->id;
                $this->s3_upload->log_insert($log_msg, $name_file);
            }
        }
        /**
         * update status = 2 của mail_subscribe_history 
         */
        $where_subs = [
            'subscriber_id'   => $subscriber_id,
            'booking_id'   => $booking_id,
        ];
        $mail_sub_his = $this->m_mailing_subscriber_history->get_list_filter($where_subs, [], []);
        $this->m_mailing_subscriber_history->update($mail_sub_his[0]->id, ['status' => 2], TRUE);
        
        $data_validate['email'] = $data['subs']->email;
        $validate = $this->m_mail_black_list->validate($data_validate);
        if (!$validate) {
            $data['is_del'] = 1;
            $html = $this->load->view("manager/mailing_del_subscriber/modal", $data, TRUE);
            echo json_encode([
                'state' => 1,
                'msg'   => my_lang('削除済みです。'),
                'html'  => $html,
            ]);
            return TRUE;
        }
        $insert_id = $this->m_mail_black_list->insert([
            'email'                => $subs->email,
            'booking_id'           => $booking_id,
            'time_created'         => date('Y-m-d H:i:s'),
            'user_created'         => $this->session->userdata('user_id')
        ], TRUE);
        if($insert_id){
            $data['is_del'] = 2;
            $log_msg = 'email ' . $subs->email . ' unsubscribe';
            $this->s3_upload->log_insert($log_msg, $name_file);
            $html = $this->load->view("manager/mailing_del_subscriber/modal", $data, TRUE);
            echo json_encode([
                'state' => 1,
                'msg'   => my_lang('操作が成功しました。'),
                'html'  => $html,
            ]);
        }
    }

    /**
     * @param        $email_result
     * @param string $name_file
     */
    protected function log_insert($email_result, $name_file = '') {
        $folderLog = APPPATH . 'logs';
        if (!is_dir($folderLog)) {
            $old = umask(0);
            mkdir($folderLog, 0755);
            umask($old);
        }
        $myFile = APPPATH . "logs/log-" . $name_file . "-" . date('Y-m-d') . ".txt";
        $fh = fopen($myFile, 'a');
        fwrite($fh, date('Y-m-d H:i:s') . ": " . $email_result . PHP_EOL);
        fclose($fh);
    }

    public function send_mail_test() {
        $data = $this->input->post();
        $file_name = 'sent-mail';
        $this->config->load('site_settings');
        $this->_promoter_site_url = trim($this->config->item('promoter_site_url'), "/");
        //get list booking mailing
        $this->m_booking_mailing->unset_all_before_get();
        $this->m_booking_mailing->set_before_get('custom_send_mail_booking');
       
        //Set data insert event subscriber
        $checksum_open = sha1(md5(uniqid()));
        $checksum_click = sha1(md5(uniqid()));
        $checksum_code = sha1(md5(uniqid()));
        $temp = [
            'booking_id'     => 1,
            'checksum_open'  => $checksum_open,
            'checksum_click' => $checksum_click,
            'time_created'   => date('Y-m-d H:i:s'),
            'checksum_code' => $checksum_code
        ];
        $subs_info = $temp;
        $mail_template = $this->m_mail_template->get($data['mail_template_id']);
        if($mail_template){
            $email_to = array($data['email1'],$data['email2'],$data['email3']);
            foreach ($email_to as $email) {
                if($email){
                    $sent_mail_return = $this->send_email_template($file_name, $mail_template, $subs_info, $email, true);
                    if(!$sent_mail_return['status']){
                        $data['title'] = my_lang('邮件还没发送完');
                        $data['content'] = my_lang('消息内容尚未确认。');
                        $data_return = [
                            'state'    => 1,
                            'html'      =>  $this->load->view("manager/booking_mailing/modal", $data, TRUE)
                        ];
                        echo json_encode($data_return);
                        die();
                    }
                }
            }
        
            $data['title'] = my_lang('送信完了');
            $data['content'] = my_lang('受信メールをご確認ください。');
            $data_return = [
                'state'    => 1,
                'html'      =>  $this->load->view("manager/booking_mailing/modal", $data, TRUE)
            ];
            
            echo json_encode($data_return);
            die();
        }

        $data_return = [
            'state'    => 0,
            'html'      =>  ''
        ];
        echo json_encode($data_return);
        return TRUE;
    }
}
