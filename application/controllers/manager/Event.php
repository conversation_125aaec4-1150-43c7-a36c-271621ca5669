<?php

/**
 * New_event controller
 *
 * @property Application_lib           application_lib
 * @property Event_lib                 event_lib
 * @property Email_lib                 email_lib
 * @property M_event_category          m_event_category
 * @property M_event_form              m_event_form
 * @property M_event_form_field        m_event_form_field
 * @property M_event_form_data         m_event_form_data
 * @property M_event_subscriber        m_event_subscriber
 * @property M_event_fee               m_event_fee
 * @property M_event_fee_data          m_event_fee_data
 * @property M_event_fee_banking       m_event_fee_banking
 * @property M_event_fee_cashier       m_event_fee_cashier
 * @property M_event_fee_combini       m_event_fee_combini
 * @property M_event_fee_item          m_event_fee_item
 * @property M_event_fee_item_data     m_event_fee_item_data
 * @property M_event_fee_item_shipping m_event_fee_item_shipping
 * @property M_user m_user
 */
class Event extends Manager_base {
    function __construct() {
        parent::__construct();
        $this->load_more_css("assets/manager/css/application_form.css");
        $this->load_more_css("assets/manager/css/ticket.css");
        $this->load_more_css("assets/manager/css/form_item.css");
        $this->load_more_css("assets/manager/css/event.css");
        $this->load_more_css("assets/front/css/trade_info.css");
        $this->load_more_js("assets/manager/js/event.js");
        $this->load_more_css("assets/manager/css/overview.css");
        $this->load->model("m_event_category");
        $this->load->model("m_event_form");
        $this->load->model("m_event_subscriber");
        $this->load->model("m_event_fee");
        $this->load->model("m_event_fee_data");
        $this->load->model("m_event_fee_banking");
        $this->load->model("m_event_fee_cashier");
        $this->load->model("m_event_fee_combini");
        $this->load->model("m_event_fee_item");
        $this->load->model("m_event_fee_item_data");
        $this->load->model("m_event_fee_item_shipping");
        $this->load->library("Application_lib");
        $this->load->library("Event_lib");
        $this->load->library("Email_lib");
        $this->set_data_part("top_bar", ['active' => 'offset1']);
        $this->load->model("m_user");
    }

    function setting_class() {
        $this->name = Array(
            "class"  => "manager/event",
            "view"   => "manager/event",
            "model"  => "m_event",
            "object" => "Event",
        );
    }

    public function manager($data = Array()) {
        redirect(site_url("manager/event_list"));
    }

    /**
     * Form create new event
     */
    public function create($event_id = NULL) {
        $data = Array();
        // get event data
        $event_id = intval($event_id);
        $data['event'] = $this->model->get($event_id);
        if (isset($data['event']->parent_id)) {
            $data['list_category_child'] = $this->m_event_category->get_many_by(['parent_id' => $data['event']->parent_id]);
        }

        $this->set_data_part("title", my_lang('イベント情報登録'), FALSE);
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav(1, $data);
        //Get list hours
        $data['list_hours'] = $this->event_lib->get_data_hours();
        //Get list minute
        $data['list_minutes'] = $this->event_lib->get_data_minutes();
        //Get list category
        $data['list_category'] = $this->m_event_category->get_many_by(['parent_id' => 0]);
        //Get list is_vip
        $data['list_is_vip'] = $this->model->get_is_vip();
        //Get list type ticket
        $data['list_type_ticket'] = $this->model->get_type_ticket();
        $data['list_held_hours'] = $this->model->get_list_held_hours();
        //Load css + js more
        $lang = get_cookie('lang') == 'english' ? "en" : "ja";
        $this->load_more_js("assets/plugins-bower/jquery-ui/ui/i18n/datepicker-$lang.js", TRUE);
        //Load link action form
        $data["save_temp_link"] = site_url($this->name["class"] . "/temp_create_save");
        $data["save_link"] = site_url($this->name["class"] . "/create_save");
        $data["event_copy"] = site_url($this->name["class"] . "/event_copy");
        $data['hide_input_field'] = TRUE;
        //Load content
        $this->load_more_css("assets/front/css/contact.css");
        $this->load_more_css("assets/manager/css/create-event.css");
        $this->load_more_js("assets/front/js/contact.js");
        $data['form'] = $this->load->view("manager/event/form", $data, TRUE);
        $data['confirm'] = $this->load->view("manager/event/create_conf", $data, TRUE);

        $content = $this->load->view("manager/event/create", $data, TRUE);
        $this->show_page($content);
    }

    function entrust_popup() {
        $data = Array();
        $modal_data['save_url'] = site_url("manager/event/entrust_send_email/2");
        $data['step1'] = $this->load->view("manager/event/entrust_popup_step1", $modal_data, TRUE);
        $modal_data['state'] = 'success';
        $data['success'] = $this->load->view("manager/event/entrust_popup_notification", $modal_data, TRUE);
        $modal_data['state'] = 'fail';
        $data['fail'] = $this->load->view("manager/event/entrust_popup_notification", $modal_data, TRUE);
        $html = $this->load->view("manager/event/entrust_popup", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    function entrust_send_email() {
        $data_return = Array();
        $data_return['callback'] = 'send_email_response';
        $data_return['state'] = 2;
        $email_data = Array();

        // get user data
        $user_id = $this->session->userdata("user_id");
        $this->db->from("users");
        $this->db->where('id', $user_id);
        $user = $this->db->get()->row();
        $this->config->load('site_settings');
        $admin_email = $this->config->item('site_admin_email');
        if (isset($user)) {
            $subject = my_lang('【フリッパ】イベント情報設定おまかせプラン');
            $email = $user->email;
            $email_data['company_name'] = $user->company_name;
            $email_data['first_name'] = $user->first_name;
            $email_data['last_name'] = $user->last_name;
            $content = $this->load->view("email/entrust_other", $email_data, TRUE);
            $return = $this->email_lib->send_email(
                $subject, $content,
                $email, NULL,
                NULL, NULL, NULL,
                $admin_email
            );

            if ($return->status_code != 'OK') {
                $data_return['state'] = 3;
            }

            $data_return['return'] = $return;
        } else {
            $data_return['state'] = 3;
        }

        echo json_encode($data_return);
    }

    /**
     * Logic temp create event save session
     */
    public function temp_create_save() {
        $data = $this->input->post();
        $data['name'] = isset($data['name']) ? $data['name'] : '';
        $data['name'] = preg_replace("/\r\n|\r|\n/", '<br/>', trim($data['name']));
        $data['name'] = $this->event_lib->convertFullWidth($data['name']);
        $data_return = Array();
        $data_return['callback'] = "form_create_event_check_response";
        if ($data && count($data)) {
            $data = $this->unset_field_unnecessary_form_create($data);
            $data_validated = $this->event_lib->event_validated($data, TRUE);
            if (!empty($data['event_id_copy']) && $data['event_id_copy'] != 1) {
                $event_copy = $this->model->get($data['event_id_copy']);
                if ($data['type_ticket'] != $event_copy->type_ticket) {
                    $data_validated["data"] = $data;
                    $data_validated['state'] = 0;
                    $data_validated["msg"] = my_lang('データが無効です。');
                    $data_validated['error']['type_ticket'] = my_lang('複写したイベントでは発券方法が変更できません。');
                }
            }

            if($data['address1']){
                $regex = preg_match('/^[一-龥ぁ-んァ-ンa-zA-Z0-9\s]+$/', $data['address1']);
                if (!$regex) {
                    $data_validated['error']['address1'] = my_lang('開催場所名部署欄は正しい形式ではありません。');
                }
            }

            // validate event id
            if (!empty($data['event_id']) && !empty($this->model->get($data['event_id']))) {
                // if event belongs to the current user
                $data_validated['data']['event_id'] = $data['event_id'];
            }
            if (is_array($data_validated) && $data_validated['state']) {
                $this->session->set_userdata("temp_create_data", $data_validated['data']);
                $this->session->set_userdata("temp_event_id_copy", $data_validated['event_id_copy']);
                $data_return["data"] = $this->event_lib->process_data_create_conf($data_validated['data_post']);
                $data_return["state"] = 1;
                $data_return["msg"] = my_lang('操作が成功しました。');
            } else {
                $data_return = $data_validated;
            }
        } else {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["state"] = 0;
        }
        $dataChoose = array();
        $dataChoose['fied_id'] = [
            '1',
            '13'
        ];
        $dataChoose['required'] = [
            '1',
            '13'
        ];
        $dataChoose['show_on_friend_form'] = [
            '1',
            '13',
            '20'
        ];
        $dataChoose['type'] = [
            'text',
            'text',
        ];
        $this->session->set_userdata('temp_application_form_data', $dataChoose);
        $data_return['data']['start_date'] = format_date_en_jp($data_return['data']['start_date']);
        $data_return['data']['end_date'] = format_date_en_jp($data_return['data']['end_date']);
        list($hour, $minute) = explode(":", $data_return['data']['start_time']);
        $data_return['data']['start_hour'] = $hour;
        $data_return['data']['start_minute'] = $minute;
        list($hour, $minute) = explode(":", $data_return['data']['opening_time']);
        $data_return['data']['opening_hour'] = $hour;
        $data_return['data']['opening_minute'] = $minute;
        list($hour, $minute) = explode(":", $data_return['data']['end_time']);
        $data_return['data']['end_hour'] = $hour;
        $data_return['data']['end_minute'] = $minute;
        $data_return['data']['is_vip'] = my_lang($data_return['data']['is_vip']);
        $typeDescription = $this->model->get_type_ticket_description();
        $data_return['data']['type_ticket'] = $typeDescription[$data['type_ticket']];
        echo json_encode($data_return);
    }

    protected function unset_field_unnecessary_form_create($data) {
        $list_field = ['start_subscribe', 'end_subscribe', 'start_checkin', 'end_checkin'];
        foreach ($list_field as $field) {
            if (isset($data[$field])) {
                unset($data[$field]);
            }
        }
        return $data;
    }

    public function check_end_date($end_date, $start_date) {
        return $this->event_lib->check_end_date($end_date, $start_date);
    }

    public function check_end_time($end_time) {
        return $this->event_lib->check_end_time($end_time);
    }

    /**
     * Create save event
     *
     * @return bool
     */
    public function create_save() {
        $data = $this->session->userdata('temp_create_data');
        if (!(is_array($data) && count($data))) {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
        $action = $this->input->get('action') ? $this->input->get('action') : 'next_step';
        $data['draft'] = 2;
        $data['created_time'] = date('Y-m-d H:i:s');
        $data['status'] = 'in_preparation';
        $data['title_e_ticket_mail'] = my_lang("[イベント名]お申し込み完了", $data['lang']);
        $data['e_ticket_mail'] = my_lang('[予約者会社名]', $data['lang']).'<br/>'
            .my_lang('[予約者部署名]', $data['lang']).'<br/>'
            .my_lang('mr_first', $data['lang']).my_lang('[予約者氏名]', $data['lang']).my_lang('mr_second', $data['lang']).'<br/><br/>'
            .my_lang('この度は、[イベント名]にお申し込みいただきまして誠にありがとうございました。', $data['lang']).'<br/>'
            .my_lang('下記の通り、受け付けを完了し、電子チケットを添付画像にてお送りいたします。', $data['lang']).'<br/><br/>'
            .my_lang('なお、複数枚ご購入頂いたご購入者様は「チケット総数」及び「総合計金額」をご確認願います。同伴者様にはチケット名と数量のみの記載となっております。', $data['lang']);

        // AnhLD - pharse 8
        $data['register_success_mail'] = $this->get_default_email_template("register_success", $data['lang']);
        $data['title_announce_win_mail'] = my_lang("[イベント名]当選通知", $data['lang']);
        $data['announce_win_mail'] = $this->get_default_email_template("announce_win", $data['lang']);
        $data['title_announce_lose_mail'] = my_lang("[イベント名]落選通知", $data['lang']);
        $data['announce_lose_mail'] = $this->get_default_email_template("announce_lose", $data['lang']);
        $data['title_payment_success_mail'] = my_lang('[イベント名]決済完了のお知らせ', $data['lang']);
        $data['payment_success_mail'] = my_lang('[予約者会社名]', $data['lang']).'<br/>'
            .my_lang('[予約者部署名]', $data['lang']).'<br/>'
            .my_lang('mr_first', $data['lang']).my_lang('[予約者氏名]', $data['lang']).my_lang('mr_second', $data['lang']).'<br/><br/>'
            .my_lang('この度は、[イベント名]にお申し込みいただきまして誠にありがとうございました。', $data['lang']).'<br/>'
            .my_lang('決済を完了しましたのでお知らせいたします。', $data['lang']).'<br/>'
            .my_lang('チケットは後日、改めてお送りいたします。', $data['lang']).'<br/>';

        $event_id_copy = $this->session->userdata('temp_event_id_copy');
        // set data event copy
        if (intval($event_id_copy)) {
            $this->model->unset_before_get('custom_select_for_user');
            $data_copy = $this->model->get($event_id_copy);
            $transactionTypes = json_decode($this->m_event_fee->get_by(['event_id' => $data_copy->id])->transaction_type, true);
            if ($action == 'goEventTop' && in_array('CASHIER', $transactionTypes)) {
                $data_return['state'] = 0;
                $data_return['msg'] = my_lang('このコピーには現金決済手数料の支払いが必要です。<br/> 次のステップへお進みくだい。');
                echo json_encode($data_return);
                return FALSE;
            }
            if (!count($data_copy) || $data_copy->draft < 5) {
                $data_return['state'] = 0;
                $data_return['msg'] = my_lang('イベントのコピーが存在しません。');
                echo json_encode($data_return);
                return FALSE;
            }
            $data['show_ticket_display'] = $data_copy->show_ticket_display;
            $data['e_ticket_mail'] = $data_copy->e_ticket_mail;
            $data['draft'] = $data_copy->draft;
            $data['vip_email_notify'] = $data_copy->vip_email_notify;

            // AnhLD - pharse 8
            $data['announce_lose_mail'] = $data_copy->announce_lose_mail;
            $data['announce_win_mail'] = $data_copy->announce_win_mail;
            $data['register_success_mail'] = $data_copy->register_success_mail;
            $data['payment_success_mail'] = $data_copy->payment_success_mail;
            $data['title_payment_success_mail'] = $data_copy->title_payment_success_mail;
            $data['settings'] = $data_copy->settings;
            $data['list_email'] = $data_copy->list_email;
            $data['is_scan_number'] = $data_copy->is_scan_number;
            $data['type_checkin'] = $data_copy->type_checkin;
            $data['event_id_copy'] = $event_id_copy;
            $data['is_update_link'] = $data_copy->is_update_link;
        }

        // check if user is editing the event
        if (!empty($data['event_id'])) {
            // if event id is not empty
            // edit that event instead of creating a new one
            $data_return = Array();
            $data_return["callback"] = "default_form_submit_respone";
            $data_return["redirect"] = site_url($this->name["class"] . "/application_form/" . $data['event_id']);
            parent::edit_save($data['event_id'], $data, $data_return);
            json_encode($data_return);
            return FALSE;
        }

        $insert_id = $this->model->insert($data, FALSE);
        $key_field = $this->model->get_primary_key();
        $data_validated[$key_field] = $insert_id;

        if ($insert_id) {
            $data_return["key_name"] = $key_field;
            $data_return["record"] = $data_validated;
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
            if ($action == 'next_step') {
                $data_return["redirect"] = site_url($this->name["class"] . "/application_form/" . $insert_id);
            } else {
                $data_return["redirect"] = site_url("manager/event_detail/index_top/" . $insert_id);
            }

            if (isset($data_copy)) {
                $user_created = $this->session->userdata('user_id');
                $time_created = date('Y-m-d H:i:s');
                $time_modified = date('Y-m-d H:i:s');
                $data_form_list = $this->m_event_form->get_many_by(['event_id' => $data_copy->id]);
                $form_id = [];
                if (is_array($data_form_list) && count($data_form_list)) {
                    foreach ($data_form_list as $form) {
                        if (isset($form->id)) {
                            $insert_temp = array();
                            $insert_temp['event_id'] = $insert_id;
                            $insert_temp['form_title'] = $form->form_title;
                            $insert_temp['type_form'] = $form->type_form;
                            $form_id[$form->type_form] = $this->m_event_form->insert($insert_temp, TRUE);
                        }
                    }
                }
                $this->m_event_form_data->include_text_view_field(TRUE);
                $data_sub_form = $this->m_event_form_data->get_many_by(['event_id' => $data_copy->id]);
                foreach ($data_sub_form as $item) {

                    // AnhLD - fix bug - create new custom field
                    $new_field_id = $this->event_lib->copy_custom_form_field($item->field_id);

                    $insert_temp = array();
                    $insert_temp['event_id'] = $insert_id;
                    $insert_temp['field_id'] = $new_field_id; // old error $item->field_id;
                    $insert_temp['form_id'] = $form_id[$item->type_form];
                    $insert_temp['default_value'] = $item->default_value;
                    $insert_temp['type_form'] = $item->type_form;
                    $insert_temp['max_choice'] = $item->max_choice;
                    $insert_temp['rules'] = $item->rules;
                    $insert_temp['required'] = $item->required;
                    $insert_temp['position'] = $item->position;
                    $insert_temp['show_on_friend_form'] = $item->show_on_friend_form;

                    $this->m_event_form_data->insert($insert_temp, TRUE);
                }
                $this->m_event_form_data->include_text_view_field(FALSE);

                if (isset($data['type_ticket']) && $data['type_ticket'] != 0) {
                    $data_fee = $this->m_event_fee->get_by(['event_id' => $data_copy->id]);
                } else {
                    $data_fee = $this->m_event_fee->get_by(['event_id' => $data_copy->id, 'is_fee' => 0]);
                }
                if (isset($data_fee->id)) {
                    $insert_temp = array();
                    $insert_temp['event_id'] = $insert_id;
                    $insert_temp['transaction_type'] = $data_fee->transaction_type;
                    $insert_temp['is_tax'] = $data_fee->is_tax;
                    $insert_temp['tax'] = $data_fee->tax;
                    $insert_temp['note1'] = $data_fee->note1;
                    $insert_temp['note4'] = $data_fee->note4;
                    $insert_temp['note3'] = $data_fee->note3;
                    $insert_temp['note2'] = $data_fee->note2;
                    $insert_temp['is_fee'] = $data_fee->is_fee;

                    $fee_id = $this->m_event_fee->insert($insert_temp, TRUE);
                    // get data fee_banking
                    $data_fee_banking = $this->m_event_fee_banking->get_by(['event_id' => $data_copy->id]);
                    if (isset($data_fee_banking->id)) {
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['account_name'] = $data_fee_banking->account_name;
                        $insert_temp['bank_name'] = $data_fee_banking->bank_name;
                        $insert_temp['shop_name'] = $data_fee_banking->shop_name;
                        $insert_temp['type'] = $data_fee_banking->type;
                        $insert_temp['account_number'] = $data_fee_banking->account_number;
                        $insert_temp['transfer_deadline'] = $data_fee_banking->transfer_deadline;
                        $insert_temp['transfer_fee'] = $data_fee_banking->transfer_fee;
                        $insert_temp['note'] = $data_fee_banking->note;
                        $insert_temp['contact_information'] = $data_fee_banking->contact_information;
                        $insert_temp['email'] = $data_fee_banking->email;
                        $insert_temp['phone_number'] = $data_fee_banking->phone_number;

                        $this->m_event_fee_banking->insert($insert_temp, TRUE);
                    }
                    // get data fee_cashier
                    $data_fee_cashier = $this->m_event_fee_cashier->get_by(['event_id' => $data_copy->id]);
                    if (isset($data_fee_cashier->id)) {
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['time_begin'] = $data_fee_cashier->time_begin;
                        $insert_temp['time_end'] = $data_fee_cashier->time_end;
                        $insert_temp['reception_place'] = $data_fee_cashier->reception_place;
                        $insert_temp['note'] = $data_fee_cashier->note;
                        $this->m_event_fee_cashier->insert($insert_temp, TRUE);
                    }
                    // get data fee_cashier
                    $data_fee_combini = $this->m_event_fee_combini->get_by(['event_id' => $data_copy->id]);
                    if (isset($data_fee_combini->id)) {
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['reception_place'] = $data_fee_combini->reception_place;
                        $insert_temp['reception_phone'] = $data_fee_combini->reception_phone;
                        $insert_temp['user_created'] = $user_created;
                        $this->m_event_fee_combini->insert($insert_temp, TRUE);
                    }
                    $data_fee_data = $this->m_event_fee_data->get_many_by(['event_id' => $data_copy->id]);
                    foreach ($data_fee_data as $item) {
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['fee_item_id'] = $item->fee_item_id;
                        $insert_temp['default_fee'] = $item->default_fee;
                        $insert_temp['fee_position'] = $item->fee_position;
                        $this->m_event_fee_data->insert($insert_temp, TRUE);
                    }
                    $data_fee_item = $this->m_event_fee_item->get_many_by(['event_id' => $data_copy->id]);
                    foreach ($data_fee_item as $item) {
                        $item_temp = (array)$item;
                        $item_temp['event_id'] = $insert_id;
                        $item_temp['fee_id'] = $fee_id;
                        $item_temp['user_created'] = $user_created;
                        $item_temp['time_created'] = $time_created;
                        $item_temp['time_modified'] = $time_modified;
                        unset($item_temp['id']);
                        unset($item_temp['fee_item_id']);
                        unset($item_temp['fee_position']);

                        $item_insert = $this->m_event_fee_item->insert($item_temp, TRUE);
                        if ($item_insert) {
                            $data_fee_item_data = $this->m_event_fee_item_data->get_many_by(['fee_item_id' => $item->id]);
                            foreach ($data_fee_item_data as $item_data) {
                                $item_data_temp = (array)$item_data;
                                $item_data_temp['fee_item_id'] = $item_insert;
                                $item_data_temp['user_created'] = $user_created;
                                $item_data_temp['time_created'] = $time_created;
                                $item_data_temp['time_modified'] = $time_modified;
                                unset($item_data_temp['id']);
                                unset($item_data_temp['item_position']);
                                unset($item_data_temp['ship_type']);
                                unset($item_data_temp['tax']);
                                $this->m_event_fee_item_data->insert($item_data_temp, TRUE);
                            }
                        }
                    }
                    $data_fee_ship = $this->m_event_fee_item_shipping->get_many_by(['event_id' => $data_copy->id]);
                    foreach ($data_fee_ship as $item) {
                        $ship_temp = (array)$item;
                        $ship_temp['event_id'] = $insert_id;
                        $ship_temp['fee_item_id'] = $fee_id;
                        unset($ship_temp['id']);
                        $this->m_event_fee_item_shipping->insert($ship_temp, TRUE);
                    }
                }

                // AnhLD - pharse 6 - copy subscriber card setting
                $this->event_lib->copy_subscriber_card_setting($event_id_copy, $insert_id);
            }
            $this->session->unset_userdata("temp_create_data");//Reset session data
            $this->session->unset_userdata("temp_event_id_copy");//Reset session data

            if (isset($data['subcrisebers_expected'])) {
                $subcrisebers_expected = mb_convert_kana($data['subcrisebers_expected'], 'n');
                if((int) $subcrisebers_expected >= 10000) {
                    //send mail bcc
                    $id_user = $this->session->userdata("user_id");
                    $original_data = $this->m_user->get($id_user);
                    $this->load->config('site_settings');
                    $bcc = $this->config->item('contact_bcc_admin_email');
                    $to = $original_data->email;
                    $subject = my_lang('【フリッパ】新規イベントの追加完了', $data['lang']);
                    $data['title_body'] = my_lang('下記のイベント情報が新規追加されました。', $data['lang']);
                    $data['user_email'] = $original_data->email;
                    $data['user_id'] = $original_data->id;
                    $content = $this->load->view('email/event', $data, TRUE);
                    $this->email_lib->send_email($subject, $content, $to, '', '', array(), array(), $bcc, null, 'text', $data['lang']);
                }
            }
            echo json_encode($data_return);
            return $insert_id;
        } else {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    function get_category_child($id) {
        $data_return['state'] = 1;
        $data_return['msg'] = '';
        $data_return['data'] = [];
        if ($id) {
            $data_return['data'] = $this->m_event_category->get_many_by(['parent_id' => $id]);
            foreach ($data_return['data'] as $item) {
                $item->cat_name = my_lang($item->cat_name);
                $item->cat_description = my_lang($item->cat_description);
            }
        }
        echo json_encode($data_return);
        return TRUE;
    }

    /**
     * Form view application setting
     *
     * @param int $id : id event
     *
     * @return bool
     */
    function application_form($id = 0) {
        $data = array();

        $this->set_data_part("title", my_lang('予約フォーム設定'), FALSE);
        $this->load_more_js("assets/manager/js/application.js", TRUE);
        //Get draft page
        $page_draft = $this->event_lib->_draft_application;
        $draft_redirect = $this->event_lib->_draft_redirect();
        //Check event
        $check_event = $this->_check_event_step($id, $page_draft);
        if (!$check_event) {
            return FALSE;
        }
        $data['event_info'] = $check_event['data'];
//        $check_event = $this->event_lib->check_event_active($id, $data['event_info']);
//        if (!($check_event['state'])) {
//            $data['is_view'] = TRUE;
//        }

        $data['event_id'] = $id;
        $data['event_info']->name = $this->event_lib->format_event_name($data['event_info']->name, '');
        $data['event_name'] = isset($data['event_info']->name) ? $data['event_info']->name : '';;
        $data['draft_step'] = isset($data['event_info']->draft) ? $data['event_info']->draft : '';
        $data['page_draft'] = $page_draft;
        //Link save
        $data['save_temp_link'] = site_url($this->name["class"] . "/temp_application_form_save");
        $data['save_link'] = site_url($this->name["class"] . "/application_form_save/" . $id);
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        if ($is_draft == 1) {
            $data['save_temp_link'] .= '?is_draft=1';
            $data['save_link'] .= '?is_draft=1';
            $data['notify_draft'] = my_lang('新規イベントの登録が完了しておりません、引き続き').'<br/>'
                .my_lang('下記のステップで新規イベントをご登録ください。 内容の変更及び、必須項目以外は登録後にもご入力いただけます');
        }
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav($page_draft, $data);

        $data['count_subscriber_form'] = $this->m_event_form_data->get_list_filter_count([
            'event_id'  => $id,
            'type_form' => $this->application_lib->_form_subscriber,
        ], [], []);

        $data['list_item_form'] = $this->event_lib->get_data_item_form_application($id, 'setting');

        //Event fee form
        $data['fee_item_ids'] = '';
        $this->m_event_fee->set_soft_delete(FALSE);
        $data['fee_info'] = $this->m_event_fee->get_by(['event_id' => $id]);
        $fee_items = $this->m_event_fee_item->get_list_filter(
            ['m.event_id' => $id], [], []);
        $fee_item_ids = [];
        $fee_item_free_ids = [];
        if (count($fee_items)) {
            foreach ($fee_items as $item) {
                if (isset($item->item_type) && $item->item_type == 0) {
                    $fee_item_free_ids[] = $item->fee_item_id;
                } else {
                    $fee_item_ids[] = $item->fee_item_id;
                }
            }
        }
        if (count($fee_item_ids)) {
            $data['fee_item_ids'] = json_encode($fee_item_ids);
        }
        if (count($fee_item_free_ids)) {
            $data['fee_item_free_ids'] = json_encode($fee_item_free_ids);
        }
        $data['banking_url'] = site_url('manager/event_fee/form_receiver_banking/' . $id);
        $data['combini_url'] = site_url('manager/event_fee/form_receiver_combini/' . $id);
        $data['cashier_url'] = site_url('manager/event_fee/form_receiver_cashier/' . $id);
        $check_event = $this->event_lib->check_event_active($id, $data['event_info']);
        if (!($check_event['state'])) {
            if (!empty($data['fee_info']->id) && $data['fee_info']->is_fee) {
                $data['fee_item_url'] = site_url('manager/event_fee/fee_item/' . $id);
                $data['fee_item_free_url'] = '';
            } else {
                $data['fee_item_url'] = '';
                $data['fee_item_free_url'] = site_url('manager/event_fee/free_fee_item/' . $id);
            }
        } else {
            $data['fee_item_url'] = site_url('manager/event_fee/fee_item/' . $id);
            $data['fee_item_free_url'] = site_url('manager/event_fee/free_fee_item/' . $id);
        }

        $data['subscriber_card_setting_url'] = site_url('manager/event_subscriber_card_setting/subscriber_card_setting/' . $id);

        $data['list_transaction_type'] = $this->m_event_subscriber->get_transaction_type();
//        setting hide fee_form
//        isset($data['fee_info']->deleted) ? ($data['fee_info']->deleted = 1) : '';
        $data['is_pay_cash_payment'] = $data['event_info']->is_pay_cash_payment;
        $step_next = $page_draft + 1;
        $step_previous = $page_draft - 1;
        $data['step_next'] = isset($draft_redirect[$step_next]) ? site_url($draft_redirect[$step_next] . $id) : '#';
        $data['step_previous'] = isset($draft_redirect[$step_previous]) ? site_url($draft_redirect[$step_previous] . $id) : '#';
        if(!empty($data['event_info']->event_id_copy)){
            $data['event_coppy'] = $this->m_event->get_event_fee($data['event_info']->event_id_copy);
        }
        $data['fee_form_html'] = $this->load->view("manager/event/fee_form", $data, TRUE);

        //Load content
        $this->load_more_js("assets/manager/js/application.js");
        $this->load_more_js("assets/manager/js/custom_field.js");
        $lang = get_cookie('lang') == 'english' ? "en" : "ja";
        $this->load_more_js("assets/plugins-bower/jquery-ui/ui/i18n/datepicker-$lang.js", TRUE);
        $this->load_more_js("assets/manager/js/event_fee.js");
        $this->load_more_js("assets/front/js/rule.js");
        $this->load_more_js("assets/js/manager/validate-form.js");
        $this->load_more_css("assets/manager/css/custom_field.css");
        $this->load_more_css("assets/manager/css/event_fee.css");
        $data['confirm'] = '';
        $data['scrollToFee'] = $this->session->userdata('scrollToFee');
        $this->session->set_userdata('scrollToFee', null);
        $data['content'] = $this->load->view("manager/event/application_form", $data, TRUE);

        $content = $this->load->view("manager/event/content_event_step", $data, TRUE);
        $this->show_page($content);
    }

    /**
     * Logic save session data setting application form
     */
    public function temp_application_form_save() {
        $data = $this->input->post();
        $data = $this->_reduce_event_fee($data);
        if (count(array_intersect([18, 19], $data['field_id'])) === count([18, 19])) {
            $data_return['state'] = 0;
            $data_return["msg"] = my_lang('同伴者数（自由入力）または同伴者数（1、2、3、4、5、6、7、8、9、10）のみを選択してください');
            echo json_encode($data_return);
            return false;
        }
        $event = $this->model->get($data['event_id']);
        $data_return = Array();
        if(in_array("CASHIER", $data["transaction_type"])){
            if(!$event->is_pay_cash_payment){
                $data_return['state'] = 0;
                $data_return["msg"] = my_lang('現金決済は、現金選択時に表示されるポップアップ画面にて <br> ご利用料金をお支払い後に、ご利用可能となります。');
                $data_return['callback'] = "show_error_pay_usage_fee";
                $data_return['error']['transaction_type_cashier'] =  my_lang('現金決済は、現金選択時に表示されるポップアップ画面にて <br> ご利用料金をお支払い後に、ご利用可能となります。');
                echo json_encode($data_return);
                return false;
            }
        }
        if ($data && count($data)) {
            $event_id = isset($data['event_id']) ? $data['event_id'] : 0;
            //Get draft page
            $page_draft = $this->event_lib->_draft_application;
            $draft_redirect = $this->event_lib->_draft_redirect();

            //Check event
            $check_event = $this->event_lib->check_event_draft($event_id, [], $page_draft);
            $type_ticket = isset($check_event['data']->type_ticket) ? $check_event['data']->type_ticket : '';
            //Validate event fee
            $validate_event_fee = $this->_validate_event_fee($event_id, $data, $type_ticket);

            if (!($check_event['state'])) {
                echo json_encode(array_merge($validate_event_fee, $check_event));
                return FALSE;
            }
            if (!(isset($data['field_id']) && count($data['field_id'])) && empty($data['not_application_form'])) {
                $data_return["state"] = 0;
                $data_return["msg"] = my_lang('操作が失敗しました。');
                echo json_encode(array_merge($validate_event_fee, $data_return));
                return FALSE;
            }

            if (isset($data['field_id'])) {
                // validate if user choose both friend ticket button, user are supposed  not to do so
                $validate_friend_ticket = $this->m_event_form_field->validate_friend_ticket($data['field_id']);
                if (!$validate_friend_ticket['state']) {
                    echo json_encode($validate_friend_ticket);
                    return FALSE;
                }
            }
            //Check field input mail
            $list_input_required = isset($data['required']) ? $data['required'] : [];
            $check_email_required = $this->event_lib->check_application_input_email_required($event_id, $check_event['data'], $list_input_required);
            if (!($check_email_required['state'])) {
                echo json_encode(array_merge($validate_event_fee, $check_email_required));
                return FALSE;
            }
            if ((isset($validate_event_fee['state']) && !($validate_event_fee['state']))) {
                if($event->event_id_copy){
                    $event_copy = $this->model->get($event->event_id_copy);
                    $event_fee_copy = $this->m_event_fee->get_by(['event_id' => $event_copy->id]);
                    if($event_fee_copy->is_fee !== $data['is_fee_fake']){
                        $data_return['error']['is_fee_fake'] = '複写したイベントでは課金の変更できません';
                    }
                }
                $validate_event_fee["msg"] = my_lang('操作が失敗しました。');
                echo json_encode($validate_event_fee);
                return FALSE;
            }

            if (isset($validate_event_fee['is_fee']) && !$validate_event_fee['is_fee']) {
                $validate_event_fee['data'] = [
                    'event_id'         => $event_id,
                    'transaction_type' => '',
                    'is_tax'           => '',
                    'tax'              => '',
                    'note1'            => '',
                    'note2'            => '',
                    'note3'            => '',
                    'note4'            => '',
                    'is_free'          => 1,
                ];
                $data['is_fee'] = 0;
            } else {
                $data['is_fee'] = 1;
            }
            $data_return['callback'] = "form_application_event_check_response";
            $this->load->model('m_event_form_field');
            $this->session->set_userdata("temp_application_form_data", $data);

            $event_fee_data = $this->session->userdata("temp_event_fee_data");
            $event_fee_data[$event_id] = $validate_event_fee['data'];
            $this->session->set_userdata("temp_event_fee_data", $event_fee_data);
            $data['list_item_form'] = [];
            if (isset($data['field_id'])) {
                $arr_field_id_new = [];
                foreach ($data['field_id'] as $item) {
                    $arr_field_id_new[$item] = $item;
                }

                //Anhld
                $this->m_event_form_data->include_text_view_field(TRUE);

                $list_item_form_data = $this->m_event_form_data->get_list_filter([
                    'event_id'  => $data['event_id'],
                    'type_form' => $this->application_lib->_form_subscriber,
                ], ['field_id' => $data['field_id']], [], 0, 0, ['m.position' => 'ASC']);
                if (count($list_item_form_data)) {
                    foreach ($list_item_form_data as $item) {
                        if (isset($arr_field_id_new[$item->field_id])) {
                            unset($arr_field_id_new[$item->field_id]);
                        }
                    }
                }
                $data['list_item_form'] = $this->event_lib->process_data_application_form($list_item_form_data);
                if (count($arr_field_id_new)) {
                    $list_item_form = $this->m_event_form_field->get_many($arr_field_id_new);
                    $data['list_item_form'] = array_merge($data['list_item_form'], $this->event_lib->process_data_application_form($list_item_form));
                }
            }
            if(isset($data['checkRedirectFee']) && $data['checkRedirectFee'] === "true"){
                $page_draft = $this->event_lib->_draft_ticket;
                $draft_redirect = $this->event_lib->_draft_redirect();
                $link_redirect = isset($draft_redirect[$page_draft]) ? ($draft_redirect[$page_draft] . $data['event_id']) : '';
                $data_return["redirect"] = site_url($link_redirect);
                $data_return["temp_form_save"] = true;
            }
            if(isset($data['checkRedirectFreeFee']) && $data['checkRedirectFreeFee'] === "true"){
                $page_draft = $this->event_lib->_draft_ticket;
                $draft_redirect = $this->event_lib->_draft_redirect();
                $link_redirect = isset($draft_redirect[$page_draft]) ? ($draft_redirect[$page_draft] . $data['event_id']) : '';
                $data_return["redirect"] = site_url($link_redirect);
                $data_return["temp_form_save"] = true;
            }
            $data_return["data"] = $data;
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
            $data['form_type'] = 'view'; //view form application conf
            $data['event'] = $check_event['data'];
            $data_return['has_sort'] = 'has_sort';
            $data['event']->name = $this->event_lib->format_event_name($data['event']->name, '');
            $data_return["content"] = $this->event_lib->get_view_application_form($event_id, 0, $data);
        } else {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["state"] = 0;
        }

        if($event->event_id_copy && $event->event_id_copy != 0 && $event->event_id_copy != 1){
            $event_fee_original = $this->m_event_fee->get_by(['event_id' => $event->id]);
            if(empty($event_fee_original)){
                $event_fee_original->is_fee = 0;
            }
            if($event_fee_original->is_fee != $data['is_fee_fake']){
                $data_return['state'] = 0;
                $data_return["msg"] = my_lang('データが無効です。');
                $data_return['error']['is_fee_fake'] = my_lang('複写したイベントでは課金の変更できません');
            }
        }

        echo json_encode($data_return);
    }

    private function _reduce_event_fee($data) {
        if (isset($data['note1'])) $data['note1'] = $this->_reduce_note_of_event_fee($data['note1']);
        if (isset($data['note2'])) $data['note2'] = $this->_reduce_note_of_event_fee($data['note2']);
        if (isset($data['note3'])) $data['note3'] = $this->_reduce_note_of_event_fee($data['note3']);
        if (isset($data['note4'])) $data['note4'] = $this->_reduce_note_of_event_fee($data['note4']);
        return $data;
    }

    /**
     * Remove `script` tag and replace line break by `br` tag from `$note` (also uglify)
     *
     * @param $note string
     * @return string
     */
    private function _reduce_note_of_event_fee($note) {
        $note = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $note);
        $note = nl2br($note);
        $note = preg_replace("/\r\n|\r|\n/", '', $note);
        return $note;
    }


    /**
     * Logic save data setting application form
     *
     * @param int $id : id event
     *
     * @return bool|void
     */
    public function application_form_save($id = 0) {
        $data = $this->session->userdata("temp_application_form_data");
        $data['position_sort'] = $this->input->post('position');
        $data['field_id_sort'] = $this->input->post('field_id');
        //Get session fee
        $session_fee = $this->session->userdata("temp_event_fee_data");
        $data_fee = isset($session_fee[$id]) ? $session_fee[$id] : [];
        $data['is_fee'] = (count($data_fee) && isset($data['fee_deleted']) && !$data['fee_deleted']) ? TRUE : FALSE;
        if (isset($data['fee_deleted'])) {
            $data_fee['deleted'] = $data['fee_deleted'];
        }
        $data_fee['fee_position'] = $this->input->post('fee_position');
        $data_fee['fee_item_id'] = $this->input->post('fee_item_id');
        $data_fee['default_fee'] = $this->input->post('default_fee');
        $data_return = [];
        //Get draft page
        $page_draft = $this->event_lib->_draft_application;
        $ticket_redirect = $this->event_lib->_draft_ticket;
        $draft_redirect = $this->event_lib->_draft_redirect();
        //Set redirect
        $page_draft_next = $page_draft + 1;
        $link_redirect = isset($draft_redirect[$page_draft_next]) ? ($draft_redirect[$page_draft_next] . $id) : '';
        if (isset($data['checkRedirect']) && $data['checkRedirect'] == "true") {
            $link_redirect = $draft_redirect[$ticket_redirect] . $id;
        }
        if(isset($data['checkStep']) && $data['checkStep'] === "true"){
            $this->session->set_userdata('scrollToFee', $data['checkStep']);
            $link_redirect = isset($draft_redirect[$page_draft]) ? ($draft_redirect[$page_draft] . $id) : '';
        };
        $data_return["redirect"] = site_url($link_redirect);
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        if ($is_draft == 1) {
            $data_return["redirect"] .= '?is_draft=1';
        }
        $data_return = $this->event_lib->save_event_application_form($id, $data, $data_return);
        if (!$data_return['state']) {
            echo json_encode($data_return);
            return FALSE;
        }
        $data_return = array_merge($data_return, $this->event_lib->save_event_fee_form($id, $data_fee));

        echo json_encode($data_return);
    }

    protected function _validate_event_fee($id, $data, $type_ticket = '') {
        $data_return = [
            'data' => [],
        ];
        if (isset($data['fee_deleted']) && !$data['fee_deleted']) {
            $total = $this->m_event_fee_item->get_list_filter_count(
                ['m.event_id' => $id, 'item_type' => 1], [], []);
            if (!($total > 0)) {
                $data_return['state'] = 0;
                $data_return['error']['fee_item_ids'] = my_lang('料金設定がご入力されておりません。');
            }
            $types = [];
            if (isset($data['transaction_type'])) {
                $types = $data['transaction_type'];
                $data['transaction_type'] = json_encode($data['transaction_type']);
            }
            if (!empty($data['is_tax'])) {
                $schema = $this->m_event_fee->schema;
                if (isset($schema['tax']['rules'])) {
                    $schema['tax']['rules'] = $schema['tax']['rules'] . '|required';
                }
                //Set schema
                $this->m_event_fee->schema = $schema;
            }
            $validate = $this->m_event_fee->validate($data);
            if (!$validate) {
                $data_return['state'] = 0;
                if (isset($data_return['error'])) {
                    $data_return['error'] = array_merge($data_return['error'], $this->m_event_fee->get_validate_error());
                } else {
                    $data_return['error'] = $this->m_event_fee->get_validate_error();
                }
            }
            if (in_array('COMBINI', $types)) {
                $cashier = $this->m_event_fee_combini->get_by(['event_id' => $id]);
                if (!isset($cashier->id)) {
                    $data_return['state'] = 0;
                    $data_return['error_transaction_type']['COMBINI'] = my_lang('コンビニ情報の設定は必須となります。');
                }
            }
            if (in_array('BANKING_CARD', $types)) {
                $banking = $this->m_event_fee_banking->get_by(['event_id' => $id]);
                if (!isset($banking->id)) {
                    $data_return['state'] = 0;
                    $data_return['error_transaction_type']['BANKING_CARD'] = my_lang('お受取口座情報は必須となります。');

                }
            }
            if (in_array('CASHIER', $types)) {
                $cashier = $this->m_event_fee_cashier->get_by(['event_id' => $id]);
                if (!isset($cashier->id)) {
                    $data_return['state'] = 0;
                    $data_return['error_transaction_type']['CASHIER'] = my_lang('お受取方法の設定は必須となります。');
                }

                // AnhLD - pharse 8 - if type ticket = 2 => alert error
                if($type_ticket == 2){
                    $data_return['state'] = 0;
                    $data_return['error_transaction_type']['CASHIER'] = my_lang('発券方法で手動発券を選択していますので、現金は設定できません。');
                }

            }
            $data_return['callback'] = 'form_setting_application_submit_response';
            $data_return['data'] = $validate;
        } else if (isset($data['fee_item_free_ids']) && !empty($data['fee_item_free_ids'])) {
            $total = $this->m_event_fee_item->get_list_filter_count(
                [
                    'm.event_id'  => $id,
                    'm.item_type' => 0,
                ], [], []);
            if ($total) {
                $data_return['is_fee'] = FALSE;
            }
        }
        return $data_return;
    }

    /**
     * View embedded code event
     *
     * @param int $id : id event
     *
     * @return bool
     */
    public function embedded_code($id = 0) {
        $this->set_data_part("title", my_lang('予約フォーム設置'), FALSE);
        $data = array();
        //Get draft page
        $page_draft = $this->event_lib->_draft_embedded;
        $draft_redirect = $this->event_lib->_draft_redirect();

        //Check event
        $check_event = $this->_check_event_step($id, $page_draft);
        if (!$check_event) {
            return FALSE;
        }
        $data['event_info'] = $check_event['data'];
        $data['event_id'] = $id;
        $data['draft_step'] = isset($data['event_info']->draft) ? $data['event_info']->draft : '';
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav($page_draft, $data);
        //Load css + js more
        $this->load_more_css("assets/manager/css/embedded_code.css");
        $this->load_more_js("assets/plugins-bower/clipboard/dist/clipboard.min.js", TRUE);
        $this->load_more_js("assets/manager/js/embedded_code.js", TRUE);
        //Get content
        $step_next = $page_draft + 1;
        $step_previous = $page_draft - 1;
        $data['save_link'] = site_url("manager/event/embedded_save_step/" . $id);
        $data['step_previous'] = isset($draft_redirect[$step_previous]) ? site_url($draft_redirect[$step_previous] . $id) : '#';

        $data['send_mail_template'] = site_url($this->name["class"] . "/send_mail_template_embedded_code/" . $id);
        $data['content'] = $this->load->view("manager/event/embedded_code", $data, TRUE);
        $content = $this->load->view("manager/event/content_event_step", $data, TRUE);
        $this->show_page($content);
    }

    public function embedded_save_step() {
        $data = $this->input->post();
        if (is_array($data) && count($data) && !empty($data['event_id'])) {
            $id = $data['event_id'];
            //Get draft page
            $page_draft = $this->event_lib->_draft_embedded;
            $draft_redirect = $this->event_lib->_draft_redirect();
            //Check event
            $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
            if (!(is_array($check_event) && $check_event['state'])) {
                echo json_encode($check_event);
                return FALSE;
            }

            $page_draft_next = $page_draft + 1;
            $data = array(
                'draft'        => $page_draft_next,
                'updated_time' => date("Y-m-d H:i:s"),
            );

            $link_redirect = isset($draft_redirect[$page_draft_next]) ? ($draft_redirect[$page_draft_next] . $id) : '';
            $data_return["redirect"] = site_url($link_redirect);
            //Check status draft
            $is_draft = $this->input->get('is_draft');
            if ($is_draft == 1) {
                $data_return["redirect"] .= '?is_draft=1';
            }
            $data_return['callback'] = 0;
            parent::edit_save($id, $data, $data_return, $skip_validate = TRUE);
        } else {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    public function set_time_next_step($id = 0) {
        if (!empty($id)) {
            //Get draft page
            $page_draft = $this->event_lib->_draft_time_subscriber;
            $draft_redirect = $this->event_lib->_draft_redirect();
            //Check event
            $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
            if (!(is_array($check_event) && $check_event['state'])) {
                echo json_encode($check_event);
                return FALSE;
            }

            $page_draft_next = $page_draft + 1;
            $data = array(
                'draft'        => $page_draft_next,
                'updated_time' => date("Y-m-d H:i:s"),
            );

            $link_redirect = isset($draft_redirect[$page_draft_next]) ? ($draft_redirect[$page_draft_next] . $id) : '';
            $data_return["redirect"] = site_url($link_redirect);

            //Check status draft
            $is_draft = $this->input->get('is_draft');
            if ($is_draft == 1) {
                $data_return["redirect"] .= '?is_draft=1';
            }
            $data_return['callback'] = 'event_default_ajax_link';
            parent::edit_save($id, $data, $data_return, $skip_validate = TRUE);
        } else {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    /**
     * @param $id
     *
     * @return bool
     */
    public function setting_time_subscriber($id) {
        $this->load_more_css("assets/manager/css/event_detail.css");
        $this->load_more_js("assets/manager/js/event_detail_edit.js", TRUE);
        $this->set_data_part("title", my_lang('電子チケット設定'), FALSE);


        //Get draft page
        $page_draft = $this->event_lib->_draft_time_subscriber;
        $draft_redirect = $this->event_lib->_draft_redirect();

        //Check event
        $check_event = $this->_check_event_step($id, $page_draft);
        if (!$check_event) {
            return FALSE;
        }
        $data = [
            'step'      => $page_draft,
            'view_type' => 'subscriber',
        ];
        $data['event'] = $check_event['data'];
        $data['event_id'] = $id;
        $data['draft_step'] = isset($data['event']->draft) ? $data['event']->draft : '';
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav($page_draft, $data);
        //Get content

        $check_event = $this->event_lib->check_event($id, []); //edit time subscriber=> $skip_check_user = FALSE
        $save_link = '';
        $data['next_step'] = site_url('manager/event/set_time_next_step/' . $id);
        if (($check_event['state'])) {
            $update_finish_link = 'manager/event_edit/update_finish_application_form/' . $id;
            $setting_link = 'manager/event_edit/edit_save_time_subscriber/' . $id;
            $save_link = 'manager/event_edit/check_validate_time_subscriber/' . $id;
            $data_button = [];
            $data_button['next_step'] = isset($data['next_step']) ? $data['next_step'] : "";
            $button_accept = $this->load->view('manager/event_edit/button_time_subscriber_view', $data_button, TRUE);
        } else {
            $button_accept = '<div class="event_finish_msg">'. my_lang('このイベントは終わりました。').'</div>';
        }

        $data['save_link'] = site_url($save_link);

        $data['setting_link'] = isset($setting_link) ? site_url($setting_link) : '';
        $data['update_finish_link'] = isset($update_finish_link) ? site_url($update_finish_link) : '';
        $data['view_file'] = "manager/event_edit/form_time_subscriber";
        $data['button_accept'] = isset($button_accept) ? $button_accept : '';
        $this->_form_edit_event_time($id, $data);
    }

    protected function _form_edit_event_time($id, $data) {
        $data_button = [];
        // check permission
        $this->application_lib->check_permission_application($data['event']->user_id, 0, FALSE);
        $data['event_id'] = $id;
        $data['list_hours'] = $this->event_lib->get_data_hours();
        $data['list_minutes'] = $this->event_lib->get_data_minutes();
        $data['save_link'] = $data['save_link'];
        $data_button['setting_link'] = isset($data['setting_link']) ? $data['setting_link'] : '';
        $lang = get_cookie('lang') == 'english' ? "en" : "ja";
        $this->load_more_js("assets/plugins-bower/jquery-ui/ui/i18n/datepicker-$lang.js", TRUE);
        //Load content
        $data['event']->status_text = $data['event']->status;
        $data['event']->status = $this->model->get_status_text($data['event']->status);
        $data['event']->name = $this->event_lib->format_event_name($data['event']->name, '');

        $content = $this->load->view($data['view_file'], $data, TRUE);
        $this->show_page($content);
    }

    /**
     * @param $id
     *
     * @return bool
     */
    public function setting_time_checkin($id) {
        $this->load_more_css("assets/manager/css/event_detail.css");
        $this->load_more_js("assets/manager/js/event_detail_edit.js", TRUE);

        $page_draft = $this->event_lib->_draft_time_checkin;
        $draft_redirect = $this->event_lib->_draft_redirect();

        //Check event
        $check_event = $this->_check_event_step($id, $page_draft);
        if (!$check_event) {
            return FALSE;
        }
        $data = [
            'step'         => $page_draft,
            'view_type'    => 'checkin',
            'step_setting' => 'create',
            'save_link'    => site_url('manager/event_edit/edit_save_time_checkin/' . $id),
            'view_file'    => "manager/event_edit/form_time_checkin",
        ];

        $data['event'] = $check_event['data'];
        $data['event_id'] = $id;
        $data['draft_step'] = isset($data['event']->draft) ? $data['event']->draft : '';
        //Link ticket_form_save
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        $data['notify_draft'] = my_lang('新規イベントの登録が完了いたしました。').'<br/>'
            .my_lang('また、登録内容の変更はイベントTOPのページから可能となります。');
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav($page_draft, $data);

        $this->_form_edit_event_time($id, $data);
    }

    function application_form_html($id) {
        $data = Array(
            'event_id'    => $id,
            'button_text' =>  my_lang('閉じる'),
        );
        $html = $this->load->view("manager/event/application_form_html", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    /**
     * View ticket form
     *
     * @param int $id : id event
     *
     * @return bool
     */
    public function ticket_form($id = 0) {
        $this->set_data_part("title", my_lang('電子チケット設定'), FALSE);
        $data = array();
        //Get draft page
        $page_draft = $this->event_lib->_draft_ticket;
        $draft_redirect = $this->event_lib->_draft_redirect();
        //Check event
        $check_event = $this->_check_event_step($id, $page_draft);
        if (!$check_event) {
            return FALSE;
        }
        $data['event_info'] = $check_event['data'];
        $data['event_id'] = $id;
        $data['draft_step'] = isset($data['event_info']->draft) ? $data['event_info']->draft : '';
        //Link ticket_form_save
        $data['save_link'] = site_url($this->name["class"] . "/ticket_form_save");
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        if ($is_draft == 1) {
            $data['save_link'] .= '?is_draft=1';
            $data['notify_draft'] = my_lang('新規イベントの登録が完了しておりません、引き続き').'<br/>'
                .my_lang('下記のステップで新規イベントをご登録ください。 内容の変更及び、必須項目以外は登録後にもご入力いただけます');
        }
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav($page_draft, $data);

        $step_next = $page_draft + 1;
        $step_previous = $page_draft - 1;
        $data['step_next'] = isset($draft_redirect[$step_next]) ? site_url($draft_redirect[$step_next] . $id) : '#';
        $data['step_previous'] = isset($draft_redirect[$step_previous]) ? site_url($draft_redirect[$step_previous] . $id) : '#';
        if (isset($check_event['data']->draft) && $check_event['data']->draft >= 5 && $check_event['data']->status != 'in_preparation') {
            $data['edit_status'] = FALSE;
        } else {
            $data['edit_status'] = TRUE;
        }
        $data['ticket_button'] = $this->load->view("manager/event/ticket_button", $data, TRUE);
        $data['template_ticket_content'] = $this->event_lib->get_view_template_ticket($id, $data['event_info'], NULL, TRUE);

        $data['caller'] = 'event';
        $data['content'] = $this->load->view("manager/event/ticket_form", $data, TRUE);
        $content = $this->load->view("manager/event/content_event_step", $data, TRUE);
        $this->load_more_js("assets/manager/js/ticket.js");
        $this->show_page($content);
    }

    /**
     * Ticket form save
     *
     * @return bool
     */
    public function ticket_form_save() {
        $data = $this->input->post();
        $id = !empty($data['event_id']) ? $data['event_id'] : 0;
        //Get draft page
        $page_draft = $this->event_lib->_draft_ticket;
        $draft_redirect = $this->event_lib->_draft_redirect();
        //Set redirect
        $page_draft_next = $page_draft + 1;
        $link_redirect = isset($draft_redirect[$page_draft_next]) ? ($draft_redirect[$page_draft_next] . $id) : '';
        $data_return["redirect"] = site_url($link_redirect);
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        if ($is_draft == 1) {
            $data_return["redirect"] .= '?is_draft=1';
        }

        echo json_encode($this->event_lib->save_event_ticket_form($id, $data, $data_return));
    }

    /**
     * Overview event
     *
     * @param int $id
     *
     * @return bool
     */
    public function overview($id = 0) {
        $data = array();
        $this->set_data_part("title", my_lang('自動返信メール設定'), FALSE);
        //Get draft page
        $page_draft = $this->event_lib->_draft_overview;
        $draft_redirect = $this->event_lib->_draft_redirect();
        //Check event
        $check_event = $this->_check_event_step($id, $page_draft);
        if (!$check_event) {
            return FALSE;
        }
        $getDataTranSaction = json_decode($this->m_event_fee->get_by(['event_id' => $id])->transaction_type);
        $data['getDataTranSaction'] = $getDataTranSaction;
        //Check input email in form application add
        $data['check_input_email'] = $this->event_lib->check_application_input_email($id);

        $data['event_info'] = $check_event['data'];
        $data['event_id'] = $id;
        $data['draft_step'] = isset($data['event_info']->draft) ? $data['event_info']->draft : '';
        $data['event_qr'] = $this->event_lib->ticket_qr($data['event_info']->name);

        $data['save_link'] = site_url($this->name["class"] . "/overview_save");
        //Check status draft
        $is_draft = $this->input->get('is_draft');
        if ($is_draft == 1) {
            $data['save_link'] .= '?is_draft=1';
            $data['notify_draft'] = my_lang('新規イベントの登録が完了しておりません、引き続き').'<br/>'
                .my_lang('下記のステップで新規イベントをご登録ください。 内容の変更及び、必須項目以外は登録後にもご入力いただけます');
        }
        //Get step nav
        $data['step_nav'] = $this->event_lib->_load_step_nav($page_draft, $data);
        //Get view ticket form
        $data['template_ticket_content'] = $this->event_lib->get_view_template_ticket($id, $data['event_info']);
        //Get view application form
        $data['form_type'] = 'email';
        $data['event_type'] = $this->m_event_fee->check_event_is_fee($id);
        $data['template_application_form_content'] = $this->event_lib->get_view_application_form($id, 0, $data);

        $step_next = $page_draft + 1;
        $step_previous = $page_draft - 1;
        $data['step_next'] = isset($draft_redirect[$step_next]) ? site_url($draft_redirect[$step_next] . $id) : '#';
        $data['step_previous'] = isset($draft_redirect[$step_previous]) ? site_url($draft_redirect[$step_previous] . $id) : '#';
        $data['url_video'] = isset($data['event_info']->url_video) ? $data['event_info']->url_video :  "";
        $data['send_mail_template'] = site_url($this->name["class"] . "/send_mail_template_overview/" . $id);
        $data['list_email'] = isset($data['event_info']->list_email) ? json_decode($data['event_info']->list_email) : [];
        $this->load_more_js("assets/manager/js/application.js");
        $this->load_more_css("assets/manager/css/application.css");
        $data['content'] = $this->load->view("manager/event/overview/overview", $data, TRUE);
        $content = $this->load->view("manager/event/content_event_step", $data, TRUE);
        $this->show_page($content);
    }

    function overview_save() {
        $data = $this->input->post();
        if (is_array($data) && count($data) && !empty($data['event_id'])) {
            $id = $data['event_id'];
            //Get draft page
            $page_draft = $this->event_lib->_draft_overview;
            $draft_redirect = $this->event_lib->_draft_redirect();
            //Check event
            $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
            if (!(is_array($check_event) && $check_event['state'])) {
                echo json_encode($check_event);
                return FALSE;
            }
            $event_type = $this->m_event_fee->check_event_is_fee($id);
            if ($this->m_event_form_data->has_email_field($id)) {
                $check_validate = $this->event_lib->validate_list_email($data);
                if (!(is_array($check_validate) && $check_validate['state'])) {
                    echo json_encode($check_validate);
                    return FALSE;
                } else {
                    if ((is_array($check_validate['list_email']) && count($check_validate['list_email']))) {
                        $list_email = json_encode($check_validate['list_email']);
                    }
                }

            }

            if ($event_type == 1) {
                $is_update_link = 0;
            } else {
				$is_update_link = isset($data['is_update_link']) ? $data['is_update_link'] : 0;
            }

            $page_draft_next = $page_draft + 1;
            $data_event = $this->m_event->get($id);
            if (isset($data_event->url_video)) {
                $data['inputUrl'] = $data_event->url_video;
            }
            $data = array(
                'draft'          => $page_draft_next,
                'is_update_link' => isset($is_update_link) ? $is_update_link : 0,
                'updated_time'   => date("Y-m-d H:i:s"),
                'list_email'     => isset($list_email) ? $list_email : '',
                'url_video'       => isset($data['inputUrl']) ? $data['inputUrl'] : "",
            );
            $link_redirect = '/manager/event_detail/index_top/' . $id;
            $data_return["redirect"] = site_url($link_redirect);
            //Check status draft
            $is_draft = $this->input->get('is_draft');
            if ($is_draft == 1) {
                $data_return["redirect"] .= '?is_draft=1';
            }

            parent::edit_save($id, $data, $data_return, $skip_validate = TRUE);
        } else {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    //AnhLD - pharse 8
    public function edit_mail($id = 0, $type = "send_ticket") {
        $data = Array();
        //Get draft page
        $page_draft = $this->event_lib->_draft_overview;
        //Check event
        $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
        if (!($check_event['state'])) {
            echo json_encode($check_event);
            return FALSE;
        }
        $data['event'] = $check_event['data'];
        $data['type'] = $type;
		$event_obj = $check_event['data'];
		$data['url_video'] = $event_obj->url_video;
        $data['event_id'] = $id;
        $data['field_name'] = $field_name = $type."_mail";
        $data['field_name_title'] = $field_name_title = 'title_' .$type."_mail";
        $data['event']->$field_name = str_replace("<br/>", '&#10;', $data['event']->$field_name);
        if ($data['event']->lang == "ja") {
            $data['event']->$field_name = trim(preg_replace('/\s+/', '', $data['event']->$field_name));
        }else{
            $data['event']->$field_name = trim($data['event']->$field_name);
        }
        $data['save_link'] = site_url("manager/event/edit_mail_save/".$type);
        $data['content'] = $data['event']->$field_name;
        $data['title'] = $data['event']->$field_name_title;

        if(empty($data['content'])){
            $data['content'] = $this->get_default_email_template($type);
        }

        $html = $this->load->view("manager/event/overview_edit_email_popup", $data, TRUE);
        $data_return["state"] = 1;
        $data_return["html"] = $html;

        echo json_encode($data_return);
        return TRUE;
    }

    // AnhLD - pharse 8
    public function get_default_email_template($type, $lang = 'ja'){
        $content = strip_tags($this->load->view("manager/email_template/overview_email_template/".$type, ['lang' => $lang], TRUE), "<br>");
        $content = implode("\n", array_map('trim', explode("\n", $content)));
        return $content;
    }

    // AnhLD - pharse 8
    public function edit_mail_save($type = "send_ticket") {
        $data = $this->input->post();
        $field_name = $type."_mail";
        $field_name_title = "title_".$type."_mail";
        if (is_array($data) && count($data) && !empty($data['event_id']) && !empty($data[$field_name])) {
            $id = $data['event_id'];
            //Get draft page
            $page_draft = $this->event_lib->_draft_overview;
            //Check event
            $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
            if (!(is_array($check_event) && $check_event['state'])) {
                echo json_encode($check_event);
                return FALSE;
            }
            $content = preg_replace("/\r\n|\r|\n/", '<br/>', trim($data[$field_name]));
            $content_field_name_title = trim($data[$field_name_title]);
            $data = array(
                $field_name => $content,
                'url_video' => $data['url_video'],
                'updated_time'  => date("Y-m-d H:i:s"),
            );
            // ID155
            if ($type != 'register_success') {
                $data[$field_name_title] = $content_field_name_title;
            }
            $data_return = array(
                "callback" => "edit_mail_e_ticket_response",
                "redirect" => "",
            );
            parent::edit_save($id, $data, $data_return, $skip_validate = TRUE);
        } else {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    public function edit_mail_e_ticket($id = 0) {
        $data = Array();
        //Get draft page
        $page_draft = $this->event_lib->_draft_overview;
        //Check event
        $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
        if (!($check_event['state'])) {
            echo json_encode($check_event);
            return FALSE;
        }
        $fee_info = $this->m_event_fee->get_by(['event_id' => $check_event['data']->id]);
        $hasCashier = false;
        if (isset($fee_info->transaction_type) && count($fee_info->transaction_type)) {
            foreach (json_decode($fee_info->transaction_type, TRUE) as $value) {
                if ($value == 'CASHIER') {
                    $hasCashier = true;
                }
            }
        }
        $data['event'] = $check_event['data'];
        $data['event_id'] = $id;
        $data['event']->e_ticket_mail = str_replace("<br/>", '&#10;', $data['event']->e_ticket_mail);
        if ($data['event']->lang == "ja") {
            $data['event']->e_ticket_mail = trim(preg_replace('/\s+/', '', $data['event']->e_ticket_mail));
        }else{
            $data['event']->e_ticket_mail = trim($data['event']->e_ticket_mail);
        }
        $data['save_link'] = site_url("manager/event/edit_mail_e_ticket_save");
        $data['field_name_title'] = $field_name_title = "title_e_ticket_mail";
        $data['title'] = my_lang($data['event']->$field_name_title);
        $data['hasCashier'] = $hasCashier;
        $html = $this->load->view("manager/event/overview_edit_popup", $data, TRUE);
        $data_return["state"] = 1;
        $data_return["html"] = $html;

        echo json_encode($data_return);
        return TRUE;
    }

    public function edit_mail_e_ticket_save() {
        $data = $this->input->post();
        if (is_array($data) && count($data) && !empty($data['event_id']) && !empty($data['e_ticket_mail'])) {
            $id = $data['event_id'];
            //Get draft page
            $page_draft = $this->event_lib->_draft_overview;
            //Check event
            $check_event = $this->event_lib->check_event_draft($id, [], $page_draft);
            if (!(is_array($check_event) && $check_event['state'])) {
                echo json_encode($check_event);
                return FALSE;
            }
            $event_fee = $this->m_event_fee->get_event_fee($id);
            if (isset($event_fee->id)) {
                $array_transaction_type = $event_fee->transaction_type;
                if(strpos($array_transaction_type, 'CASHIER') && !empty($data['url_video'])){
                    $data_return["state"] = 0;
                    $data_return["msg"] = my_lang('操作が失敗しました。');
                    echo json_encode($data_return);
                    return FALSE;
                }
            }
            //Check event accepting
//            $check_event_accepting = $this->event_lib->check_event_status_accepting($check_event['data']);
//            if (!($check_event_accepting['state'])) {
//                echo json_encode($check_event_accepting);
//                return FALSE;
//            }
            $content = preg_replace("/\r\n|\r|\n/", '<br/>', trim($data['e_ticket_mail']));
            $data = array(
                'e_ticket_mail' => $content,
                'title_e_ticket_mail' => isset($data['title_e_ticket_mail']) ? $data['title_e_ticket_mail'] : "",
                'updated_time'  => date("Y-m-d H:i:s"),
                'url_video'       => isset($data['url_video']) ? $data['url_video'] : "",
            );
            $data_return = array(
                "callback" => "edit_mail_e_ticket_response",
                "redirect" => "",
            );
            parent::edit_save($id, $data, $data_return, $skip_validate = TRUE);
        } else {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    function event_copy() {
        $data = Array();
        $this->model->unset_before_get('custom_select_for_user');
        $sample = $this->model->get_by(['type' => 'sample']);
        $arr_sample[] = $sample;
        $this->model->set_before_get('custom_select_for_user');
        $list_event = $this->model->get_list_filter([
            'm.draft>=' => '5',
        ], [], []);
        if (isset($sample->id)) {
            if (count($list_event)) {
                $list_event = array_merge($arr_sample, $list_event);
            } else {
                $list_event = $arr_sample;
            }
        }
        $data['list_event'] = $list_event;
        $html = $this->load->view("manager/event/copy_event", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    function check_event_copy() {
        $event_id = $this->input->post()['id'];
        $data_return = Array();
        $data_return['callback'] = "user_copy_check";
        $this->model->unset_before_get('custom_select_for_user');
        $event = $this->model->get($event_id);
        if (isset($event->id)) {
            $data_return['state'] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
            $event->name = preg_replace('#<br\s*/?>#i', "\n", $event->name);
            $event->event_id_copy = $event_id;
            $event->cat_name = my_lang($event->cat_name);
            $data_return['event'] = $event;
            $data_return['list_category_child'] = [];
            if (isset($event->parent_id) && $event->parent_id) {
                $data_return['list_category_child'] = $this->m_event_category->get_many_by(['parent_id' => $event->parent_id]);
                foreach ($data_return['list_category_child'] as $key => $item){
                    $data_return['list_category_child'][$key]->cat_name = my_lang($item->cat_name);
                    $data_return['list_category_child'][$key]->cat_description = my_lang($item->cat_description);
                }
            }
        } else {
            $data_return['state'] = 0;
            $data_return['msg'] = my_lang('イベントは存在しません。');
        }
        echo json_encode($data_return);
    }

    /**
     * Send mail template mail5 in step overview
     *
     * @param int $id
     *
     * @return bool
     */
    public function send_mail_template_overview($id = 0) {
        $data = $this->input->post();
        $data_return['callback'] = 'send_email_template_respone';
        if (is_array($data) && count($data)) {
            $check_null = 0;
            $data_email = array();
            foreach ($data as $key => $email) {
                $check_null = (!trim($email)) ? $check_null + 1 : $check_null;
                if ($email && !$this->valid_email($email)) {
                    $error[$key] = my_lang('欄はメールアドレスとして正しい形式でなければいけません');
                }
                if ($email && $this->valid_email($email)) {
                    $data_email[] = $email;
                }
            }
            if ($check_null == 3) {
                $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
                $data_return["msg"] = my_lang('メール情報は必要です。');
                echo json_encode($data_return);
                return FALSE;
            }
            if ($check_null < 3 && isset($error) && count($error)) {
                $data_return["error"] = $error;
                $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
                $data_return["msg"] = my_lang('メール情報は必要です。');
                echo json_encode($data_return);
                return FALSE;
            }
            $data['event'] = $this->model->get($id);
            if (!$data['event']) {
                $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
                $data_return["msg"] = my_lang('イベントは存在しません。');
                echo json_encode($data_return);
                return FALSE;
            }
            $data['form_type'] = 'email';
            $data['application_form'] = $this->event_lib->get_view_application_form($id, 0, $data);
            $data['ticket_form'] = $this->event_lib->get_view_template_ticket($id, $data['event']);
            $subject = str_replace(my_lang('[イベント名]'), '[' . $data['event']->name . ']', my_lang('[イベント名]電子チケットの送付'));
            $data['event']->e_ticket_mail = str_replace(my_lang('[イベント名]'), '[' . $data['event']->name . ']', $data['event']->e_ticket_mail);
            $content = $this->load->view("email/e_ticket", $data, TRUE);

            foreach ($data_email as $email) {
                $content = str_replace(my_lang('[埋め込みタグメールアドレス]'), $email, $content);
                $return = $this->email_lib->send_email($subject, $content, $email);
            }
            $data_return = array_merge((array)$return, $data_return);
            $data_return["msg"] = my_lang('操作が成功しました。');
            $data_return['html'] = $this->load->view("manager/event/success_modal", $data, TRUE);
            echo json_encode($data_return);
            return FALSE;
        } else {
            $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    /**
     * Send mail template mail4 in step embedded code
     *
     * @param int $id
     *
     * @return bool
     */
    public function send_mail_template_embedded_code($id = 0) {
        $data = $this->input->post();
        $data_return['callback'] = 'send_email_template_respone';
        if (is_array($data) && count($data)) {
            $check_null = 0;
            $data_email = array();
            foreach ($data as $key => $email) {
                $check_null = (!trim($email)) ? $check_null + 1 : $check_null;
                if ($email && !$this->valid_email($email)) {
                    $error[$key] = my_lang('欄はメールアドレスとして正しい形式でなければいけません');
                }
                if ($email && $this->valid_email($email)) {
                    $data_email[] = $email;
                }
            }
            if ($check_null == 3) {
                $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
                $data_return["msg"] = my_lang('メール情報は必要です。');
                echo json_encode($data_return);
                return FALSE;
            }
            if ($check_null < 3 && isset($error) && count($error)) {
                $data_return["error"] = $error;
                $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
                $data_return["msg"] = my_lang('メール情報は必要です。');
                echo json_encode($data_return);
                return FALSE;
            }
            $data['event'] = $this->model->get($id);
            if (!$data['event']) {
                $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
                $data_return["msg"] = my_lang('イベントは存在しません。');
                echo json_encode($data_return);
                return FALSE;
            }
            $subject = my_lang('【イベント名】イベント申し込みフォーム埋め込みタグ送付');
            $data['event']->link_application = site_url('manager/application_form/add/' . $id);
            $data['event']->script_html = '&lt;iframe style="border: none" width="600px" src="' . $data['event']->link_application . '"&gt;&lt;/iframe&gt;';
            $content = $this->load->view("email/embedded_code", $data, TRUE);

            foreach ($data_email as $email) {
                $content = str_replace(my_lang('[埋め込みタグメールアドレス]'), $email, $content);
                $return = $this->email_lib->send_email($subject, $content, $email);
            }
            $data_return = array_merge((array)$return, $data_return);
            $data_return["msg"] = my_lang('操作が成功しました。');
            $data_return['html'] = $this->load->view("manager/event/success_modal", $data, TRUE);
            echo json_encode($data_return);
            return FALSE;
        } else {
            $data_return["state"] = 0; /* state = 2 : thao tác thất bại */
            $data_return["msg"] = my_lang('操作が失敗しました。');
            echo json_encode($data_return);
            return FALSE;
        }
    }

    protected function _check_event_step($id, $page_draft, $data = []) {
        $check_event = $this->event_lib->check_event_draft($id, $data, $page_draft);
        if (!($check_event['state'])) {
            $data_show_error['id'] = $id;
            $data_show_error['msg'] = $check_event['msg'];
            $data_show_error['page_draft'] = $page_draft;
            $this->_show_error($data_show_error);
            return FALSE;
        }

        return $check_event;
    }

    protected function _check_event_accepting($id, $page_draft, $data = []) {
        $check_event = $this->event_lib->check_event_status_accepting($data);
        if (!($check_event['state'])) {
            $data_show_error['id'] = $id;
            $data_show_error['msg'] = $check_event['msg'];
            $data_show_error['page_draft'] = $page_draft;
            $this->_show_error($data_show_error);
            return FALSE;
        }

        return $check_event;
    }

    protected function _show_error($data) {
        //Get view nav step
        $data['step_nav'] = $this->event_lib->_load_step_nav($data['page_draft'], $data);
        //Get view content
        $data['content'] = "<div style='text-align: center;padding-top: 20px;padding-bottom:50px;color: red'>" . $data['msg'] . "</div>";
        $content = $this->load->view("manager/event/content_event_step", $data, TRUE);
        $this->show_page($content);
    }


    /**
     * Valid Email
     *
     * @param    string
     *
     * @return    bool
     */
    protected function valid_email($str) {
        // if (function_exists('idn_to_ascii') && $atpos = strpos($str, '@')) {
        //     $str = substr($str, 0, ++$atpos) . idn_to_ascii(substr($str, $atpos));
        // }

        // return (bool)filter_var($str, FILTER_VALIDATE_EMAIL);
return preg_match("/^[a-zA-Z0-9\._-]+@([a-zA-Z0-9_-])+([a-zA-Z0-9\._-]+)+$/", $str);
    }

    /**
     * Check time tomorrow event
     */
    public function check_time_tomorrow_event($str) {
        return $this->event_lib->check_time_tomorrow_event($str);
    }

    /**
     * Check time today event
     */
    public function check_time_today_event($str) {
        return $this->event_lib->check_time_today_event($str);
    }

    /**
     * Check start time event
     */
    public function check_start_time_event($str, $field) {
        return $this->event_lib->check_start_time_event($str, $field);
    }

    /**
     * Check start date event
     */
    public function check_start_date_event($str, $field) {
        return $this->event_lib->check_start_date_event($str, $field);
    }

    /**
     * Check end date event
     */
    public function check_end_date_event($str, $field) {
        return $this->event_lib->check_end_date_event($str, $field);
    }

    /**
     * Check checkin time event
     */
    public function check_checkin_time_event($str, $field) {
        return $this->event_lib->check_checkin_time_event($str, $field);
    }

    /**
     * Check subscribe time event
     */
    public function check_subscribe_time_event($str, $field) {
        return $this->event_lib->check_subscribe_time_event($str, $field);
    }

    /**
     * Check payment time event
     */
    public function check_payment_time_event($str, $field) {
        return $this->event_lib->check_payment_time_event($str, $field);
    }

    /**
     * Check name event
     */
    public function check_name_event($str) {
        return $this->event_lib->check_name_event($str);
    }

      // ThanhlV - create 4 event sample when register
    public function create_event_sample_after_register() {
        $data = [];
        $this->config->load('event_sample', TRUE);
        $getData = $this->config->item('event_sample');
        if ($this->session->userdata('option') == 4) {
            $payMent = $getData['manual_no_free']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['manual_no_free'] = $getData['manual_no_free'];
            $data['manual_no_free']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 2) {
            $payMent = $getData['auto_no_free']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['auto_no_free'] = $getData['auto_no_free'];
            $data['auto_no_free']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 3) {
            $data['manual_free'] = $getData['manual_free'];
        }

        if ($this->session->userdata('option') == 1) {
            $data['auto_free'] = $getData['auto_free'];
        }

        if ($this->session->userdata('option') == 5) {
            $payMent = $getData['manual_no_free_5']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['manual_no_free_5'] = $getData['manual_no_free_5'];
            $data['manual_no_free_5']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 6) {
            $payMent = $getData['auto_no_free_6']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['auto_no_free_6'] = $getData['auto_no_free_6'];
            $data['auto_no_free_6']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 7) {
            $payMent = $getData['manual_no_free_7']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['manual_no_free_7'] = $getData['manual_no_free_7'];
            $data['manual_no_free_7']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 8) {
            $payMent = $getData['template_8']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['template_8'] = $getData['template_8'];
            $data['template_8']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 9) {
            $payMent = $getData['template_9']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['template_9'] = $getData['template_9'];
            $data['template_9']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 10) {
            $payMent = $getData['template_10']['data_fee']['transaction_type'];
            if (empty($payMent)) {
                $payMent = $this->session->userdata('type_payment');
            }
            $data['template_10'] = $getData['template_10'];
            $data['template_10']['data_fee']['transaction_type'] = $payMent;
        }

        if ($this->session->userdata('option') == 11) {

            $data['template_11'] = $getData['template_11'];
        }

        if ($this->session->userdata('option') == 12) {
            $data['template_12'] = $getData['template_12'];
        }

        $time_created = date('Y-m-d H:i:s');
        $time_modified = date('Y-m-d H:i:s');
        if (count($data) != 0) {
            foreach ($data as $key => $item) {
                if (isset($item['event'])) {
                    $data_copy = (object) $item['event'];
                    $data = array();
                    $data['name'] = $data_copy->name;
                    $data['target'] = $data_copy->target;
                    $data['start_date'] = date('Y-m-d');
                    $data['start_time'] = date('H:i:00');
                    $data['opening_time'] = date('H:i:00',  strtotime("+1 hour"));
                    $data['end_date'] = date('Y-m-d', strtotime("+1 day"));
                    $data['end_time'] = date('H:i:00');
                    $data['cat_id'] = $data_copy->cat_id;
                    $data['total_held_days'] = $data_copy->total_held_days;
                    $data['address1'] = $data_copy->address1;
                    $data['address2'] = $data_copy->address2;
                    $data['subcrisebers_expected'] = $data_copy->subcrisebers_expected;
                    $data['company_name'] = $data_copy->company_name;
                    $data['sponsorship_name'] = $data_copy->sponsorship_name;
                    $data['url'] = $data_copy->url;
                    $data['phone'] = $data_copy->phone;
                    $data['is_vip'] = $data_copy->is_vip;
                    $data['vip_email_notify'] = $data_copy->vip_email_notify;
                    $data['type_ticket'] = $data_copy->type_ticket;
                    $data['total_held_hours'] = $data_copy->total_held_hours;
                    $data['total_held_minutes_bonus'] = $data_copy->total_held_minutes_bonus;
                    $data['user_id'] = $this->session->userdata('user_id');
                    $data['draft'] = $data_copy->draft;
                    $data['created_time'] = date('Y-m-d H:i:s');
                    $data['status'] = $data_copy->status;
                    $data['e_ticket_mail'] = $data_copy->e_ticket_mail;
                    $data['register_success_mail'] = $data_copy->register_success_mail;
                    $data['announce_win_mail'] = $data_copy->announce_win_mail;
                    $data['announce_lose_mail'] = $data_copy->announce_lose_mail;
                    $data['payment_success_mail'] = $data_copy->payment_success_mail;
                    $data['is_stock'] = $data_copy->is_stock;
                    $data['show_ticket_display'] = $data_copy->show_ticket_display;
                    $data['list_email'] = $data_copy->list_email;
                    $data['settings'] = $data_copy->settings;
                    $data['is_update_link'] = $data_copy->is_update_link;
                    $data['deleted'] = $data_copy->deleted;
                    $data['is_sent_mail'] = $data_copy->is_sent_mail;
                    $data['no_sent_mail'] = $data_copy->no_sent_mail;
                    $data['url_video'] = $data_copy->url_video;
                    $insert_id =  $this->m_event->insert($data, true);
                    $type_ticket = $data_copy->type_ticket;
                }
                // break;
                if (isset($item['data_form_list'])) {
                    $data_form_list = (object) $item['data_form_list'];
                    $form_id = [];
                    foreach ($data_form_list as $form) {
                        $form = (object) $form;
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['form_title'] = $form->form_title;
                        $insert_temp['type_form'] = $form->type_form;
                        $insert_temp['time_created'] = date('Y-m-d H:i:s');
                        $insert_temp['user_created'] = $this->session->userdata('user_id');
                        $form_id[$form->type_form] = $this->m_event_form->insert($insert_temp, TRUE);
                    }
                }
                // break;
                if (isset($item['data_sub_form'])) {
                    $data_sub_form = $item['data_sub_form'];
                    foreach ($data_sub_form as $sub_form) {
                        $sub_form = (object) $sub_form;
                        $new_field_id = $this->event_lib->copy_custom_form_field($sub_form->field_id);

                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['field_id'] = $new_field_id; // old error $item->field_id;
                        $insert_temp['form_id'] = $form_id[$sub_form->type_form];
                        $insert_temp['default_value'] = $sub_form->default_value;
                        $insert_temp['type_form'] = $sub_form->type_form;
                        $insert_temp['max_choice'] = $sub_form->max_choice;
                        $insert_temp['rules'] = $sub_form->rules;
                        $insert_temp['required'] = $sub_form->required;
                        $insert_temp['position'] = $sub_form->position;
                        $insert_temp['created_time'] = $time_created;
                        $insert_temp['show_on_friend_form'] = $sub_form->show_on_friend_form;

                        $this->m_event_form_data->insert($insert_temp, TRUE);
                    }
                }
                // break;
                if (isset($item['data_fee'])) {
                    $data_fee =  (object) $item['data_fee'];
                    $insert_temp = array();
                    $insert_temp['event_id'] = $insert_id;
                    $insert_temp['transaction_type'] = $data_fee->transaction_type;
                    $insert_temp['is_tax'] = $data_fee->is_tax;
                    $insert_temp['tax'] = $data_fee->tax;
                    $insert_temp['note1'] = $data_fee->note1;
                    $insert_temp['note4'] = $data_fee->note4;
                    $insert_temp['note3'] = $data_fee->note3;
                    $insert_temp['note2'] = $data_fee->note2;
                    $insert_temp['is_fee'] = $data_fee->is_fee;

                    $fee_id = $this->m_event_fee->insert($insert_temp, TRUE);


                    if (isset($item['data_fee_banking'])) {
                        $data_fee_banking =  (object) $item['data_fee_banking'];
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['account_name'] = $data_fee_banking->account_name;
                        $insert_temp['bank_name'] = $data_fee_banking->bank_name;
                        $insert_temp['shop_name'] = $data_fee_banking->shop_name;
                        $insert_temp['type'] = $data_fee_banking->type;
                        $insert_temp['account_number'] = $data_fee_banking->account_number;
                        $insert_temp['transfer_deadline'] = $data_fee_banking->transfer_deadline;
                        $insert_temp['transfer_fee'] = $data_fee_banking->transfer_fee;
                        $insert_temp['note'] = $data_fee_banking->note;

                        $this->m_event_fee_banking->insert($insert_temp, TRUE);
                    }

                    if (isset($item['data_fee_cashier'])) {
                        $data_fee_cashier =  (object) $item['data_fee_cashier'];
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['time_begin'] =  date('Y-m-d');
                        $insert_temp['time_end'] = date('Y-m-d', strtotime("+1 day"));
                        $insert_temp['reception_place'] = $data_fee_cashier->reception_place;
                        $insert_temp['note'] = $data_fee_cashier->note;

                        $this->m_event_fee_cashier->insert($insert_temp, TRUE);
                    }

                    if (isset($item['data_fee_combini'])) {
                        $data_fee_combini =  (object) $item['data_fee_combini'];
                        $insert_temp = array();
                        $insert_temp['event_id'] = $insert_id;
                        $insert_temp['fee_id'] = $fee_id;
                        $insert_temp['reception_place'] = $data_fee_combini->reception_place;
                        $insert_temp['reception_phone'] = $data_fee_combini->reception_phone;
                        $insert_temp['user_created'] = $this->session->userdata('user_id');
                        $this->m_event_fee_combini->insert($insert_temp, TRUE);
                    }

                    if (isset($item['data_fee_item'])) {
                        $data_fee_item =  (object) $item['data_fee_item'];
                        $item_temp = array();
                        $item_temp['event_id'] = $insert_id;
                        $item_temp['fee_id'] = $fee_id;
                        $item_temp['user_created'] = $this->session->userdata('user_id');
                        $item_temp['time_created'] = $time_created;
                        $item_temp['time_modified'] = $time_modified;
                        $item_temp['fee_item_name'] = $data_fee_item->fee_item_name;
                        $item_temp['max_choice'] = $data_fee_item->max_choice;
                        $item_temp['description'] = $data_fee_item->description;
                        $item_temp['rules'] = $data_fee_item->rules;
                        $item_temp['ship_type'] = $data_fee_item->ship_type;
                        $item_temp['item_position'] = $data_fee_item->item_position;
                        $item_temp['is_fee_shipping'] = $data_fee_item->is_fee_shipping;
                        $item_temp['is_stock_item'] = $data_fee_item->is_stock_item;
                        $item_temp['item_type'] = $data_fee_item->item_type;
                        $item_temp['fee_item_type'] = $data_fee_item->fee_item_type;
                        $item_insert = $this->m_event_fee_item->insert($item_temp, TRUE);

                        if ($item_insert) {
                            $data_fee_item_data = (object) $item['data_fee_item_data'];
                            foreach ($data_fee_item_data as $item_data) {
                                $item_data = (object) $item_data;
                                $item_data_temp = array();
                                $item_data_temp['fee_item_id'] = $item_insert;
                                $item_data_temp['name_display'] = $item_data->name_display;
                                $item_data_temp['price'] = $item_data->price;
                                $item_data_temp['unit'] = $item_data->unit;
                                $item_data_temp['total_limit'] = $item_data->total_limit;
                                $item_data_temp['position'] = $item_data->position;
                                $item_data_temp['status'] = $item_data->status;
                                $item_data_temp['user_created'] = $this->session->userdata('user_id');
                                $item_data_temp['time_created'] = $time_created;
                                $item_data_temp['time_modified'] = $time_modified;

                                //Set stock info for manual event
                                if ($type_ticket == 2) {
                                    $item_data_temp['total_stock'] =  $item_data->total_stock;;
                                    $item_data_temp['current_stock'] =  $item_data->current_stock;;
                                    $item_data_temp['is_infinitive'] = 1;
                                } else { // Set stock info for auto event
                                    $item_data_temp['total_stock'] = 0;
                                    $item_data_temp['current_stock'] = 0;
                                    $item_data_temp['is_infinitive'] = 1;
                                }
                                $this->m_event_fee_item_data->insert($item_data_temp, TRUE);
                            }
                        }
                    }


                    if (isset($item['data_fee_data'])) {
                        $data_fee_data =  (object) $item['data_fee_data'];
                        foreach ($data_fee_data as $fee_data) {
                            $fee_data = (object) $fee_data;
                            $insert_temp = array();
                            $insert_temp['event_id'] = $insert_id;
                            $insert_temp['fee_id'] = $fee_id;
                            if ($fee_data->fee_item_id == null) {
                                $insert_temp['fee_item_id'] = $item_insert;
                            } else {
                                $insert_temp['fee_item_id'] = $fee_data->fee_item_id;
                            }
                            $insert_temp['default_fee'] = $fee_data->default_fee;
                            $insert_temp['fee_position'] = $fee_data->fee_position;
                            $this->m_event_fee_data->insert($insert_temp, TRUE);
                        }
                    }
                }
            }
        }
    }
}
