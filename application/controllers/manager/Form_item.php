<?php

/**
 * New_event controller
 *
 * @property Application_lib           application_lib
 * @property Event_lib                 event_lib
 * @property S3_upload                 s3_upload
 * @property M_event_form_field        m_event_form_field
 * @property M_event_form_field_data   m_event_form_field_data
 * @property M_event_form_data         m_event_form_data
 * @property M_event_subscriber_detail m_event_subscriber_detail
 */
class Form_item extends Manager_layout {

    function __construct() {
        parent::__construct();
        $this->load->library("Application_lib");
        $this->load->library("Event_lib");
        $this->load->library("S3_upload");
        $this->load->model('m_event_form_field');
        $this->load->model('m_event_form_field_data');
        $this->load->model('m_event_form_data');
    }

    function add_form($id = 0) {
        $data = Array();
        $data['data_field_custom'] = array();
        $data_field_custom = $this->session->userdata("temp_data_field_custom");
        if (!is_array($data_field_custom)) {
            //Get list data custom field form in db of event
            $data_field_custom = $this->m_event_form_data->get_many_by([
                'event_id'  => $id,
                'type_form' => $this->application_lib->_form_subscriber,
            ]);
        }
        foreach ($data_field_custom as $item) {
            if (!empty($item->id) && !empty($item->is_custom_field)) {
                $more_data = json_decode($item->more_data);
                foreach ($more_data as $key => $item_data) {
                    $item->$key = $item_data;
                }
                $data['data_field_custom'][$item->field_name] = $item;
            }
            $data['required'] = (!empty($item->required) && is_array($item->required)) ? array_flip($item->required) : '';
        }
        $data['event_id'] = $id;
        // $check_event = $this->event_lib->check_event_active($id);
        // if (!$check_event['state']) {
        //     $data['is_view'] = TRUE;
        // }
        $data['save_link'] = site_url("manager/form_item/add_form_save");
        $html = $this->load->view("manager/form_item/add_form", $data, TRUE);
        echo json_encode(['state' => 1,
                          'html'  => $html,]);
    }

    function add_form_save() {
        $data = $this->input->post();
        $data_return = [];
        $id = isset($data['event_id']) ? $data['event_id'] : 0;
        // $check_event = $this->event_lib->check_event_active($id);
        // if (!($check_event['state'])) {
        //     $data_return["msg"] = $check_event['msg'];
        //     $data_return["state"] = 0;
        //     echo json_encode($data_return);
        //     return FALSE;
        // }
        $data_return['state'] = 1;
        $data_return['callback'] = "add_form_field_response";
        if (!($data && count($data) && isset($data['custom_field']) && count($data['custom_field']) && isset($data['field_display_name']) && count($data['field_display_name']))) {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["state"] = 0;
            echo json_encode($data_return);
            return FALSE;
        }
        $data['required'] = isset($data['required']) ? array_flip($data['required']) : array();
        $list_custom_field = [];
        for ($i = 1; $i <= 10; $i++) {
            $list_custom_field[] = "custom_field_" . $i;
        }
        $status = TRUE;
        $data_field = [];
        $validate = $this->m_event_form_field->get_validate_from_schema();
        foreach ($data['custom_field'] as $key => $item) {
            if (isset($data['field_display_name'][$key]) && $data['field_display_name'][$key] && in_array($item, $list_custom_field)) {
                $temp = [];
                $more_data = [
                    "field_display_name" => $this->event_lib->format_data_textarea($data['field_display_name'][$key]),
                    "field_group"        => "",
                    "field_note"         => "",
                    "field_unit"         => "",
                    "field_data"         => "",
                    "field_height"       => (isset($data['field_height'][$key])) ? $data['field_height'][$key]:'',
                    "field_width"        => (isset($data['field_width'][$key])) ? $data['field_width'][$key]:'',
                    "field_color"        => (isset($data['field_color'][$key])) ? $data['field_color'][$key]:'',
                ];
                $temp['field_name'] = $item;
                $temp['field_display_name'] = $more_data['field_display_name'];
                $temp['is_custom_field'] = 1;
                $temp['type'] = 'text';
                if (!empty($data['position'][$key])) {
                    $temp['default_position'] = $data['position'][$key];
                } else {
                    $temp['default_position'] = filter_var($item, FILTER_SANITIZE_NUMBER_INT) + 10;
                }
                $temp['more_data'] = json_encode($more_data, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
                if (!empty($data['id'][$key])) {
                    $temp['id'] = $data['id'][$key];
                }
                //Check validate
                $data_validate = $this->m_event_form_field->validate($temp, $validate);
                if (!$data_validate) {
                    $string_error = '';
                    $data_return["state"] = 0;
                    $errors = $this->m_event_form_field->get_validate_error();
                    foreach ($errors as $item_error) {
                        $string_error .= $item_error . '<br/>';
                    }
                    $data_return['error']['position'][$temp['default_position']] = $string_error;
                } else {
                    $data_field[$key] = $data_validate;
                }
            } else {
                if ($data['id'][$key]) {
                    $status = $this->m_event_form_field->update($data['id'][$key], array('deleted' => 1), TRUE);
                    $this->m_event_form_data->update_by(array(
                        'form_id'  => $data['id'][$key],
                        'event_id' => $data['event_id'],
                    ), array('deleted' => 1));
                }
            }
        }
        if (!$data_return["state"]) {
            $data_return["msg"] = my_lang('データが無効です。');
            echo json_encode($data_return);
            return FALSE;
        }
        $temp_data_field_custom = [];
        foreach ($data_field as $key => $item) {
            if (!empty($item['id'])) {
                $this->m_event_form_field->update($item['id'], $item, TRUE);
            } else {
                $item['id'] = $this->m_event_form_field->insert($item, TRUE);
            }
            $item['field_id'] = $item['id'];
            $item['required'] = isset($data['required']) ? array_flip($data['required']) : array();
            $temp_data_field_custom[] = (object)$item;
        }

        if ($status) {
            $this->session->set_userdata("temp_data_field_custom", $temp_data_field_custom);
            $data['list_item_form'] = $this->event_lib->process_data_item_form($temp_data_field_custom);
            $data_return["data"] = $data;
            $data_return["content"] = $this->load->view("manager/form_item/add_form_result", $data, TRUE);;
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
        } else {
            $this->session->unset_userdata("temp_data_field_custom");
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
        }
        echo json_encode($data_return);
    }

    public function add_form_item_checkbox($id = 0) {
        $data = Array();
        $data['data_field_custom'] = array();
        $data['event_id'] = $id;
        $data['save_link'] = site_url("manager/form_item/temp_form_item_checkbox_save/" . $id);
        $data['finish_link'] = site_url("manager/form_item/form_item_checkbox_save/" . $id);
        $data['custom_field'] = $this->event_lib->get_content_custom_field($id, ['soft_delete' => FALSE]);
        $html = $this->load->view("manager/form_item/add_form_item_checkbox", $data, TRUE);
        echo json_encode(['state' => 1, 'html'  => $html,]);
    }

    public function temp_form_item_checkbox_save($id = 0) {
        $data = $this->input->post();
        $this->s3_upload->log_insert("temp_form_item_checkbox_save: " . json_encode($data), "event-form-field");
        $data['type'] = 'checkbox';
        $data['type_form'] = 'subscriber';
        $data['session'] = 'temp_data_field_custom';
        $data_return = $this->event_lib->form_item_save($id, $data, []);

        echo json_encode($data_return);
    }

    public function form_item_checkbox_save($id = 0) {
        $data = $this->input->post();
        $this->s3_upload->log_insert("form_item_checkbox_save: " . json_encode($data), "event-form-field");
        $data['type'] = 'checkbox';
        $data['type_form'] = 'subscriber';
        $data['session'] = 'temp_data_field_custom';
        $data_return = $this->event_lib->form_item_save($id, $data, ['callback' => 'form_field_checkbox_save_response']);

        echo json_encode($data_return);
    }

    public function  delete_item_checkbox($id = 0) {
        $post = $this->input->post();
        $this->s3_upload->log_insert("Data post: " . json_encode($post), "event-form-field");
        $post['type'] = 'checkbox';
        $post['type_form'] = 'subscriber';
        $post['session'] = 'temp_data_field_custom';
        $session = ($post['type'] != 'type') ? ($post['session'] . '_' . $post['type']) : $post['session'];
        $session_forms = $this->session->userdata($session);
        $field_id = $post['field_id'];
        $questions = [];
        $sum_question = 20;
        $list_question = $this->m_event_form_data->get_list_filter([
            'event_id'        => $post['event_id'],
            'type_form'       => 'subscriber',
            'is_custom_field' => 1,
            'i.type'          => 'checkbox',
        ], [], [], 0, 0, ['m.position' => 'ASC']);
        $this->s3_upload->log_insert("list_question: " . json_encode($list_question), "event-form-field");
        for ($un = 0; $un < count($list_question); $un ++) {
            $this->m_event_form_field->update_name($list_question[$un]->field_id, array('field_name' => 'custom_field_checkbox_' . ($un + 1)));
        }
        if(!empty($id)){
            for ($i = 0; $i < count($list_question); $i++) {
                $event_id = $list_question[$i]->event_id;
                if ($list_question[$i]->field_id == $field_id) {
                    $delete_question = true;
                    $i_main = $i;
                    $this->m_event_form_data->update_by(array(
                        'field_id'  => $field_id,
                        'event_id'  => $event_id,
                        'type_form' => 'subscriber',
                        'form_id'   => $id,
                    ), array('deleted' => 1));
                    $this->m_event_form_field->update($field_id, ['deleted' => 1], TRUE);
                    $schema = $this->m_event_form_field_data->schema;
                    $this->m_event_form_field_data->schema = [
                        'deleted' => $schema['deleted'],
                    ];
                    $this->m_event_form_field_data->update_by(['field_id' => $field_id], ['deleted' => 1]);
                    $this->s3_upload->log_insert("question_delete: $field_id(field_id), $event_id(event_id), $id(id)", "event-form-field");
                }
                if (isset($i_main) && isset($list_question[$i + 1])) {
                    for ($j = $i_main; $j <= count($list_question); $j ++) {
                        $field_id_next = $list_question[$j + 1]->field_id;
                        $position = $list_question[$j + 1]->position;
                        $this->m_event_form_data->update_by(array(
                            'field_id'  => $field_id_next,
                            'event_id'  => $event_id,
                            'type_form' => 'subscriber',
                            'form_id'   => $id,
                        ), array('position' => (int) $position - 1));
                        $this->m_event_form_field->update_name($list_question[$j + 1]->field_id, array('field_name' => 'custom_field_checkbox_' . ($j + 1)));
                        $this->s3_upload->log_insert("m_event_form_data_update: $field_id_next(field_id), $event_id(event_id), $id(id)", "event-form-field");
                    }
                    break;
                }
            }
            if(isset($session_forms)){
                unset($session_forms[$post['event_id']]['custom_field_checkbox_' . $post['field_value']]);
                if($delete_question) {
                    $questions_old = count($list_question) - 1;
                    for ($i = 1; $i <= $sum_question; $i++){
                        if($session_forms[$post['event_id']]['custom_field_checkbox_' . $i]){
                            $session_forms[$post['event_id']]['custom_field_checkbox_' . $i]->field_name = 'custom_field_checkbox_' . ($questions_old + count($questions) + 1);
                            $questions['custom_field_checkbox_' . ($questions_old + count($questions) + 1)] = $session_forms[$post['event_id']]['custom_field_checkbox_' . $i];
                        }
                    }
                } else {
                    $questions_old = count($list_question);
                    for ($qn = 1; $qn <= $sum_question; $qn++){
                        if($session_forms[$post['event_id']]['custom_field_checkbox_' . ($qn + 1)]){
                            $session_forms[$post['event_id']]['custom_field_checkbox_' . ($qn + 1)]->field_name = 'custom_field_checkbox_' . ($questions_old + count($questions) + 1);
                            $questions['custom_field_checkbox_' . ($questions_old + count($questions) + 1)] = $session_forms[$post['event_id']]['custom_field_checkbox_' . ($qn + 1)];
                        }
                    }
                }
                $check_question_new = true;
            }
        }
        $event_id = $post['event_id'];
        if(!isset($delete_question)){
            $schema = $this->m_event_form_field_data->schema;
            $this->m_event_form_field_data->schema = [
                'deleted' => $schema['deleted'],
            ];
            $this->m_event_form_field_data->update_by(['field_id' => $field_id], ['deleted' => 1]);
            $schema = $this->m_event_form_field->schema;
            $this->m_event_form_field->schema = [
                'deleted' => $schema['deleted'],
            ];
            $this->m_event_form_field->update($field_id, ['deleted' => 1]);
            unset($session_forms[$event_id]['custom_field_checkbox_' . $post['field_value']]);
            $questions_old = count($list_question) != 0 ? count($list_question) : 0;
            if(count($questions) == 0){
                for ($i = 1; $i <= $sum_question; $i++){
                    if($session_forms[$post['event_id']]['custom_field_checkbox_' . $i]){
                        $session_forms[$post['event_id']]['custom_field_checkbox_' . $i]->field_name = 'custom_field_checkbox_' . ($questions_old + count($questions) + 1);
                        $questions['custom_field_checkbox_' . ($questions_old + count($questions) + 1)] = $session_forms[$post['event_id']]['custom_field_checkbox_' . $i];
                    }
                }
            }
        }

        if(!isset($check_question_new) && isset($delete_question)) {
            unset($session_forms[$event_id]['custom_field_checkbox_' . $post['field_value']]);
            if(count($list_question) != 0) {
                for ($i = 1; $i <= $sum_question; $i++){
                    if($session_forms[$event_id]['custom_field_checkbox_' . $i]){
                        $session_forms[$event_id]['custom_field_checkbox_' . $i]->field_name = 'custom_field_checkbox_' . (count($list_question) + count($questions) + 1);
                        $questions['custom_field_checkbox_' . (count($list_question) + count($questions) + 1)] = $session_forms[$event_id]['custom_field_checkbox_' . $i];
                    }
                }
            } else {
                for ($i = 1; $i <= count($session_forms[$event_id]) + 1; $i++){
                    if($session_forms[$event_id]['custom_field_checkbox_' . $i]){
                        $session_forms[$event_id]['custom_field_checkbox_' . $i]->field_name = 'custom_field_checkbox_' . (count($questions) + 1);
                        $questions['custom_field_checkbox_' . (count($questions) + 1)] = $session_forms[$event_id]['custom_field_checkbox_' . $i];
                    }
                }
            }
        }
        $session_forms[$event_id] = $questions;
        $this->session->set_userdata($session, $session_forms);
        $this->s3_upload->log_insert("set_userdata: " . json_encode($session_forms), "event-form-field");

        $default_data = [];
        $data['soft_delete'] = false;
        $default_data['type'] = 'checkbox';
        $default_data['session'] = 'temp_data_field_custom';
        $default_data['type_form'] = 'subscriber';
        $default_data['field_value'] = 1;
        $default_data['field_return_value'] = 0;
        $default_data['field_return_field_id'] = 0;
        $default_data['key_field_name'] = 'custom_field_checkbox';

        $data = array_merge($default_data, $data);
        // check trường hợp là form survey
        if ($data['type_form'] == 'survey' || $data['type_form'] == 'subscriber') {
            $data['field_value'] = ($data['field_value'] > 20) ? 20 : $data['field_value'];
        } else {
            $data['field_value'] = ($data['field_value'] > 10) ? 10 : $data['field_value'];
        }
        $_SESSION['delete_question'] = 'delete_question';
        $list_field = $this->event_lib->get_list_custom_field($post['event_id'], $data);
        $this->s3_upload->log_insert("list_field: " . json_encode($list_field), "event-form-field");
        $list_check = [];
        for ($lf = 1; $lf <= count($list_field) + 1; $lf++) {
            if($list_field['custom_field_checkbox_' . $lf] && empty($list_field['custom_field_checkbox_' . $lf]->id)){
                for ($i = 1; $i <= count($list_field) + 1; $i++){
                    if($list_field['custom_field_checkbox_' . $i]){
                        $list_check[] = $list_field['custom_field_checkbox_' . $i]->id;
                        $this->m_event_form_field->update_name($list_field['custom_field_checkbox_' . $i]->field_id, array('field_name' => 'custom_field_checkbox_' . count($list_check)));
                        $this->s3_upload->log_insert("update_name: " . $list_field['custom_field_checkbox_' . $i]->field_id . ' - custom_field_checkbox_' . count($list_check), "event-form-field");
                    }
                }
                break;
            }
        }
        $_SESSION['delete_question'] = 'delete_question';
        self::form_custom_field($post['event_id']);
    }

    protected function log_insert($result, $name_file = 'event-form-field') {
        $folderLog = APPPATH . 'logs';
        if (!is_dir($folderLog)) {
            $old = umask(0);
            mkdir($folderLog, 0755);
            umask($old);
        }
        $myFile = APPPATH . "logs/log-" . $name_file . "-" . date('Y-m-d') . ".txt";
        $fh = fopen($myFile, 'a');
        fwrite($fh, date('Y-m-d H:i:s') . "," . $result . PHP_EOL);
        fclose($fh);
    }

    public function form_custom_field($id) {
        $data = $this->input->post();
        //Check validate
        $data['soft_delete'] = FALSE;
        echo json_encode([
            'state'       => 1,
            'callback'    => "change_custom_field",
            'field_value' => isset($data['field_value']) ? $data['field_value'] : 1,
            'html'        => $this->event_lib->get_content_custom_field($id, $data),
        ]);

    }

    public function edit_item($id = 0, $field_id = 0) {
        $data = Array();
        $data['form_item'] = $this->m_event_form_data->get_by([
            'event_id'  => $id,
            'field_id'  => $field_id,
            'type_form' => $this->application_lib->_form_subscriber,
        ]);
        $list_default_value = $this->session->userdata("temp_default_value");
        $value = isset($list_default_value[$id]) ? $list_default_value[$id] : '';
        if (isset($data['form_item']->default_value)) {
            $data['form_item']->default_value = str_replace("<br/>", '&#10;', $data['form_item']->default_value);
            $data['form_item']->default_value = trim(preg_replace('/\s+/', '', $data['form_item']->default_value));
        }
        $data['default_value'] = (isset($data['form_item']->default_value)) ? $data['form_item']->default_value : ($value ? $value : 'ここにテキストをご入力ください。');

        $data['save_link'] = site_url("manager/form_item/edit_item_save/" . $id . '/' . $field_id);
        $html = $this->load->view("manager/form_item/item", $data, TRUE);
        echo json_encode(['state' => 1,
                          'html'  => $html,]);
    }

    public function edit_item_save($id = 0, $field_id = 0) {
        $data = $this->input->post();
        $data_return = Array();
        $data_return['callback'] = "edit_form_field_response";
        $list_default_value = $this->session->userdata("temp_default_value");
        $list_default_value[$id] = isset($data['default_value']) ? $data['default_value'] : '';
        $this->session->set_userdata("temp_default_value", $list_default_value);
        $data_return["data"] = $data;
        $data_return["state"] = 1;
        $data_return["msg"] = my_lang('操作が成功しました。');

        echo json_encode($data_return);
    }

    public function add_form_item_password($id = 0, $field_id = 0) {
        $data = Array();

        if ($field_id != 0) {
            $data['list_passwords'] = $this->m_event_form_data->get_by([
                'field_id'  => $field_id,
                'event_id'  => $id,
                'type_form' => $this->application_lib->_form_subscriber,
            ]);
        }
        $default_value = '';
        if (isset($data['list_passwords']->default_value)) {
            $default_value = str_replace("<br>", '&#10;', $data['list_passwords']->default_value);
            $default_value = trim(preg_replace('/\s+/', '', $default_value));
        }
        $value = $this->session->userdata("temp_password_default");
        if (isset($value[$id])) {
            $default_value = $value[$id];
        }
        $data['default_value'] = $default_value;
        $data['save_link'] = site_url("manager/form_item/form_item_password_save/" . $id . '/' . $field_id);
        // $check_event = $this->event_lib->check_event_active($id);
        // if (!$check_event['state']) {
        //     $data['is_view'] = TRUE;
        // }
        $html = $this->load->view("manager/form_item/add_form_item_password", $data, TRUE);
        echo json_encode(['state'         => 1,
                          'default_value' => $value,
                          'html'          => $html,]);
    }

    public function form_item_password_save($id, $field_id) {
        $data = $this->input->post();
        $data['event_id'] = $id;
        // $check_event = $this->event_lib->check_event_active($id);
        // if (!$check_event['state']) {
        //     echo json_encode($check_event);
        // }
        $data_return = Array();
        $data_return['callback'] = "form_field_password_save_response";
        if (($data['default_value'] == '')) {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["state"] = 0;
            echo json_encode($data_return);
            return FALSE;
        }
        $list_password = $this->event_lib->format_data_textarea($data['default_value']);
        $password_data = explode('<br/>', $list_password);
        $validate = $this->check_event_password($password_data);
        if (!$validate) {
            return FALSE;
        }
        $data_insert = array();
        $more_data = array(
            "field_display_name" => 'パスワード',
            "field_group"        => "",
            "field_note"         => "",
            "field_unit"         => "",
            "field_data"         => "",
        );
        $data_insert['more_data'] = json_encode($more_data);
        $data_insert['field_name'] = "custom_field_password";
        $data_insert['field_display_name'] = $more_data['field_display_name'];
        $data_insert['type'] = "password";
        $data_insert['default_position'] = 42;
        $data_insert['is_custom_field'] = 1;
        if ($field_id) {
            $data_insert['id'] = $field_id;
            $this->m_event_form_field->update($field_id, $data_insert);
        } else {
            $data_insert['id'] = $field_id = $this->m_event_form_field->insert($data_insert);
        }
        $data_insert['field_id'] = $data_insert['id'];
        if ($data_insert['id']) {
            $data_insert['default_value'] = $list_password;
            $password_data = explode('<br/>', $data_insert['default_value']);
            //Insert, update form item data
            $form_item_data_insert = [];
            foreach ($password_data as $item) {
                $temp = [
                    'field_id' => $data_insert['id'],
                    'value'    => $item,
                    'data'     => $item,
                    'typing'   => 'text',
                    'position' => 0,
                ];
                $form_item_data_insert[] = $temp;
            }
            $schema = $this->m_event_form_field_data->schema;
            $this->m_event_form_field_data->schema = [
                'deleted' => $schema['deleted'],
            ];
            $this->m_event_form_field_data->update_by(['field_id' => $data_insert['id']], ['deleted' => 1]);
            if (count($form_item_data_insert)) {
                $this->m_event_form_field_data->schema = $schema;
                $state = $this->m_event_form_field_data->insert_many($form_item_data_insert);
            }

            if (!(is_array($state) && count($state))) {
                $data_return["data"] = $data;
                $data_return["msg"] = my_lang('操作が失敗しました。');
                $data_return["state"] = 0;
                $data_return["error"] = $this->m_event_form_field_data->get_validate_error();
                echo json_encode($data_return);

                return FALSE;
            }
            $temp_data_session = $this->session->userdata("temp_password_default");
            $temp_data_session[$id] = $data['default_value'];
            $this->session->set_userdata("temp_password_default", $temp_data_session);
            $list_field[] = (object)$data_insert;
            $data['list_item_form'] = $this->event_lib->process_data_item_form($list_field);
            $data_return["data"] = $data;
            $data_return["content"] = $this->load->view("manager/form_item/add_form_result", $data, TRUE);;
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
        } else {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
        }
        echo json_encode($data_return);
    }

    protected function check_event_password($arr) {
        if (!$arr) {
            return FALSE;
        }
        $value_max_number = 500;
        $data_return['callback'] = "form_field_password_save_response";
        if (count($arr) > $value_max_number) {
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["error_msg"] = my_lang('パスワード数１個から最大500個となります。');
            $data_return["state"] = 0;
            echo json_encode($data_return);
            return FALSE;
        }
        $form_error = [];
        foreach ($arr as $item) {
            $temp_result = $this->validate_event_password($item);
            if (!$temp_result) {
                $form_error[] = $item;
            }
        }
        if (is_array($form_error) && count($form_error)) {
            $data_return["error_data"] = $form_error;
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["error_msg"] = my_lang('4文字から20文字以内で半角英数字大小文字区別有りの文字がご利用いただけます。');
            $data_return["state"] = 0;
            echo json_encode($data_return);
            return FALSE;
        }
        return TRUE;
    }

    protected function validate_event_password($str) {
        $value_max = 20;
        $value_min = 4;
        if (!trim($str)) {
            return FALSE;
        }
        return (($value_max >= mb_strlen($str)) && (mb_strlen($str) >= $value_min));
    }

    // AnhLD
    public function add_text_view($id = 0) {
        $data = Array();
        $data['data_field_custom'] = array();
        $data_field_custom = $this->session->userdata("temp_data_text_view_custom_".$id);
        // $data_field_custom = NULL;

        if (!is_array($data_field_custom)) {
            //Get list data custom field form in db of event
            $this->m_event_form_data->include_text_view_field(TRUE);
            $data_field_custom = $this->m_event_form_data->get_many_by([
                'event_id'  => $id,
                'type_form' => $this->application_lib->_form_subscriber,
            ]);
        }
        foreach ($data_field_custom as $item) {
            if (!empty($item->id) && !empty($item->is_custom_field)) {
                $more_data = json_decode($item->more_data);
                foreach ($more_data as $key => $item_data) {
                    $item->$key = $item_data;
                }
                $data['data_field_custom'][$item->field_name] = $item;
            }
            $data['required'] = (!empty($item->required) && is_array($item->required)) ? array_flip($item->required) : '';
        }
        $data['event_id'] = $id;
        // $check_event = $this->event_lib->check_event_active($id);
        // if (!$check_event['state']) {
        //     $data['is_view'] = TRUE;
        // }
        $data['save_link'] = site_url("manager/form_item/add_text_view_save");
        $html = $this->load->view("manager/form_item/add_text_view", $data, TRUE);
        echo json_encode(['state' => 1,
                          'html'  => $html,]);
    }

    // AnhLD
    function add_text_view_save() {
        $data = $this->input->post();
        $data_return = [];
        $id = isset($data['event_id']) ? $data['event_id'] : 0;

        $data_return['state'] = 1;
        $data_return['callback'] = "add_form_field_text_view_response";
        if (!($data && count($data) && isset($data['custom_field']) && count($data['custom_field']) && isset($data['field_display_name']) && count($data['field_display_name']))) {
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('データが無効です。');
            $data_return["state"] = 0;
            echo json_encode($data_return);
            return FALSE;
        }
        $data['required'] = isset($data['required']) ? array_flip($data['required']) : array();
        $list_custom_field = [];
        for ($i = 1; $i <= 10; $i++) {
            $list_custom_field[] = "custom_field_text_view_" . $i;
        }
        $status = TRUE;
        $data_field = [];
        $validate = $this->m_event_form_field->get_validate_from_schema();
        foreach ($data['custom_field'] as $key => $item) {
            if (isset($data['field_display_name'][$key]) && $data['field_display_name'][$key] && in_array($item, $list_custom_field)) {
                $temp = [];
                $more_data = [
                    "field_display_name" => $this->event_lib->format_data_textarea($data['field_display_name'][$key]),
                    "field_group"        => "",
                    "field_note"         => "",
                    "field_unit"         => "",
                    "field_data"         => "",
                    "field_height"       => (isset($data['field_height'][$key])) ? $data['field_height'][$key]:'',
                    "field_width"        => (isset($data['field_width'][$key])) ? $data['field_width'][$key]:'',
                    "field_color"        => (isset($data['field_color'][$key])) ? $data['field_color'][$key]:'',
                ];
                $temp['field_name'] = $item;
                $temp['field_display_name'] = "text_view"; // AnhLD fixed
                $temp['is_custom_field'] = 1;
                $temp['type'] = 'text_view';
                if (!empty($data['position'][$key])) {
                    $temp['default_position'] = $data['position'][$key];
                } else {
                    $temp['default_position'] = filter_var($item, FILTER_SANITIZE_NUMBER_INT) + 10;
                }
                $temp['more_data'] = json_encode($more_data, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
                if (!empty($data['id'][$key])) {
                    $temp['id'] = $data['id'][$key];
                }
                //Check validate
                $data_validate = $this->m_event_form_field->validate($temp, $validate);
                if (!$data_validate) {
                    $string_error = '';
                    $data_return["state"] = 0;
                    $errors = $this->m_event_form_field->get_validate_error();
                    foreach ($errors as $item_error) {
                        $string_error .= $item_error . '<br/>';
                    }
                    $data_return['error']['position'][$temp['default_position']] = $string_error;
                } else {
                    $data_field[$key] = $data_validate;
                }
            } else {
                if ($data['id'][$key]) {
                    $status = $this->m_event_form_field->update($data['id'][$key], array('deleted' => 1), TRUE);
                    $this->m_event_form_data->update_by(array(
                        'form_id'  => $data['id'][$key],
                        'event_id' => $data['event_id'],
                    ), array('deleted' => 1));
                }
            }
        }
        if (!$data_return["state"]) {
            $data_return["msg"] = my_lang('データが無効です。');
            echo json_encode($data_return);
            return FALSE;
        }
        $temp_data_field_custom = [];
        foreach ($data_field as $key => $item) {
            if (!empty($item['id'])) {
                $this->m_event_form_field->update($item['id'], $item, TRUE);
            } else {
                $item['id'] = $this->m_event_form_field->insert($item, TRUE);
            }
            $item['field_id'] = $item['id'];
            $item['required'] = isset($data['required']) ? array_flip($data['required']) : array();
            $temp_data_field_custom[] = (object)$item;
        }

        if ($status) {
            $this->session->set_userdata("temp_data_text_view_custom_".$id, $temp_data_field_custom);
            $data['list_item_form'] = $this->event_lib->process_data_item_form($temp_data_field_custom);
            $data_return["data"] = $data;
            $data_return["content"] = $this->load->view("manager/form_item/add_form_result", $data, TRUE);;
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
        } else {
            $this->session->unset_userdata("temp_data_text_view_custom_".$id);
            $data_return["data"] = $data;
            $data_return["msg"] = my_lang('操作が失敗しました。');
            $data_return["state"] = 0;
        }
        echo json_encode($data_return);
    }
}