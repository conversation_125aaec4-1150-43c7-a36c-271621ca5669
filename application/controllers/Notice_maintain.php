<?php
/**
 * Created by IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 06/09/2017
 * Time: 8:52 SA
 */
class Notice_maintain extends Manager_layout {

    function __construct() {
        parent::__construct();
    }

    public function index() {
        $data = Array();
        $this->set_data_part("title", "Notice maintain", FALSE);
        $content = $this->load->view("front/notice_maintain/notice_maintain", $data, TRUE);
        $this->session->unset_userdata('notice_url', '');
        $this->show_page($content);
    }

}