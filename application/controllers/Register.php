<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 24/06/2016
 * Time: 2:05 CH
 *
 * @property M_user          m_user
 * @property M_role          m_role
 * @property Application_lib application_lib
 */
class Register extends Front_layout {

    function __construct() {
        parent::__construct();
        $this->load->model("m_user");
        $this->load->model("m_role");
        $this->load->model("m_files");
        $this->load->library("Application_lib");
        $this->ion_auth->remove_user_active_expiration();
    }

    public function temp_form_get() {
        $data = Array();
        $this->session->unset_userdata('option_1');
        $this->session->unset_userdata('option_2');
        $this->session->unset_userdata('button_four');
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $html = $this->load->view("front/register_temp/modal", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function temp_form_get_1() {
        $data = Array();
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $data['event'] = 'no_free';
        isset($data['event']) ? $validated = true : $validated = false;
        $data_return = Array();
        $data_return['callback'] = "event_register_check";
        if ($validated) {
            $this->session->set_userdata("event", $data);
            $data_return["data"] = $data;
            $data_return["state"] = 1;
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $error['event'] = my_lang('選択肢を選んでください。');
            $data_return["error"] = $error;
        }
        $html = $this->load->view("front/register_temp/modal_1", $data, TRUE);
        echo json_encode([
            'state' =>  $data_return["state"],
            'html'  => $html,
            $data_return
        ]);
    }

    public function temp_form_get_2() {
        $data = Array();
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $html = $this->load->view("front/register_temp/modal_2", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function temp_form_get_3() {
        $data = Array();
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $html = $this->load->view("front/register_temp/modal_3", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function temp_form_get_4() {
        $data = Array();
        $this->session->unset_userdata('option_1');
        $this->session->unset_userdata('option_2');
        $this->session->unset_userdata('button_four');
        if(empty($this->session->userdata('option_1'))) {
            $this->session->set_userdata('option_1', 'event_sam_5');
        }
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $data['event'] = 'no_free';
        isset($data['event']) ? $validated = true : $validated = false;
        $data_return = Array();
        $data_return['callback'] = "event_register_check";
        if ($validated) {
            $this->session->set_userdata("event", $data);
            $data_return["data"] = $data;
            $data_return["state"] = 1;
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $error['event'] = my_lang('選択肢を選んでください。');
            $data_return["error"] = $error;
        }
        $html = $this->load->view("front/register_temp/modal_1", $data, TRUE);
        echo json_encode([
            'state' =>  $data_return["state"],
            'html'  => $html,
            $data_return
        ]);
    }
    
    public function temp_form_get_5() {
        $data = Array();
        $this->session->unset_userdata('option_1');
        $this->session->unset_userdata('option_2');
        $this->session->unset_userdata('button_four');
        if(empty($this->session->userdata('option_1'))) {
            $this->session->set_userdata('option_1', 'button_three');
        }
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $html = $this->load->view("front/register_temp/modal_1", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    public function temp_form_get_6() {
        $data = Array();
        $this->session->unset_userdata('option_1');
        $this->session->unset_userdata('option_2');
        $this->session->unset_userdata('button_four');
        $this->session->set_userdata('button_four', 'button_four');
        $data['step1'] = $this->_load_temp_step(1);
        $data['step2'] = $this->_load_temp_step(2);
        $data['step3'] = $this->_load_temp_step(3);
        $data['event_step1'] = $this->_load_event_step(1);
        $data['event_step2'] = $this->_load_event_step(2);
        $data['event_step3'] = $this->_load_event_step(3);
        $data['event_step4'] = $this->_load_event_step(4);
        $html = $this->load->view("front/register_temp/modal", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    private function _load_temp_step($step) {
        $data = [
            'step' => $step,
        ];
        $promoter_site_url = $this->config->item('promoter_site_url');
        if($step == 4){
            
            $data['promoter_site_url'] = $promoter_site_url;
        };
        $data['header_step'] = $this->load->view("front/register_temp/step_block", $data, TRUE);
        return $this->load->view("front/register_temp/step$step", $data, TRUE);
    }

    public function temp_form_check() {
        $data = $this->input->post();
        $validated = $this->m_user->register_temp_validate($data);
        $data_return = Array();
        $data_return['callback'] = "temple_register_check";
        if ($validated) {
            unset($data['email_conf']);
            $this->session->set_userdata("temp_user", $data);
            $data_return["data"] = $data;
            $data_return["state"] = 1;
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $error = $this->m_user->get_validate_error();
            if (isset($error['first_name'])) {
                $error['last_name'] = $error['first_name'];
            }
            $data_return["error"] = $error;
        }
        echo json_encode($data_return);
    }

    public function temp_form_save() {
        $data_return = Array();
        $option_1 = $this->session->userdata("option_1");
        $option_2 = $this->session->userdata("option_2");
        $button_four = $this->session->userdata("button_four");
        $data_return['callback'] = "temp_register_save";
        $data = $this->session->userdata("temp_user");
        if($option_1 == "true" && $option_2 == "no_free" && $button_four == null) {
            $data['option_event'] = 4;
        }

        if($option_1 == "false" && $option_2 == "no_free" && $button_four == null) {
            $data['option_event'] = 2;
        }

        if($option_1 == "true" && $option_2 == "free" && $button_four == null) {
            $data['option_event'] = 3;
        }

        if($option_1 == "false" && $option_2 == "free" && $button_four == null)  {
            $data['option_event'] = 1;
        }

        if($option_1 == 'event_sam_5' && $option_2 == null ) {
            $data['option_event'] = 5;
        }

        if($option_1 == 'event_sam_6') {
            $data['option_event'] = 6;
        }

        if($option_1 == 'button_three' && $option_2 == null) {
            $data['option_event'] = 7;
        }

        if($option_1 == 'event_sam_8') {
            $data['option_event'] = 8;
        }

        if ($option_1 == "true" && $option_2 == "no_free" && $button_four == "button_four") {
            $data['option_event'] = 9;
        }

        if ($option_1 == "false" && $option_2 == "no_free" && $button_four == 'button_four') {
            $data['option_event'] = 10;
        }

        if ($option_1 == "true" && $option_2 == "free" && $button_four == 'button_four') {
            $data['option_event'] = 11;
        }

        if ($option_1 == "false" && $option_2 == "free" && $button_four == 'button_four') {
            $data['option_event'] = 12;
        }

        if (empty($this->session->userdata['payment'])) {
            $data['payment_type'] = 1;
        } else {
            $data['payment_type'] = $this->session->userdata['payment'];
        }

        if (!$data) {
            $data_return["state"] = 0; /* state = 0 : invalid data */
            $data_return["msg"] = my_lang('セッションエラー！ 後でもう一度試してみてください。');
        } else {
            $data['role_id'] = $this->m_role->get_role_id("admin");
            $data['password'] = uniqid();
            $result = $this->m_user->insert($data, TRUE);
            if ($result) {
                $this->session->unset_userdata("temp_user");
                $data_return["state"] = 1; /* state = 0 : invalid data */
                $data_return["msg"] = "Ok!";
            } else {
                $data_return["state"] = 0; /* state = 0 : invalid data */
                $data_return["msg"] = $this->ion_auth->errors();
            }
        }
        $this->session->unset_userdata('option_1');
        $this->session->unset_userdata('option_2');
        $this->session->unset_userdata('button_four');

        echo json_encode($data_return);
    }

    public function form($user_id, $active_code) {
        $data = Array();
        $user = $this->m_user->get_by([
            'id'              => $user_id,
            'activation_code' => $active_code,
        ]);
        $data['step2'] = $this->_load_step(2);
        $data['step3'] = $this->_load_step(3);
        if (!$user || $user->active == 1) {
            $data_error = ['step' => 0];
            $data_error['header_step'] = $this->load->view("front/register/step_block", $data_error, TRUE);
            $data['step1'] = $this->load->view("front/register/token_error", $data_error, TRUE);
        } else {
            $this->session->set_userdata("temp_user", $user);
            $data['user'] = $user;
            $data['step1'] = $this->_load_step(1, $data);
        }
        $html = $this->load->view("front/register/modal", $data, TRUE);
        echo json_encode([
            'state' => 1,
            'html'  => $html,
        ]);
    }

    private function _load_step($step, $data = Array()) {
        $data['step'] = $step;
        $data['header_step'] = $this->load->view("front/register/step_block", $data, TRUE);
        return $this->load->view("front/register/step$step", $data, TRUE);
    }
    
    public function check() {
        $user = $this->session->userdata("temp_user");
        $data = $this->input->post();
        $allowtypes = array('image/jpg', 'image/png', 'image/jpeg', 'image/gif');
        $data_error = [];

        if(empty($data['company_registration'])){
            $data_return['error']['company_registration'] = my_lang('法人登記有りと法人登記無しのいずれかのチェックが必須です。');
        }elseif($data['company_registration'] == 1){
            if($_FILES['photo']){
                $data_error['photo'] = my_lang('法人登記有りを選択した場合、証明証を添付できません。');
            }
            if($_FILES['back_photo']){
                $data_error['back_photo'] = my_lang('法人登記有りを選択した場合、証明証を添付できません。');
            }
            if($_FILES['front_photo']){
                $data_error['front_photo'] = my_lang('法人登記有りを選択した場合、証明証を添付できません。');
            }
            unset($this->m_user->schema["photo"]);
            unset($this->m_user->schema["back_photo"]);
            unset($this->m_user->schema["front_photo"]);
        }else{
            unset($this->m_user->schema["company_registration"]);
            if($_FILES['photo']){
                if($_FILES['photo']['size'] > 3072000){
                    $data_error['photo'] = my_lang('ファイルサイズが3MBを超えています');
                }elseif($_FILES['photo']['error'] == 1 | !in_array($_FILES['photo']['type'],$allowtypes )){
                    $data_error['photo'] = my_lang('ファイルサイズが3MBを超えています');
                }
                unset($this->m_user->schema["back_photo"]);
                unset($this->m_user->schema["front_photo"]);
                if($_FILES['front_photo']){
                    $data_error['front_photo'] = '①'. my_lang('を選択した場合、②の写真は選択できません。');
                }
                if($_FILES['back_photo']){
                    $data_error['back_photo'] = '①'. my_lang('を選択した場合、②の写真は選択できません。');
                }
            } elseif(!$_FILES['photo']){
                if($_FILES['front_photo']){
                    if($_FILES['front_photo']['size'] > 3072000){
                        $data_error['front_photo'] = my_lang('ファイルサイズが3MBを超えています');
                    }elseif($_FILES['front_photo']['error'] == 1 | !in_array($_FILES['front_photo']['type'],$allowtypes )){
                        $data_error['front_photo'] = my_lang('ファイルサイズが3MBを超えています');
                    }
                    unset($this->m_user->schema["photo"]);
                }
                if($_FILES['back_photo']){
                    if($_FILES['back_photo']['size'] > 3072000){
                        $data_error['back_photo'] = my_lang('ファイルサイズが3MBを超えています');
                    }elseif($_FILES['back_photo']['error'] == 1 | !in_array($_FILES['back_photo']['type'],$allowtypes )){
                        $data_error['back_photo'] = my_lang('ファイルサイズが3MBを超えています');
                    }
                    unset($this->m_user->schema["photo"]);
                }
            }
            if($data['legal_entity']){
                $data_error['legal_entity'] = my_lang('法人登記無しを選択した場合、法人番号を入力できません。');
            }
            unset($this->m_user->schema["legal_entity"]);
        }
        if($data['department']){
            $regex = preg_match('/^[一-龥ぁ-んァ-ンa-zA-Z0-9\s]+$/', $data['department']);
            if (!$regex) {
                $data_error['department'] = my_lang('所属部署欄は正しい形式ではありません。');
            }
        }
        if($data['address4']){
            $regex = preg_match('/^[一-龥ぁ-んァ-ンa-zA-Z0-9\s]+$/', $data['address4']);
            if (!$regex) {
                $data_error['address4'] = my_lang('ビル名 部屋番号欄は正しい形式ではありません。');
            }
        }
        $data['id'] = $user->id;
        $data['email'] = $user->email;
        $data['email_conf'] = $user->email;
        unset($this->m_user->schema["email"]);
        unset($this->m_user->schema["email_conf"]);
        $data['role_id'] = $user->role_id;
        $data['is_privacy'] = "1";
        $data['is_term_of_service'] = "1";
        $validated = $this->m_user->validate($data);
        $data_return = Array();
        $data_return['callback'] = "real_register_check";
        if ($validated) {
            unset($data['email_conf']);
            unset($data['password_conf']);
            if($_FILES['photo']){
                $file['photo'] = $_FILES['photo'];
            }
            if($_FILES['back_photo']){
                $file['back_photo'] = $_FILES['back_photo'];
            }
            if($_FILES['front_photo']){
                $file['front_photo'] = $_FILES['front_photo'];
            }
            $validated = $this->m_user->file_upload_before_add_save($file);
            if($validated['photo_image']){
                $data['photo'] = $validated['photo_image'];
            }
            if($validated['front_photo_image']){
                $data['front_photo'] = $validated['front_photo_image'];
            }
            if($validated['back_photo_image']){
                $data['back_photo'] = $validated['back_photo_image'];
            }
            $this->session->set_userdata("user_full_info", $data);
            $data_return["data"] = $data;

            if($data_error){
                if($data_error['photo']){
                    $error['photo'] = $data_error['photo'];
                }
                if($data_error['front_photo']){
                    $error['front_photo'] = $data_error['front_photo'];
                }
                if($data_error['back_photo']){
                    $error['back_photo'] = $data_error['back_photo'];
                }
                if($data_error['legal_entity']){
                    $error['legal_entity'] = $data_error['legal_entity'];
                }
                if($data_error['department']){
                    $error['department'] = $data_error['department'];
                }
                if($data_error['address4']){
                    $error['address4'] = $data_error['address4'];
                }
                $data_return["error"] = $error;
                $data_return["state"] = 0;
            }else{
                $data_return["state"] = 1; /* state = 1 : insert success */
            }
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0; /* state = 0 : invalid data */
            $error = $this->m_user->get_validate_error();
            if (isset($error['first_name'])) {
                $error['last_name'] = $error['first_name'];
            }
            if (isset($error['first_name_furi'])) {
                $error['last_name_furi'] = $error['first_name_furi'];
            }
            if($data_error){
                if($data_error['photo']){
                    $error['photo'] = $data_error['photo'];
                }
                if($data_error['front_photo']){
                    $error['front_photo'] = $data_error['front_photo'];
                }
                if($data_error['back_photo']){
                    $error['back_photo'] = $data_error['back_photo'];
                }
                if($data_error['legal_entity']){
                    $error['legal_entity'] = $data_error['legal_entity'];
                }
                if($data_error['department']){
                    $error['department'] = $data_error['department'];
                }
                if($data_error['address4']){
                    $error['address4'] = $data_error['address4'];
                }
            }
            $data_return["error"] = $error;
        }
        echo json_encode($data_return);
    }

    public function save() { 
        $data_return = Array();
        $data_return['callback'] = "real_register_save";
        $data = $this->session->userdata("user_full_info");

        if (!$data) {
            $data_return["state"] = 0; /* state = 0 : invalid data */
            $data_return["msg"] = my_lang('セッションエラー！ 後でもう一度試してみてください。');
        } else {
            $id = $data['id'];
            unset($data['id']);
            $this->ion_auth->set_hook('post_activate_successful', 'send_email_activate_successful',
                $this, 'send_email_activate_successful', [$data]);
            $active = $this->ion_auth->activate($id);
            $update = $this->m_user->update($id, $data, TRUE);
            if ($active && $update) {
                $this->session->unset_userdata("temp_user");
                $data_return["state"] = 1; /* state = 1 : insert success */
                $data_return["msg"] = "Ok!";
            } else {
                $data_return["state"] = 0; /* state = 0 : invalid data */
                $data_return["msg"] = $this->ion_auth->errors();
            }
        }
        echo json_encode($data_return);
    }

    public function send_email_activate_successful($user) {
        $this->load->library('email');
        $this->load->config('ion_auth', TRUE);
        $this->load->config('site_settings', TRUE);
        $email_config = $this->config->item('email_config', 'ion_auth');
        $admin_email = $this->config->item('site_admin_email');
        $this->email->initialize($email_config);
        $data['user'] = $user;
        $promoter_url = $this->config->item('promoter_site_url');
        $data['promoter_url'] = $promoter_url . '/manager/login';
        $message = $this->load->view("email/active_done", $data, TRUE);
        $this->email->clear();
        $this->email->from($this->config->item('admin_email', 'ion_auth'), my_lang($this->config->item('site_title', 'ion_auth')));
        $this->email->to($user['email']);
        $this->email->bcc($admin_email);
        $this->email->subject(my_lang($this->config->item('site_title', 'ion_auth')) . my_lang('主催者専用アカウントの登録完了通知'));
        if($user['photo']){
            $this->email->attach(s3_url($this->m_files->get($user['photo'])->path));
        }
        if($user['front_photo'] && $user['back_photo']){
            $this->email->attach(s3_url($this->m_files->get($user['back_photo'])->path));
            $this->email->attach(s3_url($this->m_files->get($user['front_photo'])->path));
        }
        $this->email->message($message);
        $this->email->send();
    }

 
    public function _load_event_step($step){
        $this->config->load('site_settings');
        $promoter_site_url = $this->config->item('promoter_site_url');
        $data = [
            'step' => 1,
        ];
        if($step == 4){
            
            $data['promoter_site_url'] = $promoter_site_url;
        };
        return $this->load->view("front/register_event/step$step", $data, TRUE);
    }

   
    public function event_form_check()
    {
        $data = $this->input->post();
        isset($data['event']) ? $validated = true : $validated = false;
        $this->session->set_userdata('option_2',$data['event']);
        $data_return = Array();
        $data_return['callback'] = "event_register_check";
        if ($validated) {
            $this->session->set_userdata("event", $data);
            $data_return["data"] = $data;
            $data_return["state"] = 1;
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $error['event'] = my_lang("選択肢を選んでください。");
            $data_return["error"] = $error;
        }
        echo json_encode($data_return);
    }
    
    public function event_form_check_2()
    {
        $data = $this->input->post();
        isset($data['event_type']) ? $validated = true : $validated = false;
        $data_return = Array();
        $data_return['callback'] = "event_register_check";
        if ($validated) {
            $this->session->set_userdata("event_type", $data);
            $output = ''; 
            $i = 0; 
            $dot = '';
            $size = count($data['event_type']);
            foreach($data['event_type'] as $key => $type) {
                $dot = ++$i === $size ?  '' : ',';
                $output .= (isset($type) ) ? '"'.$type.'"'.$dot  : '';
            }
            $output = '[' . $output . ']';
            $this->session->set_userdata('payment',$output);
            $data_return["data"] = $data;
            $data_return["state"] = 1;
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $error['event_type'] = my_lang('選択肢を選んでください。');
            $data_return["error"] = $error;
        }
        echo json_encode($data_return);
    }

    public function event_form_check_3()
    {
        $data = $this->input->post();
        isset($data['event_with_friend']) ? $validated = true : $validated = false;
        $data_return = Array();
        $data_return['callback'] = "event_register_check";
        if(empty($this->session->userdata('option_1'))) {
            $this->session->set_userdata('option_1',$data['event_with_friend']);
        }
        if($this->session->userdata('option_1') == "event_sam_5" && $data['event_with_friend'] == "false") {
            $this->session->set_userdata('option_1',"event_sam_6");
        }

        if($this->session->userdata('option_1') == "button_three" && $data['event_with_friend'] == "false") {
            $this->session->set_userdata('option_1',"event_sam_8");
        }

        if ($validated) {
            $event = $this->session->userdata("event")['event'];
            $event_type = $this->session->userdata('event_type')['event_type'];
            $data_return["data"] = $data;
            $data_return["data_html"] = $this->choose_event($event, $event_type, $data['event_with_friend']); 
            $data_return["state"] = 1;
        } else {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $error['event_with_friend'] = my_lang('選択肢を選んでください。');
            $data_return["error"] = $error;
        }
        echo json_encode($data_return);
    }

    public function choose_event()
    {
        $data = Array();
        $data['html']= "";
        // show text on registration screen
        $option_1 = $this->session->userdata("option_1");
        $option_2 = $this->session->userdata("option_2");
        $button_four = $this->session->userdata("button_four");
        if($option_1 == "true" && $option_2 == "no_free" && $button_four == null) {
            $data['html']  .= "<div>". my_lang('サンプル④　手動発券・有料')."</div>";

        }

        if($option_1 == "false" && $option_2 == "no_free" && $button_four == null) {
            $data['html']  .= "<div>". my_lang('サンプル②　自動発券・有料')."</div>";
        }

        if($option_1 == "true" && $option_2 == "free" && $button_four == null) {
            $data['html']  .= "<div>". my_lang('サンプル③　手動発券・無料')."</div>";
        }

        if($option_1 == "false" && $option_2 == "free" && $button_four == null)  {
            $data['html']  .= "<div>". my_lang('サンプル①　自動発券・無料')."</div>";
        }

        if($option_1 == 'event_sam_5' && $option_2 == null ) {
            $data['html']  .= "<div>". my_lang('サンプル⑤　手動発券・有料')."</div>";
        }

        if($option_1 == 'event_sam_6') {
            $data['html']  .= "<div>". my_lang('サンプル⑥ 自動発券・有料')."</div>";
        }

        if($option_1 == 'button_three' && $option_2 == null) {
            $data['html']  .= "<div>". my_lang('サンプル⑦物販　手動発券・有料')."</div>";
        }

        if($option_1 == 'event_sam_8') {
            $data['html']  .= "<div>". my_lang('サンプル⑧物販自動発券・有料')."</div>";
        }

        if ($option_1 == "true" && $option_2 == "no_free" && $button_four == "button_four") {
            $data['html']  .= "<div>". my_lang('サンプル⑨動画手動発券・有料')."</div>";
        }

        if ($option_1 == "false" && $option_2 == "no_free" && $button_four == 'button_four') {
            $data['html']  .= "<div>". my_lang('サンプル⑩動画自動発券・有料')."</div>";
        }

        if ($option_1 == "true" && $option_2 == "free" && $button_four == 'button_four') {
            $data['html']  .= "<div>". my_lang('サンプル11動画手動発券・無料')."</div>";
        }

        if ($option_1 == "false" && $option_2 == "free" && $button_four == 'button_four') {
            $data['html']  .= "<div>". my_lang('サンプル12動画　自動発券・無料')."</div>";
        }
    //     // Event auto - free
    //     if($event == "free" && $event_with_friend == "false" ){
    //         $data['html']  .= "<div>サンプル①　自動発券・無料</div>";
    //     }
        
    //     // Event auto - no free
    //     if($event == "no_free" && (in_array("credit_card",$event_type)
    //      || in_array("combini",$event_type) 
    //      || in_array("cash",$event_type))
    //      && $event_with_friend == "false" && !in_array("banking",$event_type)){
    //         $data['html'] .= "<div>サンプル②　自動発券・有料</div>";
    //     }

    //     // Event  manual - free
    //     if($event == "free" && (($event_with_friend == "true") || $event_with_friend == "false")) {
    //         $data['html'] .= "<div>サンプル③　手動発券・無料</div>";
    //    }

    //    // Event manual no free    
    //    if($event == "no_free" && (in_array("credit_card",$event_type)
    //    || in_array("combini",$event_type) 
    //    || in_array("banking",$event_type)) 
    //    && (($event_with_friend == "true") || $event_with_friend == "false")){
    //         $data['html'] .= "<div>サンプル④　手動発券・有料</div>";
    //   }

    //   if($event == "no_free" && in_array("cash",$event_type)
    //    && ($event_with_friend == "true")){
    //         $data['html'] .= "<div>参加者を選ぶことができません。現金以外の決済方法を選択してください。</div>";
    //   }
      return $data['html'];
    }

    /**
     * Checks hiragana
     * ぁ-ゖー
     *
     * @param string $str The value to check.
     *
     * @return boolean
     */
    public function is_katakana($str) {
        $allow_space = FALSE;
        $this->load->library("Japanese_check");
        return $this->japanese_check->is_katakana($str, $allow_space);
    }

    public function is_phone($str) {
        return $this->application_lib->is_phone($str);
    }

    public function is_zip_code($str) {
        return $this->application_lib->is_zip_code($str);
    }

    public function check_exits_domain($str) {
        return $this->application_lib->check_exits_domain($str);
    }

}