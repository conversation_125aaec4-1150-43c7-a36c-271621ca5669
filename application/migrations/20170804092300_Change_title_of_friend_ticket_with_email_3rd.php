<?php
/**
 * Created by PhpStorm.
 * User: luongnv
 * Date: 04/08/2017
 * Time: 09:24
 * @property CI_DB_query_builder $db
 */

class Migration_Change_title_of_friend_ticket_with_email_3rd extends CI_Migration {
    public function up() {
        $table_name = 'event_form_fields';
        $data = Array([
            'field_name' => 'friend_ticket_with_email',
            'more_data'  => '{"field_display_name":"同伴者一括申込フォーム（名前、メールアドレスのみ）","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);

        $this->db->update_batch($table_name, $data, 'field_name');
    }

    public function down() {
        $table_name = 'event_form_fields';
        $data = Array([
            'field_name' => 'friend_ticket_with_email',
            'more_data'  => '{"field_display_name":"同伴者一括購入フォーム（名前、メールアドレスのみ）","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);
        $this->db->update_batch($table_name, $data, 'field_name');
    }
}