<?php

/**
 * Created by IntelliJ IDEA.
 * User: hana
 * Date: 25/07/2017
 * Time: 17:02
 * 20170801104000_Add_tbl_event_fee_combini
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_tbl_event_fee_combini extends CI_Migration {
    public function up() {
        $this->_create_table_event_fee_combini();
    }

    public function down() {
        $this->dbforge->drop_table('event_fee_combini');
    }

    private function _create_table_event_fee_combini() {
        $table_name = 'event_fee_combini';
        if (!$this->db->table_exists($table_name)) {
            $sql = "CREATE TABLE `" . $table_name . "` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) DEFAULT NULL,
                  `fee_id` int(11) DEFAULT NULL,
                  `reception_place` text COLLATE utf8_unicode_ci,
                  `reception_phone` varchar(255),
                  `user_created` int(11) DEFAULT NULL,
                  `time_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                  `user_modified` int(11) DEFAULT NULL,
                  `time_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `deleted` tinyint(4) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
            $this->db->query($sql);
        }
    }
} 