<?php

/**
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_current_stock extends CI_Migration {

    public function up() {
        $sql = "ALTER TABLE `event_fee_items_data` ADD COLUMN `current_stock` INT (11) DEFAULT 0 AFTER `total_stock`;";
        $this->db->query($sql);
    }

    public function down() {
        $sql = " ALTER TABLE `event_fee_items_data` DROP COLUMN `current_stock`;";
        $this->db->query($sql);
    }
}