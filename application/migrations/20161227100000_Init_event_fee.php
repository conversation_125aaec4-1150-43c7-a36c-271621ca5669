<?php

/**
 * User: locmx
 * Date: 12/17/2016
 * Time: 10:57
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Init_event_fee extends CI_Migration {

    public function up() {
        $sql = "ALTER TABLE `event_fee_data` CHANGE COLUMN `fee_id` `fee_item_id` int(11) DEFAULT '0';";
        $this->db->query($sql);

        $sql = "ALTER TABLE `event_fee_shipping` CHANGE COLUMN `fee_id` `fee_item_id` int(11) DEFAULT '0';";
        $this->db->query($sql);

        $this->dbforge->rename_table('event_fee', 'event_fee_items');
        $this->dbforge->rename_table('event_fee_data', 'event_fee_item_data');
        $this->dbforge->rename_table('event_fee_shipping', 'event_fee_item_shipping');

        $this->_create_table_event_fee();
        $this->_create_table_event_fee_forms_data();
        $this->_create_table_event_fee_cashier();
        $this->_create_table_event_fee_banking();
    }

    public function down() {
        $this->dbforge->drop_table('event_fee');
        $this->dbforge->drop_table('event_fee_data');
        $this->dbforge->drop_table('event_fee_cashier');
        $this->dbforge->drop_table('event_fee_banking');

        $this->dbforge->rename_table('event_fee_items', 'event_fee');
        $this->dbforge->rename_table('event_fee_item_data', 'event_fee_data');
        $this->dbforge->rename_table('event_fee_item_shipping', 'event_fee_shipping');
    }

    private function _create_table_event_fee() {
        $table_name = 'event_fee';
        $sql = "CREATE TABLE `" . $table_name . "` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) DEFAULT NULL,
                  `type` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `is_tax` tinyint(4) DEFAULT '0',
                  `tax` decimal(11,0) DEFAULT NULL,
                  `note` text COLLATE utf8_unicode_ci,
                  `user_created` int(11) DEFAULT NULL,
                  `time_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                  `user_modified` int(11) DEFAULT NULL,
                  `time_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `deleted` tinyint(4) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
        $this->db->query($sql);
    }

    private function _create_table_event_fee_forms_data() {
        $table_name = 'event_fee_data';
        $sql = "CREATE TABLE `" . $table_name . "` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) DEFAULT NULL,
                  `fee_id` int(11) DEFAULT NULL,
                  `fee_item_id` int(11) DEFAULT NULL,
                  `item_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `position` int(11) DEFAULT NULL,
                  `user_created` int(11) DEFAULT NULL,
                  `time_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                  `user_modified` int(11) DEFAULT NULL,
                  `time_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `deleted` tinyint(4) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
        $this->db->query($sql);
    }

    private function _create_table_event_fee_cashier() {
        $table_name = 'event_fee_cashier';
        $sql = "CREATE TABLE `" . $table_name . "` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) DEFAULT NULL,
                  `fee_id` int(11) DEFAULT NULL,
                  `time_begin` datetime DEFAULT NULL,
                  `time_end` datetime DEFAULT NULL,
                  `reception_place` text COLLATE utf8_unicode_ci,
                  `note` text COLLATE utf8_unicode_ci,
                  `user_created` int(11) DEFAULT NULL,
                  `time_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                  `user_modified` int(11) DEFAULT NULL,
                  `time_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `deleted` tinyint(4) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
        $this->db->query($sql);
    }

    private function _create_table_event_fee_banking() {
        $table_name = 'event_fee_banking';
        $sql = "CREATE TABLE `" . $table_name . "` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `event_id` int(11) DEFAULT NULL,
                  `fee_id` int(11) DEFAULT NULL,
                  `account_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `bank_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `shop_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `type` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `account_number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `transfer_deadline` datetime DEFAULT NULL,
                  `transfer_fee` tinyint(2) DEFAULT '0',
                  `note` text COLLATE utf8_unicode_ci,
                  `user_created` int(11) DEFAULT NULL,
                  `time_created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                  `user_modified` int(11) DEFAULT NULL,
                  `time_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `deleted` tinyint(4) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
        $this->db->query($sql);
    }
}