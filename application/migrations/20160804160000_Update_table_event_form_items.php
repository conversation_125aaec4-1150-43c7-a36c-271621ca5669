<?php

/**
 * User: locmx
 * Date: 08/04/2016
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Update_table_event_form_items extends CI_Migration {

    public function up() {
        $this->_update_table_event_form_items();
    }

    public function down() {
        $this->_downgrade_table_event_form_items();
    }

    private function _update_table_event_form_items() {
        $table_name = 'event_form_items';
        $data = Array(
            Array(
                'id'         => '18',
                'field_name' => 'people_count',
                'more_data'  => '{"field_display_name":"\u4e88\u7d04\u540c\u4f34\u8005\u6570","field_group":"people_count_typing","field_note":"\u81ea\u7531\u5165\u529b","field_unit":"","field_data":""}',
            ),
            Array(
                'id'         => '19',
                'field_name' => 'people_count',
                'more_data'  => '{"field_display_name":"\u4e88\u7d04\u540c\u4f34\u8005\u6570","field_group":"people_count_select","field_note":"1\u30012\u30013\u30014\u30015\u30016\u30017\u30018\u30019\u300110","field_unit":"\u4eba","field_data":{"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7","8":"8","9":"9","10":"10"}}',
            ),
        );

        $this->db->update_batch($table_name, $data, 'id');
    }

    private function _downgrade_table_event_form_items() {
        $table_name = 'event_form_items';
        $data = Array(
            Array(
                'id'         => '18',
                'field_name' => 'number_companion',
                'more_data'  => '{"field_display_name":"\u4e88\u7d04\u540c\u4f34\u8005\u6570","field_group":"","field_note":"\u81ea\u7531\u5165\u529b","field_unit":"","field_data":""}',
            ),
            Array(
                'id'         => '19',
                'field_name' => 'number_companion_range',
                'more_data'  => '{"field_display_name":"\u4e88\u7d04\u540c\u4f34\u8005\u6570","field_group":"","field_note":"1\u30012\u30013\u30014\u30015\u30016\u30017\u30018\u30019\u300110","field_unit":"\u4eba","field_data":{"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7","8":"8","9":"9","10":"10"}}',
            ),
        );

        $this->db->update_batch($table_name, $data, 'id');
    }
}