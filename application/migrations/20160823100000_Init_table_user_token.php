<?php

/**
 * Class Migration_Init_table_user_token
 *
 * @property CI_DB_query_builder $db
 * @property CI_DB_forge         $dbforge
 */
class Migration_Init_table_user_token extends CI_Migration {

    public function up() {
        $this->_create_table_user_token();
    }

    public function down() {
        $this->dbforge->drop_table('user_token');
    }

    private function _create_table_user_token() {
        $sql = "CREATE TABLE `user_token` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `user_id` mediumint(8) NOT NULL,
                  `token` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
                  `time_created` int(11) DEFAULT NULL,
                  `time_expired` int(11) DEFAULT NULL,
                  `deleted` tinyint(2) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
        $this->db->query($sql);
    }
}