<?php

/**
 * User: locmx
 * Date: 07/20/2016
 * Time: 9:32
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_draff extends CI_Migration {

    public function up() {
        $this->_update_table_events();
    }

    public function down() {
        $this->_downgrade_table_events();
    }

    private function _update_table_events(){
        $sql = "ALTER TABLE `events` ADD COLUMN `draff` int(11) DEFAULT 0;";
        $this->db->query($sql);
    }
    private function _downgrade_table_events(){
        $sql = " ALTER TABLE `events` DROP COLUMN `draff`;";
        $this->db->query($sql);
    }
}