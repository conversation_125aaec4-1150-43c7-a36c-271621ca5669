<?php

/**
 * User: locmx
 * Date: 09/22/2016
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Edit_events_field_is_ticket extends CI_Migration {

    public function up() {
        $sql = "ALTER TABLE `events` CHANGE COLUMN `is_ticket` `type_ticket` int(2) DEFAULT 1 COMMENT '1: 自動発券（予約申し込み完了時又は決済完了時に自動送信）; 2: 手動発券（お申込者一覧画面にて操作）';";
        $this->db->query($sql);
    }

    public function down() {
        $sql = "ALTER TABLE `events` CHANGE COLUMN `type_ticket` `is_ticket` int(2) DEFAULT NULL;";
        $this->db->query($sql);
    }
}