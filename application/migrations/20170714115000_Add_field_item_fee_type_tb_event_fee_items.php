<?php

/**
 * Created by IntelliJ IDEA.
 * User: hana
 * Date: 12/06/2017
 * Time: 09:37
 *20170714114500_Add_field_item_fee_type_tb_event_fee_items
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_item_fee_type_tb_event_fee_items extends CI_Migration {

    public function up() {
        if (!$this->db->field_exists('item_type', 'event_fee_items')) {
            $sql = "ALTER TABLE `event_fee_items` ADD COLUMN `item_type`  int(2) DEFAULT 1;";
            $this->db->query($sql);
        }
    }

    public function down() {
        if ($this->db->field_exists('status_email', 'event_fee_items')) {
            $sql = "ALTER TABLE `event_fee_items` DROP COLUMN `item_type`  ";
            $this->db->query($sql);
        }
    }
}