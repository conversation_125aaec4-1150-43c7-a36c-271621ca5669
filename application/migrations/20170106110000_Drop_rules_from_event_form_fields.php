<?php
/**
 * @property CI_DB_query_builder $db
 */

class Migration_Drop_rules_from_event_form_fields extends CI_Migration {
    public function up() {
        $sql = "DELETE FROM `event_form_fields` WHERE `field_name` LIKE 'rules';";
        $this->db->query($sql);
    }

    public function down() {
        $more_data = new stdClass();
        $more_data->field_display_name = '個人情報保護方針';
        $more_data->field_group = '';
        $more_data->field_note = '';
        $more_data->field_unit = '';
        $more_data->field_data = '';
        $data = Array(
            'field_name' => 'rules',
            'more_data'  => json_encode($more_data),
            'type'       => 'checkbox',
            'is_custom_field'  => 0,
            'default_position' => 1000
        );
        $this->db->insert('event_form_fields', $data);
    }
}