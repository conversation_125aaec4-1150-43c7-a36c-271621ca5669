<?php

/**
 * Created by IntelliJ IDEA.
 * User: hana
 * Date: 17/07/2017
 * Time: 14:52
 * 20170721143000_Change_title_button_friend_ticket_tb_event_form_fields
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Change_title_button_friend_ticket_tb_event_form_fields extends CI_Migration {
    public function up() {
        $table_name = 'event_form_fields';
        $data = Array([
            'field_name' => 'friend_ticket',
            'more_data'  => '{"field_display_name":"同伴者一括申込フォーム","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);

        $this->db->update_batch($table_name, $data, 'field_name');
    }

    public function down() {
        $table_name = 'event_form_fields';
        $data = Array([
            'field_name' => 'friend_ticket',
            'more_data'  => '{"field_display_name":"同伴者一括購入フォーム","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);
        $this->db->update_batch($table_name, $data, 'field_name');
    }
}