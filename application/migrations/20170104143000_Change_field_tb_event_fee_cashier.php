<?php

/**
 * @property CI_DB_query_builder $db
 */
class Migration_Change_field_tb_event_fee_cashier extends CI_Migration {

    public function up() {
        $this->_up_field_date_begin();
        $this->_up_field_date_end();
    }

    public function down() {
        $this->_down_field_date_begin();
        $this->_down_field_date_end();
    }

    private function _up_field_date_begin() {
        $sql = " ALTER TABLE `event_fee_cashier` ADD COLUMN `date_begin` DATE NULL DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_fee_cashier` CHANGE `time_begin` `time_begin` TIME NULL DEFAULT NULL;";
        $this->db->query($sql);
    }

    private function _up_field_date_end() {
        $sql = " ALTER TABLE `event_fee_cashier` ADD COLUMN `date_end` DATE NULL DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_fee_cashier` CHANGE `time_end` `time_end` TIME NULL DEFAULT NULL;";
        $this->db->query($sql);
    }

    private function _down_field_date_begin() {
        $sql = "ALTER TABLE `event_fee_cashier` CHANGE `time_begin` `time_begin` DATETIME NULL DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_fee_cashier` DROP COLUMN `date_begin`;";
        $this->db->query($sql);
    }

    private function _down_field_date_end() {
        $sql = "ALTER TABLE `event_fee_cashier` CHANGE `time_end` `time_end` DATETIME NULL DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_fee_cashier` DROP COLUMN `date_end`;";
        $this->db->query($sql);
    }
}