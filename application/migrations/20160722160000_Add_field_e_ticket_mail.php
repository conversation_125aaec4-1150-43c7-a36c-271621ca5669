<?php

/**
 * User: locmx
 * Date: 07/22/2016
 * Time: 9:32
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_e_ticket_mail extends CI_Migration {

    public function up() {
        $this->_update_table_events();
    }

    public function down() {
        $this->_downgrade_table_events();
    }

    private function _update_table_events(){
        $sql = "ALTER TABLE `events` ADD COLUMN `e_ticket_mail` TEXT DEFAULT NULL COMMENT '';";
        $this->db->query($sql);
    }
    private function _downgrade_table_events(){
        $sql = " ALTER TABLE `events` DROP COLUMN `e_ticket_mail`;";
        $this->db->query($sql);
    }
}