<?php

/**
 * Class Migration_Add_field_transaction_type_table_event_subscribers
 *
 * @property CI_DB_query_builder $db
 * @property CI_DB_forge         $dbforge
 */
class Migration_Add_field_transaction_type_table_event_subscribers extends CI_Migration {

    public function up() {
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `total_bill` decimal (11) DEFAULT 0;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `total_paid` decimal (11) DEFAULT 0;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `transaction_type` VARCHAR (50) DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `transaction_last_time` datetime DEFAULT NULL;";
        $this->db->query($sql);
    }

    public function down() {
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `total_bill`;";
        $this->db->query($sql);
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `total_paid`;";
        $this->db->query($sql);
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `transaction_type`;";
        $this->db->query($sql);
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `transaction_last_time`;";
        $this->db->query($sql);
    }
}