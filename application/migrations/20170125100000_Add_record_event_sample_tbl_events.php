<?php

/**
 * @property CI_DB_query_builder $db
 */
class Migration_Add_record_event_sample_tbl_events extends CI_Migration {

    public function up() {
        $sql = "ALTER TABLE `events` ADD COLUMN `type` VARCHAR (10) DEFAULT NULL AFTER `target`;";
        $this->db->query($sql);
        $this->db->insert('events', [
            'name'                  => 'サンプルイベント',
            'start_date'            => '2020-07-24',
            'start_time'            => '14:00:00',
            'opening_time'          => '15:00:00',
            'end_date'              => '2020-07-25',
            'end_time'              => '12:00:00',
            'total_held_hours'      => '22',
            'address1'              => '国立競技場',
            'subcrisebers_expected' => '20000',
            'show_ticket_display'   => '{"name":"","start_date":"","start_time":"","opening_time":"","end_time":"","address1":"","sms1":""}',
            'draft'                 => '5',
            'e_ticket_mail'         => '[予約者会社名]<br/>[予約者部署名]<br/>[予約者氏] [予約者名]様<br/><br/>この度は、[イベント名]にお申し込みいただきまして誠にありがとうございました。<br/>下記の通り、受け付けを完了し、電子チケットを本メール及び添付画像にて同じものをお送りいたします。<br/><br/>当日の受付にて本メールもしくは、添付画像のご提示をお願いいたします。',
            'cat_id'                => '93',
            'type'                  => 'sample',
        ]);
        $event_id = $this->db->insert_id();

        $this->db->insert('event_forms', [
            'event_id'   => $event_id,
            'form_title' => 'サンプルイベント',
            'type_form'  => 'subscriber',
        ]);
        $form_id = $this->db->insert_id();

        $this->db->insert_batch('event_forms_data', [
            [
                'event_id'  => $event_id,
                'field_id'  => '1',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => 'required',
                'required'  => '1',
                'position'  => '1',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '2',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => 'required',
                'required'  => '1',
                'position'  => '1',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '13',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => 'required',
                'required'  => '1',
                'position'  => '2',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '20',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => 'required',
                'required'  => '1',
                'position'  => '2',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '3',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => '',
                'required'  => '0',
                'position'  => '3',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '4',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => '',
                'required'  => '0',
                'position'  => '3',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '8',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => '',
                'required'  => '0',
                'position'  => '4',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '11',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => '',
                'required'  => '0',
                'position'  => '6',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '10',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => '',
                'required'  => '0',
                'position'  => '5',
            ],
            [
                'event_id'  => $event_id,
                'field_id'  => '5',
                'form_id'   => $form_id,
                'type_form' => 'subscriber',
                'rules'     => '',
                'required'  => '0',
                'position'  => '7',
            ],
        ]);
    }

    public function down() {
        $sql = " ALTER TABLE `events` DROP COLUMN `type`;";
        $this->db->query($sql);
    }
}