<?php
class Migration_Alter_friend_ticket_display_name extends CI_Migration {
    public function up() {
        $table_name = "event_form_fields";

        // friend_ticket
        $data = Array([
            'field_name' => 'friend_ticket',
            'more_data'  => '{"field_display_name":"同伴者一括申し込みフォーム①<br><ul><li>同伴者は購入者と同じフォーム内容。</li><li>決済完了時に同伴者へチケットが送信されます。</li></ul>","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);
        $this->db->update_batch($table_name, $data, 'field_name');

        // friend_ticket_with_email
        $data = Array([
            'field_name' => 'friend_ticket_with_email',
            'more_data'  => '{"field_display_name":"同伴者一括申し込みフォーム②<br><ul><li>同伴者は名前、メールアドレスのみのフォーム。</li><li>決済完了時に同伴者へチケットが送信されます。</li></ul>","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);
        $this->db->update_batch($table_name, $data, 'field_name');


        // friend_ticket_without_email
        $data = Array([
            'field_name' => 'friend_ticket_without_email',
            'more_data'  => '{"field_display_name":"同伴者一括申し込みフォーム③<br><ul><li>同伴者は名前、メールアドレスのみのフォーム。</li><li>決済完了時に同伴者へチケットが送信されます。</li></ul>","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);
        $this->db->update_batch($table_name, $data, 'field_name');
    }

    public function down() {
        $table_name = "event_form_fields";
        $data = Array([
            'field_name' => ['friend_ticket','friend_ticket_with_email','friend_ticket_without_email'],
            'more_data'  => '{"field_display_name":"同伴者一括申込フォーム<br><ul><li>同伴者は名前、メールアドレスのみのフォーム。</li><li>決済完了時に同伴者へチケットが送信されます。</li></ul>","field_group":"","field_note":"","field_unit":"","field_data":""}',
        ]);
        $this->db->update_batch($table_name, $data, 'field_name');
    }
}