<?php

/**
 * User: locmx
 * Date: 08/04/2016
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_vip_email_notify extends CI_Migration {

    public function up() {
        $this->_update_table_events();
    }

    public function down() {
        $this->_downgrade_table_events();
    }

    private function _update_table_events() {
        $sql = "ALTER TABLE `events` ADD COLUMN `vip_email_notify` TEXT DEFAULT NULL COMMENT 'list email send notify vip' ;";
        $this->db->query($sql);
    }

    private function _downgrade_table_events() {
        $sql = " ALTER TABLE `events` DROP COLUMN `vip_email_notify`;";
        $this->db->query($sql);
    }
}