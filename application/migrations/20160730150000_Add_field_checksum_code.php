<?php

/**
 * User: locmx
 * Date: 07/27/2016
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_checksum_code extends CI_Migration {

    public function up() {
        $this->_update_table_event_subscribers();
    }

    public function down() {
        $this->_downgrade_table_event_subscribers();
    }

    private function _update_table_event_subscribers() {
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `checksum_code` VARCHAR(255) DEFAULT NULL ;";
        $this->db->query($sql);
    }

    private function _downgrade_table_event_subscribers() {
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `checksum_code`;";
        $this->db->query($sql);
    }
}