<?php

/**
 * User: locmx
 * Date: 10/05/2016
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Change_updated_time_database extends CI_Migration {

    public function up() {
        $sql = "ALTER TABLE `events` CHANGE COLUMN `created_time` `created_time` datetime DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `events` CHANGE COLUMN `updated_time` `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` CHANGE COLUMN `created_time` `created_time` datetime DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` CHANGE COLUMN `updated_time` `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;";
        $this->db->query($sql);
    }

    public function down() {
        $sql = "ALTER TABLE `events` CHANGE COLUMN `created_time` `created_time` datetime DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `events` CHANGE COLUMN `updated_time` `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` CHANGE COLUMN `created_time` `created_time` datetime DEFAULT NULL;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` CHANGE COLUMN `updated_time` `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;";
        $this->db->query($sql);
    }
}