<?php

/**
 * @property CI_DB_query_builder $db
 */
class Migration_Add_column_booking_mailing extends CI_Migration {
    public function up() {
        $this->change_delete_booking_mailing();
        $this->change_delete_mailing();
        $this->change_delete_mailing_fee();
        $this->change_delete_mailing_details();
        $this->_create_table_mailing_form_field();
        $this->add_modify_column();

    }

    private function add_modify_column() {
        $sql = "ALTER TABLE `booking_mailing` ADD COLUMN `number_click` INT (11) DEFAULT 0 AFTER `booking_time`, ADD COLUMN `number_open` INT (11) DEFAULT 0 AFTER `number_click`, ADD COLUMN `quantity` INT (11) DEFAULT 0 AFTER `number_open`;";
        $this->db->query($sql);
    }

    private function _create_table_mailing_form_field() {
        $sql = "CREATE TABLE `mailing_form_fields` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `mailing_id` int(11) DEFAULT NULL,
              `field_id` int(11) DEFAULT NULL,
              `field_name` Varchar(255) DEFAULT NULL,
              `user_created` int(11) DEFAULT NULL,
              `time_created` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
              `user_modified` int(11) DEFAULT NULL,
              `time_modified` datetime DEFAULT NULL,
              `deleted` tinyint(4) DEFAULT '0',
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;";
        $this->db->query($sql);
    }

    private function change_delete_booking_mailing() {
        $sql = "ALTER TABLE `booking_mailing` CHANGE `delete` `deleted` tinyint(4) DEFAULT '0';";
        $this->db->query($sql);
    }

    private function change_delete_mailing() {
        $sql = "ALTER TABLE `mailing` CHANGE `delete` `deleted` tinyint(4) DEFAULT '0';";
        $this->db->query($sql);
    }

    private function change_delete_mailing_fee() {
        $sql = "ALTER TABLE `mailing_fee` CHANGE `delete` `deleted` tinyint(4) DEFAULT '0';";
        $this->db->query($sql);
    }

    private function change_delete_mailing_details() {
        $sql = "ALTER TABLE `mailing_details` CHANGE `detete` `deleted` tinyint(4) DEFAULT '0';";
        $this->db->query($sql);
    }

    public function down() {
        $sql = " ALTER TABLE `booking_mailing` DROP COLUMN `number_click`,DROP COLUMN `number_open`;";
        $this->db->query($sql);
        $this->dbforge->drop_table('mailing_form_fields');
    }
}