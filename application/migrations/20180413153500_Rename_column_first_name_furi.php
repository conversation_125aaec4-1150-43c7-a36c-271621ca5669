<?php
class Migration_Rename_column_first_name_furi extends CI_Migration {
    public function up() {

        // update first name field name
        $this->db->where("field_name", "first_name_furi");
        $fields = $this->db->get("event_form_fields");
        $result = $fields->result();
        if(count($result)){
            $more_data = json_decode($result[0]->more_data);
            if($more_data){
                $more_data->field_display_name = "氏名フリガナ";
                $this->db->where("field_name", "first_name_furi");
                $this->db->set("more_data", json_encode($more_data));
                $this->db->update("event_form_fields");
            }
        }

    }

    public function down() {

    }
}