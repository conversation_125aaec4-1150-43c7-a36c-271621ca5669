<?php

/**
 * Class Migration_Add_field_data_checkbox_table_event_form_fields_data
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_data_checkbox_table_event_form_fields_data extends CI_Migration {

    public function up() {
        $this->_update_table_event_form_fields_data();
    }

    public function down() {
        $this->_downgrade_table_event_form_fields_data();
    }

    private function _update_table_event_form_fields_data() {
        $table_name = 'event_form_fields_data';
        $this->db->insert_batch($table_name, [
            [
                'field_id' => '14',
                'value'    => '1',
                'data'     => 'yes',
                'typing'   => 'checkbox',
                'unit'     => '',
                'deleted'  => '0',
            ],
            [
                'field_id' => '15',
                'value'    => '1',
                'data'     => 'yes',
                'typing'   => 'checkbox',
                'unit'     => '',
                'deleted'  => '0',
            ],
        ]);
    }

    private function _downgrade_table_event_form_fields_data() {
        $table_name = 'event_form_fields_data';
        $this->db->delete($table_name, "field_id='14'");
        $this->db->delete($table_name, "field_id='15'");
    }
}