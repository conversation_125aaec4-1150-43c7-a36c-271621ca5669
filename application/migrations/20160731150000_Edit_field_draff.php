<?php

/**
 * User: locmx
 * Date: 07/27/2016
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Edit_field_draff extends CI_Migration {

    public function up() {
        $this->_update_table_events();
    }

    public function down() {
        $this->_downgrade_table_events();
    }

    private function _update_table_events() {
        $sql = "ALTER TABLE `events` CHANGE COLUMN `draff` `draft` int(11) DEFAULT NULL;";
        $this->db->query($sql);
    }

    private function _downgrade_table_events() {
        $sql = "ALTER TABLE `events` CHANGE COLUMN `draft` `draff` int(11) DEFAULT NULL;";
        $this->db->query($sql);
    }
}