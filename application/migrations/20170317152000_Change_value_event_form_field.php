<?php

/**
 * Created by PhpStorm.
 * User: Hp
 * Date: 3/17/2017
 * Time: 3:21 PM
 */
class Migration_Change_value_event_form_field extends CI_Migration {

    public function up() {
        $this->_update_table_event_field_name_fields();
        $this->_update_table_event_form_fields();
    }

    private function _update_table_event_field_name_fields() {
        $table_name = 'event_form_fields';
        $data = Array(
            Array(
                'field_name'         => 'shipping_address1',
                'field_display_name' => '都道府府県',
            ),
            Array(
                'field_name'         => 'shipping_address2',
                'field_display_name' => '区市町村',
            ),
        );

        $this->db->update_batch($table_name, $data, 'field_display_name');
    }

    private function _update_table_event_form_fields() {
        $table_name = 'event_form_fields';
        $data = Array(
            Array(
                'field_name'         => 'shipping_address1',
                'field_display_name' => '都道府県',
                'more_data'          => '{"field_display_name":"\u90fd\u9053\u5e9c\u770c","field_group":"shipping_info","field_note":"","field_unit":"","field_data":""}',
            ),
            Array(
                'field_name' => 'shipping_address2',
            ),
        );

        $this->db->update_batch($table_name, $data, 'field_name');
    }
}
