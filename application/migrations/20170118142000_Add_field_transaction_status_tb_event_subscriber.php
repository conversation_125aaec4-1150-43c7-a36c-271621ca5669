<?php
/**
 * Created by IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 18/01/2017
 * Time: 2:16 CH
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_transaction_status_tb_event_subscriber extends CI_Migration {

    public function up() {
        $this->_up_add_field_transaction_status();
    }

    public function down() {
        $this->_down_drop_field_transaction_status();
    }

    private function _down_drop_field_transaction_status() {
        $sql = "ALTER TABLE `event_subscribers` DROP COLUMN `transaction_status`;";
        $this->db->query($sql);
    }

    private function _up_add_field_transaction_status() {
        $sql = " ALTER TABLE `event_subscribers` ADD COLUMN `transaction_status`  int(2) DEFAULT 0;";
        $this->db->query($sql);
    }

}