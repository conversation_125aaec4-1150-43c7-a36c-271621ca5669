<?php

/**
 * User: locmx
 * Date: 07/22/2016
 * Time: 9:32
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Add_field_table_event_subscribers extends CI_Migration {

    public function up() {
        $this->_update_table_event_subscribers();
    }

    public function down() {
        $this->_downgrade_table_event_subscribers();
    }

    private function _update_table_event_subscribers() {
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `department_name` VARCHAR (255) DEFAULT NULL ;";
        $this->db->query($sql);
        $sql = "ALTER TABLE `event_subscribers` ADD COLUMN `canceled` INT (11) DEFAULT 0 ;";
        $this->db->query($sql);
    }

    private function _downgrade_table_event_subscribers() {
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `department_name`;";
        $this->db->query($sql);
        $sql = " ALTER TABLE `event_subscribers` DROP COLUMN `canceled`;";
        $this->db->query($sql);
    }
}