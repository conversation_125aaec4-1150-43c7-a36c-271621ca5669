<?php

/**
 * User: locmx
 * Date: 11/15/2016
 * Time: 10:57
 *
 * @property CI_DB_query_builder $db
 */
class Migration_Init_event_form_items_data extends CI_Migration {

    public function up() {
        $this->_insert_table();
    }

    public function down() {
        $this->dbforge->drop_table('event_form_items_data');
    }

    private function _insert_table() {
        $table_name = 'event_form_items_data';
        $sql = "DROP TABLE IF EXISTS `" . $table_name . "`;";
        $this->db->query($sql);
        $sql = "CREATE TABLE `" . $table_name . "` (
                  `id` int(11) NOT NULL AUTO_INCREMENT,
                  `form_id` int(11) NOT NULL,
                  `value` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `data` text COLLATE utf8_unicode_ci,
                  `unit` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `checked_time` datetime DEFAULT NULL,
                  `user_created` int(11) DEFAULT NULL,
                  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `user_update` int(11) DEFAULT NULL,
                  `position` int(11) DEFAULT NULL,
                  `deleted` tinyint(2) DEFAULT '0',
                  PRIMARY KEY (`id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";
        $this->db->query($sql);

        $this->db->insert_batch($table_name, [
            [
                'form_id' => '5',
                'value'   => 'male',
                'data'    => '男性',
                'unit'    => '',
            ],
            [
                'form_id' => '5',
                'value'   => 'female',
                'data'    => '女性',
                'unit'    => '',
            ],
            [
                'form_id' => '7',
                'value'   => '10',
                'data'    => '10代',
                'unit'    => '代',
            ],
            [
                'form_id' => '7',
                'value'   => '20',
                'data'    => '20代',
                'unit'    => '代',
            ],
            [
                'form_id' => '7',
                'value'   => '30',
                'data'    => '30代',
                'unit'    => '代',
            ],
            [
                'form_id' => '7',
                'value'   => '40',
                'data'    => '40代',
                'unit'    => '代',
            ],
            [
                'form_id' => '7',
                'value'   => '50',
                'data'    => '50代',
                'unit'    => '代',
            ],
            [
                'form_id' => '7',
                'value'   => '60',
                'data'    => '60代',
                'unit'    => '代',
            ],
            [
                'form_id' => '19',
                'value'   => '1',
                'data'    => '1',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '2',
                'data'    => '2',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '3',
                'data'    => '3',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '4',
                'data'    => '4',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '5',
                'data'    => '5',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '6',
                'data'    => '6',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '7',
                'data'    => '7',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '8',
                'data'    => '8',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '9',
                'data'    => '9',
                'unit'    => '人',
            ],
            [
                'form_id' => '19',
                'value'   => '10',
                'data'    => '10',
                'unit'    => '人',
            ],
            [
                'form_id' => '21',
                'value'   => '1',
                'data'    => '同意',
                'unit'    => '',
            ],
        ]);
    }
}