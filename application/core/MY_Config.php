<?php

/**
 * Created by PhpStorm.
 * User: nguyen
 * Date: 4/8/2016
 * Time: 10:32 AM
 */
class MY_Config extends CI_Config {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Set a config file item
     *
     * @param	string|Array	$item	Config item key
     * @param	string	$value	Config item value
     * @return	void
     */
    public function set_item($item, $value) {
        if (is_string($item)) {
            parent::set_item($item, $value);
        } else if (is_array($item) && count($item) == 2) {
            $file = $item[0];
            $index = $item[1];
            if (isset($this->config[$file])) {
                $this->config[$file][$index] = $value;
            } else {
                throw new RuntimeException('Config file "' . $file . '" not found!');
            }
        } else {
            throw new RuntimeException('Fist param must be String or array with 2 element!');
        }
    }
}