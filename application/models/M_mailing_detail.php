<?php

/**
 * Created by PhpStorm.
 * User: Hp
 * Date: 3/6/2017
 * Time: 11:43 AM
 */
class M_mailing_detail extends Crud_manager {
    protected $_table = 'mailing_details';
    public $schema = [
        'first_name'      => [
            'field'    => 'first_name',
            'db_field' => 'es.first_name',
            'label'    => '氏',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'booking_id'      => [
            'field'    => 'booking_id',
            'db_field' => 'bm.id',
            'label'    => 'Ma booking mailing',
            'rules'    => '',
            'form'     => FALSE,
            'filter'   => [
                'type'        => 'text',
                'search_type' => 'where',
                'db_field'    => 'booking_id',
            ],
        ],
        'mailing_id'      => [
            'field'    => 'mailing_id',
            'db_field' => 'mailing_id',
            'label'    => 'Ma mailing',
            'rules'    => '',
            'form'     => FALSE,
            'filter'   => [
                'type'        => 'text',
                'search_type' => 'where',
                'db_field'    => 'mailing_id',
            ],
        ],
        'last_name'       => [
            'field'    => 'last_name',
            'db_field' => 'es.last_name',
            'label'    => '名',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'first_name_furi' => [
            'field'    => 'first_name_furi',
            'db_field' => 'es.first_name_furi',
            'label'    => 'フリガナ氏',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'last_name_furi'  => [
            'field'    => 'last_name_furi',
            'db_field' => 'es.last_name_furi',
            'label'    => 'フリガナ名',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'company_name'    => [
            'field'    => 'company_name',
            'db_field' => 'es.company_name',
            'label'    => '会社名',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'department_name' => [
            'field'    => 'department_name',
            'db_field' => 'es.department_name',
            'label'    => '部署',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'email'           => [
            'field'    => 'email',
            'db_field' => 'es.email',
            'label'    => 'メールアドレス',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'status'          => [
            'field'    => 'status',
            'db_field' => 'status',
            'label'    => '配信ステータス',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => array(
                'callback_render_data' => 'get_status_text',
                'class'                => 'no-wrap center disable_sort',
            ),
        ],
        'time_open'       => [
            'field'    => 'time_open',
            'db_field' => 'msh.time_open',
            'label'    => '開封日時',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => TRUE,
        ],
        'time_click'      => [
            'field'    => 'time_click',
            'db_field' => 'msh.time_click',
            'label'    => 'クリック日時',
            'rules'    => '',
            'form'     => FALSE,
            'table'    => [
                'callback_render_data' => "get_data_time_click",
                'class'                => "",
            ],
        ],
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get['join_mail_detail_table'] = "join_mail_detail_table";
        $this->updateLabels();
    }

    public function updateLabels()
    {
        foreach ($this->schema as $key => $field) {
            if (isset($field['label'])) {
                $this->schema[$key]['label'] = my_lang($field['label']);
            }
        }
    }

    public function join_mail_detail_table() {
        $this->db->select($this->_table_alias . ".*");
        $this->db->select("es.first_name as first_name,es.last_name as last_name,es.first_name_furi as first_name_furi,es.last_name_furi as last_name_furi,es.company_name as company_name,es.department_name as department_name,es.email as email,msh.time_open as time_open,msh.time_click as time_click");
        $this->db->join("mailing_subscriber_history as msh", $this->_table_alias . ".subscriber_id=msh.subscriber_id", 'LEFT');
        $this->db->join("event_subscribers as es", $this->_table_alias . ".subscriber_id=es.id AND es.deleted=0", 'LEFT');
        $this->db->where("es.email is not null");
        $this->db->where("es.email NOT IN ( SELECT email FROM mail_black_list WHERE deleted = 0 )");
        $this->db->group_by('m.subscriber_id');
    }
    public function get_list_mail_del() {
        $this->db->select($this->_table_alias . ".*, es.email as email");
        $this->db->join("event_subscribers as es", $this->_table_alias . ".subscriber_id=es.id AND es.deleted=0", 'LEFT');
        $this->db->where("es.email is not null");
        $this->db->where("es.email NOT IN ( SELECT email FROM mail_black_list WHERE deleted = 0 )");
        $this->db->group_by('m.id');
        $this->db->where('m.deleted = 0');
    }

    public function join_time_booking_table() {
        $this->db->join("booking_mailing as bm", "bm.mailing_id =" . $this->_table_alias . ".mailing_id");
        $this->db->group_by('m.subscriber_id');
    }

    public function get_list_mail_cancel() {
        $this->db->select($this->_table_alias . ".*, es.email as email");
        $this->db->join("event_subscribers as es", $this->_table_alias . ".subscriber_id=es.id AND es.deleted=0", 'LEFT');
        $this->db->group_by('m.id');
    }

    public function unset_all_before_get() {
        $this->before_get = [];
    }

    public function get_data_time_click($origin_column_value, $column_name, &$record, $column_data, $caller) {
        $origin_column_value = json_decode($origin_column_value, TRUE);
        $status = NULL;
        if ($origin_column_value != NULL) {
            foreach ($origin_column_value as $key => $value) {
                $status .= $value . " <br>";
            }
        }
        return $status;
    }

    public function unset_before_get($string) {
        if (trim($string))
            unset($this->before_get[$string]);
    }

    public function set_before_get($string) {
        if (trim($string))
            $this->before_get[$string] = $string;
    }

    public function get_status_subs() {
        return [
            '0' => my_lang('配信風能'),
            '1' => my_lang('配信可能')
            // '2' => '配信停止',
        ];
    }

    public function set_soft_delete($stt) {
        $this->soft_delete = $stt;
    }

}