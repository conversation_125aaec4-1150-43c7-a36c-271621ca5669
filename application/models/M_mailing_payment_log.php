<?php

class M_mailing_payment_log extends Crud_manager
{
    protected $_table = 'mailing_payment_log';
    public $schema = [
        'id' => [
            'field' => 'id',
            'label' => 'id',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'user_id' => [
            'field' => 'user_id',
            'label' => 'user_id',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'is_email_delivery' => [
            'field' => 'is_email_delivery',
            'label' => 'is_email_delivery',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'is_cash_payment' => [
            'field' => 'is_cash_payment',
            'label' => 'is_cash_payment',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'total_bill' => [
            'field' => 'total_bill',
            'label' => 'total_bill',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'transaction_type' => [
            'field' => 'transaction_type',
            'label' => 'transaction_type',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'created_time' => [
            'field' => 'created_time',
            'label' => 'created_time',
            'rules' => '',
            'form' => [
                'type' => 'datetime',
            ],
        ],
        'year' => [
            'field' => 'year',
            'label' => 'year',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ],
        'month' => [
            'field' => 'month',
            'label' => 'month',
            'rules' => '',
            'form' => [
                'type' => 'number',
            ],
        ]
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function create($data = []) {
        $fields = array_column($this->schema, 'field');

        $filteredData = array_filter(
            $data,
            function ($key) use ($fields) {
                return in_array($key, $fields);
            },
            ARRAY_FILTER_USE_KEY
        );

        if ($this->db->insert($this->_table, $filteredData)) {
            return true;
        }

        return false;
    }

    public function get_all($year, $month) {
        $this->db->select('u.id as user_id, u.email, u.company_name, u.first_name, u.last_name, mpl.is_cash_payment,mpl.total_bill, mpl.is_email_delivery, mpl.created_time');
        $this->db->from('mailing_payment_log mpl');
        $this->db->join('users u', 'u.id = mpl.user_id');
        $this->db->where('mpl.year', $year);
        $this->db->where('mpl.month', $month);
        $query = $this->db->get();
        return $query->result();
    }

    public function get_unique_column_data($column){
        $this->db->distinct("$column");
        $this->db->select("$column");
        $this->db->from($this->_table);
        return $this->db->get()->result();
    }

    public function get_unique_month_by_year($year){
        $this->db->distinct("month");
        $this->db->select("month");
        $this->db->from($this->_table);
        $this->db->where("year", $year);
        return $this->db->get()->result();
    }
}
