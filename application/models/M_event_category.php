<?php

class M_event_category extends Crud_manager {

    protected $_table = 'event_category';
    public $schema = [
        'cat_name'        => [
            'field' => 'cat_name',
            'label' => 'カテゴリー名',
            'rules' => 'required',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [],
        ],
        'cat_description' => [
            'field' => 'cat_description',
            'label' => 'カテゴリ説明',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [],
        ],
        'parent_id'       => [
            'field'    => 'parent_id',
            'label'    => 'カテゴリの父',
            'rules'    => '',
            'form'     => [
                'type' => 'text',
            ],
            'db_field' => 'p.cat_name',
            'table'    => [],
        ],
        'time_created'    => [
            'field' => 'time_created',
            'label' => 'time_created',
            'rules' => '',
        ],
        'user_created'    => [
            'field' => 'user_created',
            'label' => 'user_created',
            'rules' => '',
        ],
        'time_modified'   => [
            'field' => 'time_modified',
            'label' => 'time_modified',
            'rules' => '',
        ],
        'user_modified'   => [
            'field' => 'user_modified',
            'label' => 'user_modified',
            'rules' => '',
        ],
        'deleted'         => [
            'field' => 'deleted',
            'label' => 'deleted',
            'rules' => '',
        ],
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get['custom_select_table'] = "custom_select_table";
        $this->updateLabels();
    }

    public function updateLabels()
    {
        foreach ($this->schema as $key => $field) {
            if (isset($field['label'])) {
                $this->schema[$key]['label'] = my_lang($field['label']);
            }
        }
    }

    public function custom_select_table() {
        $this->db->select($this->_table_alias . ".*");
        $this->db->order_by($this->_table_alias . ".position", 'ASC');
        $this->db->order_by($this->_table_alias . ".id", 'ASC');
    }

    public function custom_select_api() {
        $this->db->select($this->_table_alias . ".id, " . $this->_table_alias . ".cat_name as name, " . $this->_table_alias . ".parent_id");
        $this->db->order_by($this->_table_alias . ".position", 'ASC');
        $this->db->order_by($this->_table_alias . ".id", 'ASC');
    }

    public function unset_before_get($key) {
        unset($this->before_get[$key]);
    }

    public function set_before_get($key) {
        $this->before_get[$key] = $key;
    }

    public function get_all_children($parent_id = 0) {
        $list = Array();
        $listHaveChild = Array();
        $user = $this->get_many_by(array('parent_id' => $parent_id));
        if (count($user)) {
            foreach ($user as $row) {
                $listHaveChild[] = $row->id;
                $list[] = $row->id;
            }
            $i = 0;
            while ($i < sizeof($listHaveChild)) {
                $user = $this->get_many_by(array('parent_id' => $listHaveChild[$i]));
                if (count($user)) {
                    foreach ($user as $row) {
                        $listHaveChild[] = $row->id;
                        $list[] = $row->id;
                    }
                }
                $i++;
            }
        }
        return $list;
    }
}