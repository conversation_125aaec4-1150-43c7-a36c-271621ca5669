<?php

/**
 * Created by IntelliJ IDEA.
 * User: locmx
 * Date: 27/07/16
 */
class M_event_subscriber_detail extends Crud_manager {

    protected $_table = 'event_subscriber_details';
    public $schema = [
        'subscriber_id' => [
            'field' => 'subscriber_id',
            'label' => 'subscriber_id',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'event_form_id' => [
            'field' => 'event_form_id',
            'label' => 'event_form_id',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'value'         => [
            'field' => 'value',
            'label' => 'value',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get['join_event_subscriber_forms_table'] = "join_event_subscriber_forms_table";
        $this->updateLabels();
    }

    public function updateLabels()
    {
        foreach ($this->schema as $key => $field) {
            if (isset($field['label'])) {
                $this->schema[$key]['label'] = my_lang($field['label']);
            }
        }
    }

    public function join_event_subscriber_forms_table() {
        $this->db->select($this->_table_alias . ".*, i.id as field_id, i.field_name,i.default_position, i.default_rules, i.more_data,i.type,i.is_custom_field,f.required, f.rules");
        $this->db->join("event_forms_data as f", $this->_table_alias . ".event_form_id=f.id");
        $this->db->join("event_form_fields as i", "f.field_id=i.id");
        $this->db->order_by("i.default_position", "ASC");
    }

    public function get_list_answer_survey($event_id, $form_id)
    {
        $this->db->select("es.email, es.first_name, " . $this->_table_alias . ".*, i.id as field_id, i.field_name,i.default_position, i.default_rules, i.more_data,i.type,i.is_custom_field,f.required, f.rules");
        $this->db->from("event_subscriber_details as " . $this->_table_alias);
        $this->db->join("event_forms_data as f", $this->_table_alias . ".event_form_id=f.id");
        $this->db->join("event_subscribers as es", $this->_table_alias . ".subscriber_id=es.id");
        $this->db->join("event_form_fields as i", "f.field_id=i.id");
        $this->db->where("es.event_id", $event_id);
        $this->db->where("f.form_id", $form_id);
        $this->db->where("m.deleted", 0);
        $this->db->order_by("m.id", "DESC");
        $this->db->order_by("i.default_position", "ASC");
        $query = $this->db->get();
        return $query->result();
    }
}
