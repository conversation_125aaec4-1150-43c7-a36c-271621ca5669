<?php

class M_admin_settlement_history extends Crud_manager {

    protected $_table = 'admin_settlement_history';
    public $schema = [
        'id' => [],
        'admin_settlement_id'   =>  [],
        'file_name'   =>  [],
        'time_created'  =>  [],
        'deleted'   =>  []
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get[] = "join_admin_settlement_table";
    }

    public function join_admin_settlement_table() {
        $this->db->select($this->_table_alias . ".*, as.year, as.month");
        $this->db->join("admin_settlement as as", $this->_table_alias . ".admin_settlement_id=as.id");
    }
}