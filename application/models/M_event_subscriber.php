<?php

/**
 * Created by IntelliJ IDEA.
 * User: locmx
 * Date: 15/07/16
 * Time: 15:33
 */
class M_event_subscriber extends Crud_manager {
    public $data_column_application = [];
    public $_mailing_type = 0;
    public $_get_mailing_subs = FALSE;
    public $_last_sent_no = 0;
    protected $_table = 'event_subscribers';
    public $schema = [
        'deleted'               => [
            'field' => 'deleted',
            'label' => '削除',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_delete_html",
                'class'                => 'disable_sort',
            ],
            'mark' => TRUE,
        ],
        'first_name'            => [
            'field' => 'first_name',
            'label' => '姓',
            'rules'  => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => TRUE,
        ],
        'last_name'             => [
            'field' => 'last_name',
            'label' => '名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => TRUE,
        ],
        'first_name_furi'       => [
            'field' => 'first_name_furi',
            'label' => '姓フリガナ',
            'rules' => 'callback_is_katakana',
            'form'  => [
                'type' => 'text',
            ],
            'table' => TRUE,
        ],
        'last_name_furi'        => [
            'field' => 'last_name_furi',
            'label' => '名フリガナ',
            'rules' => 'callback_is_katakana',
            'form'  => [
                'type' => 'text',
            ],
            'table' => TRUE,
        ],
        'gender'                => [
            'field' => 'gender',
            'label' => '性別',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_gender_text",
            ],
        ],
        'company_name'          => [
            'field' => 'company_name',
            'label' => '会社名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => TRUE,
        ],
        'company_name_furi'     => [
            'field' => 'company_name_furi',
            'label' => '会社名フリガナ',
            'rules' => 'callback_is_katakana',
            'form'  => [
                'type' => 'text',
            ],
            'table' => TRUE,
        ],
        'department_name'       => [
            'field' => 'department_name',
            'label' => '部署名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'class' => 'disable_sort',
            ],
        ],
        'school_name'           => [
            'field' => 'school_name',
            'label' => '学校名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'class' => 'disable_sort',
            ],
        ],
        'class_name'            => [
            'field' => 'class_name',
            'label' => 'クラス名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'class' => 'disable_sort',
            ],
        ],
        'phone'                 => [
            'field'  => 'phone',
            'label'  => '電話番号',
            'rules'  => 'callback_is_phone',
            'form'   => [
                'type' => 'number',
            ],
            'errors' => [
                'is_phone' => 'ハイフンは含みません。',
            ],
            'table'  => [
                'class' => 'disable_sort',
            ],
        ],
        'email'                 => [
            'field'  => 'email',
            'label'  => 'メールアドレス',
            'rules'  => 'valid_email',
            'form'   => [
                'type' => 'text',
            ],
            'errors' => [
                'check_exits_domain' => '入力したドメインは存在しません。<br>メールアドレスを再度ご確認ください。',
                'is_unique'          => 'こちらのメールアドレスはすでにお申込み済みです。<br>他のメールアドレスでお申込みください。',
            ],
            'table'  => [
                'class' => 'disable_sort',
            ],
        ],
        'people_count'          => [
            'field' => 'people_count',
            'label' => '同伴者数',
            'rules' => 'numeric|greater_than_equal_to[0]',
            'form'  => [
                'type' => 'number',
            ],
            'table' => [
                'class' => 'disable_sort',
            ],
        ],
        'item_name'            => [
            'field' => 'id as item_name', //temp_value
            'label' => '料金項目名',
            'form'  => [
                'type' => 'text',
            ],
            'table'  => [
                'class' => 'disable_sort',
            ],
        ],
        'total_bill'            => [
            'field' => 'total_bill',
            'label' => '合計金額',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
            ],
        ],
        'total_paid'            => [
            'field' => 'total_paid',
            'label' => '決済金額',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
            ],
        ],
        'total_ship'            => [
            'field' => 'total_ship',
            'label' => '送料',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
                'class' => "disable_sort"
            ],
        ],
        'total_tax'             => [
            'field' => 'total_tax',
            'label' => '消費税',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
            ],
        ],
        'total_sub_fee'         => [
            'field' => 'total_sub_fee',
            'label' => '小計金額',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
            ],
        ],
        'all_total_sub_fee'         => [
            'field' => 'all_total_sub_fee',
            'label' => '合計小計',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
            ],
        ],
        'transaction_type'      => [
            'field'  => 'transaction_type',
            'label'  => '決済方法',
            'rules'  => 'required',
            'form'   => [
                'type' => 'text',
            ],
            'errors' => [
                'required' => '支払い方法が選ばれていません。',
            ],
            'table'  => [
                'callback_render_data' => "get_transaction_type_text",
            ],
            'mark' => TRUE,
        ],
        'transaction_last_time' => [
            'field' => 'transaction_last_time',
            'label' => '決済完了日',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_transaction_time_html",
            ],
            'mark' => TRUE,
        ],
        'note'                  => [
            'field' => 'note',
            'label' => 'メモ',
            'rules' => '',

            'form' => [
                'type' => 'text',
            ],
            'mark' => TRUE,
        ],
        'note_time'             => [
            'field' => 'note_time',
            'label' => 'メモ',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'class'                => 'disable_sort',
                'callback_render_data' => "get_note_time_html",
            ],
        ],
        'canceled_time'         => [
            'field' => 'canceled_time',
            'label' => 'キャンセル',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_cancel_html",
            ],
            'mark' => TRUE,
        ],
        'status_email'          => [
            'field' => 'status_email',
            'label' => 'メール到達状況',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_status_email_html",
            ],
            'mark' => TRUE,
        ],
        'vip_time'              => [
            'field' => 'vip_time',
            'label' => 'VIP',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_vip_html",
            ],
            'mark' => TRUE,
        ],
        'created_time'          => [
            'field' => 'created_time',
            'label' => '申し込み日時',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "change_value_to_text",
            ],
        ],
        'edited_time'           => [
            'field' => 'edited_time',
            'label' => '編集',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_edit_html",
            ],
            'mark' => TRUE,
        ],
        'send_ticket_time'      => [
            'field' => 'send_ticket_time',
            'label' => '発券',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_send_ticket_html",
            ],
            'mark' => TRUE,
        ],
        'sent_ticket_log'       => [
            'field' => 'sent_ticket_log',
            'label' => 'sent_ticket_log',
            'rules' => '',
            'mark' => TRUE,
        ],
        'last_sent_no'          => [
            'field' => 'last_sent_no',
            'label' => 'last_sent_no',
            'rules' => '',
        ],
        'email_type'            => [
            'field' => 'email_type',
            'label' => '',
            'rules' => '',
        ],
        'scanner_number' => [
            'field' => 'scanner_number',
            'label' => 'スキャナー',
            'rules' => '',
            'form' => [
                'type' => 'text'
            ],
            'table' => [
                'callback_render_data' => "convert_scanner_number",
            ],
            'mark' => TRUE,
        ],
        'time_video' => [
            'field' => 'time_video',
            'label' => '動画視聴',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table'  => [
                'class' => 'disable_sort',
            ],
            'mark' => TRUE,
        ],
        'subscriber_image'       => [
            'field' => 'subscriber_image',
            'label' => '名札',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_view_subscriber_image_html",
            ],
            'mark' => TRUE,
        ],
        'is_answer_survey'         => [
            'field' => 'id as is_answer_survey',
            // 'field'  => 'is_answer_survey',
            'label' => 'アンケート',
            'form'  => [
                'type' => 'text',
            ],
            'table'  => [
                'class' => 'disable_sort',
            ],
            'mark' => TRUE,
        ],
        
        'checkin_log' => [
            'field' => 'checkin_log',
            'label' => '受付',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'mark' => TRUE,
        ],
        'event_id'              => [
            'field'  => 'event_id',
            'label'  => 'event_id',
            'rules'  => '',
            'filter' => [
                'type'        => 'text',
                'search_type' => 'where',
                'db_field'    => 'event_id',
            ],
        ],
        'no_ticket'             => [
            'field' => 'no_ticket',
            'label' => 'NO.',
            'rules' => '',
            'form'  => [
                'type' => 'number',
            ],
            'table' => [
                'callback_render_data' => 'render_no_ticket_data',
            ],
        ],
        'subno_ticket'         => [
            'field' => 'subno_ticket',
            'label' => 'subno_ticket',
            'rules' => '',
        ],
        'total_ticket'          => [
            'field' => 'total_ticket',
            'label' => '数量',
            'rules' => '',
            'table' => [
                // 'callback_render_data' => 'render_delegate_name',
                'class'                => 'disable_sort',
            ],
        ],
        'canceled'              => [
            'field' => 'canceled',
            'label' => 'canceled',
            'rules' => '',
        ],
        'checkin_people_count'  => [
            'field' => 'checkin_people_count',
            'label' => 'checkin_people_count',
            'rules' => '',
        ],
        'user_accept_id'        => [
            'field' => 'user_accept_id',
            'label' => 'user_accept_id',
            'rules' => '',
        ],
        'updated_time'          => [
            'field' => 'updated_time',
            'label' => 'updated_time',
            'rules' => '',
        ],
        'privacy_rule'          => [
            'field'  => 'privacy_rule',
            'label'  => 'privacy_rule',
            'rules'  => 'required|is_natural_no_zero',
            'errors' => [
                'required'           => '同意事項は必須となります。​',
                'is_natural_no_zero' => '同意事項は必須となります。​',
            ],
        ],
        'policy_rule'          => [
            'field'  => 'policy_rule',
            'label'  => 'policy_rule',
            'rules'  => 'required|is_natural_no_zero',
            'errors' => [
                'required'           => '同意事項は必須となります。​',
                'is_natural_no_zero' => '同意事項は必須となります。​',
            ],
        ],
        'delegate'              => [
            'field' => 'delegate',
            'label' => 'お申し込み代表者氏名',
            'rules' => '',
            'table' => [
                'callback_render_data' => 'render_delegate_name',
                'class'                => 'disable_sort',
            ],
        ],
        'checkin_time'          => [
            'field' => 'checkin_time',
            'label' => '受付',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "get_checkin_html",
            ],
            'mark' => TRUE,
        ],
        'slot_number'           => [
            'field' => 'slot_number',
            'label' => '座席番号',
            'rules' => '',
            'table' => [
                'callback_render_data' => 'get_view_slot_number_html',
                'class' => 'disable_sort',
            ],
            'mark' => TRUE,
        ],
        'is_winner'           => [
            'field' => 'is_winner',
            'label' => '当選/落選',
            'rules' => '',
            'table' => [
                'callback_render_data' => 'get_view_is_winner_html',
                'class' => 'disable_sort',
            ],
            'mark' => TRUE,
        ],
        'is_winner_handled'           => [
            'field' => 'is_winner_handled',
            'label' => 'is_winner_handled',
            'rules' => ''
        ],
        'cron_send_email_announce_win_lose'           => [
            'field' => 'cron_send_email_announce_win_lose',
            'label' => 'cron_send_email_announce_win_lose',
            'rules' => ''
        ],
        'first_send_ticket'           => [
            'field' => 'first_send_ticket',
            'label' => 'first_send_ticket',
            'rules' => ''
        ],
        'first_token_time'           => [
            'field' => 'first_token_time',
            'label' => 'first_token_time',
            'rules' => ''
        ],
        'token_number_count'           => [
            'field' => 'token_number_count',
            'label' => 'token_number_count',
            'rules' => ''
        ],
        'fee_ticket_issuance'     => [
            'field' => 'fee_ticket_issuance',
            'label' => '',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'consumption_tax'         => [
            'field' => 'consumption_tax',
            'label' => '',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'fee_transaction'             => [
            'field' => 'fee_transaction',
            'label' => '',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'total_system_fee'        => [
            'field' => 'total_system_fee',
            'label' => '発券料',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [
                'callback_render_data' => "format_number_money",
            ],
        ],
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get['custom_select_table'] = 'custom_select_table';
        $this->before_get['custom_format_select'] = 'custom_format_select';
        $this->before_get['before_get_ticket'] = 'before_get_ticket';
        $this->updateLabels();
    }

    public function updateLabels()
    {
        foreach ($this->schema as $key => $field) {
            if (isset($field['label'])) {
                $this->schema[$key]['label'] = my_lang($field['label']);
            }
        }
    }

    public function custom_select_table() {
        $this->db->select($this->_find_getter_fields());
        $this->db->select("e.name, e.user_id as event_user_id, e.status, e.draft, e.is_vip, e.vip_email_notify, e.type_checkin");
        $this->db->select("e.type_ticket");
        $this->db->select("e.is_force");
        $this->db->join("events as e", "e.id=" . $this->_table_alias . ".event_id");
    }

    /**
     * _find_getter_fields
     *
     * Safer version of $this->_table_alias . ".*"
     *
     * @return array
     */
    private function _find_getter_fields() {
        $extract_field_name = function ($field_content) {
            return $field_content['field'];
        };
        $prepend_table_alias = function ($field) {
            return $this->_table_alias . '.' . $field;
        };
        $is_not_formatted = function ($field) {
            $formatted_fields = array('vip_time', 'canceled_time', 'send_ticket_time', 'updated_time', 'edited_time');
            return !in_array($field, $formatted_fields);
        };

        $schema_fields = array_map($extract_field_name, $this->schema);
        $not_declared_fields = array(
            $this->primary_key,
            'checksum_code',
            'transaction_status',
            'company_address',
            'qr_id',
            'ticket_type',

        );
        $all_fields = array_merge($not_declared_fields, $schema_fields);
        $raw_getter_fields = array_filter($all_fields, $is_not_formatted);
        $getter_fields = array_map($prepend_table_alias, $raw_getter_fields);
        return $getter_fields;
    }

    public function custom_select_subs_of_event() {
        $last_sent_no = $this->_last_sent_no;
        $this->db->where("(last_sent_no != " . $last_sent_no . " OR ((email_type = 4 AND status_email = 0) OR (email_type != 4)))");
    }

    public function custom_format_select() {
        $this->db->select(" DATE_FORMAT(e.start_date, '%Y/%m/%d') AS start_date");
        $this->db->select(" DATE_FORMAT(e.end_date, '%Y/%m/%d') AS end_date");
        $this->db->select(" DATE_FORMAT(e.start_time, '%H:%i') AS start_time");
        $this->db->select(" DATE_FORMAT(e.opening_time, '%H:%i') AS opening_time");
        $this->db->select(" DATE_FORMAT(e.end_time, '%H:%i') AS end_time");
        $this->db->select(" DATE_FORMAT(e.start_subscribe, '%Y/%m/%d %H:%i') AS start_subscribe");
        $this->db->select(" DATE_FORMAT(e.end_subscribe, '%Y/%m/%d %H:%i') AS end_subscribe");
        $this->db->select(" DATE_FORMAT(e.start_checkin, '%Y/%m/%d %H:%i') AS start_checkin");
        $this->db->select(" DATE_FORMAT(e.end_checkin, '%Y/%m/%d %H:%i') AS end_checkin");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".vip_time, '%Y/%m/%d %H:%i') AS vip_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".canceled_time, '%Y/%m/%d %H:%i') AS canceled_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".send_ticket_time, '%Y/%m/%d %H:%i') AS send_ticket_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".updated_time, '%Y/%m/%d %H:%i') AS updated_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".edited_time, '%Y/%m/%d %H:%i') AS edited_time");
    }

    public function before_get_ticket() {
        $this->db->select("CONCAT('{',GROUP_CONCAT(CONCAT('\"',t.subscriber_ticket_no,'\":',t.checkin_log,'')),'}') AS ticket_checkin_log");
        $this->db->group_by($this->_table_alias . '.' . $this->primary_key);
        $this->db->join('event_subscriber_tickets as t', 't.subscriber_id=' . $this->_table_alias . '.' . $this->primary_key . ' AND t.deleted=0', 'LEFT');
    }

    // AnhLD - remove group by id;
    public function before_get_ticket_remove_group_by() {
        $this->db->select("CONCAT('{',GROUP_CONCAT(CONCAT('\"',t.subscriber_ticket_no,'\":',t.checkin_log,'')),'}') AS ticket_checkin_log");
        $this->db->join('event_subscriber_tickets as t', 't.subscriber_id=' . $this->_table_alias . '.' . $this->primary_key . ' AND t.deleted=0', 'LEFT');
    }

    public function unset_before_get($key) {
        unset($this->before_get[$key]);
    }

    public function set_before_get($key) {
        $this->before_get[$key] = $key;
    }

    public function custom_select_application() {
        $data_sub_form = $this->data_column_application;
        $this->db->select($this->_table_alias . ".*");
        $this->db->select("e.name, e.user_id as event_user_id, e.status, e.draft, e.type_checkin");
        $this->db->select("e.is_vip");
        $this->db->select("e.type_ticket");
        foreach ($data_sub_form as $item) {
            $this->db->select("MAX(CASE WHEN i.field_name = '" . $item->field_name . "' THEN d.value end) as " . $item->field_name);
        }
        $this->db->join("events as e", "e.id=" . $this->_table_alias . ".event_id");
        $this->db->join("event_subscriber_details as d", "d.subscriber_id=" . $this->_table_alias . ".id", "left");
        $this->db->join("event_forms_data as f", "d.event_form_id = f.id", "left");
        $this->db->join("event_form_fields as i", "i.id = f.field_id", "left");
        $this->db->group_by($this->_table_alias . ".id");
    }

    public function custom_select_application_list() {
        $data_sub_form = $this->data_column_application;
        $this->db->select("count(distinct m.id) as all_total_ticket");
        $this->db->select($this->_table_alias . ".*");
        $this->db->select("e.name, e.user_id as event_user_id, e.status, e.draft, e.type_checkin");
        $this->db->select("e.is_vip");
        $this->db->select("e.type_ticket");
        $this->db->select("subscriber_info.*");
        $this->db->select("SUM(m.total_ship) as total_ship");
        $this->db->select("m.total_sub_fee * count(distinct m.id) as total_sub_fee");
        $this->db->join("events as e", "e.id=" . $this->_table_alias . ".event_id");
        $this->db->join($this->get_subscriber_info(), "subscriber_info.subscriber_id = m.id");
        $this->db->join("event_subscribers_fee_detail as esf", "esf.subscriber_id = m.id");
        $this->db->join("event_fee_items_data as efid", "esf.fee_data_id = efid.id");
        $this->db->join("event_fee_items as efi", "efi.id = efid.fee_item_id");
        $this->db->where("esf.quantity > 0");
        $this->db->group_by($this->_table_alias . ".no_ticket");
        $this->db->group_by($this->_table_alias . ".subno_ticket");
        $this->db->group_by("efid.id");
    }

    public function get_subscriber_info() {
        $data_sub_form = $this->data_column_application;
        $subquery = "(SELECT es.id AS subscriber_id,";
        $listField = [];
        for ($i = 0; $i < count($data_sub_form); $i++) {
            if(in_array($data_sub_form[$i]->field_name,$listField)) continue;
            $subquery .= " MAX( CASE WHEN i.field_name = '" . $data_sub_form[$i]->field_name . "' THEN d.value END ) as " . $data_sub_form[$i]->field_name . ", ";
            $listField[] = $data_sub_form[$i]->field_name;
        }
        $subquery = rtrim($subquery, ', ').' ';
        $subquery .= "FROM
        `event_subscribers` as es
        LEFT JOIN `event_subscriber_details` as d on es.id = d.subscriber_id
        LEFT JOIN `event_forms_data` as `f` ON `d`.`event_form_id` = `f`.`id`
        LEFT JOIN `event_form_fields` as `i` ON `i`.`id` = `f`.`field_id`
        GROUP BY es.id) as subscriber_info ";
        return $subquery;
    }

    public function custom_select_mailing_table() {
        $this->db->select($this->_table_alias . ".*, m_d.status as status");
        $this->db->join("mailing_details as m_d", "m_d.subscriber_id =" . $this->_table_alias . ".id AND m_d.deleted = 0", 'left');
        $this->db->where($this->_table_alias . ".email IS NOT NULL");
        $this->db->where($this->_table_alias . ".email NOT IN ( SELECT email FROM mail_black_list WHERE deleted = 0 )");
        $this->db->where("(m_d.status != 2 or m_d.status is null)");
        if ($this->_mailing_type) {
            $this->db->group_by($this->_table_alias . ".id");
        } else {
            $this->db->group_by($this->_table_alias . ".email");
        }
    }

    public function custom_select_mailing_application() {
        $data_sub_form = $this->data_column_application;
        $this->db->select($this->_table_alias . ".*");
        $this->db->select("e.name, e.user_id as event_user_id, e.status as event_status, e.draft,m_d.status as status");
        $this->db->select("e.is_vip");
        $this->db->select("e.type_ticket");
        /*foreach ($data_sub_form as $item) {
            $field_name = is_array($item) ? $item['field'] : (isset($item->field_name) ? $item->field_name : $item);
            $this->db->select("MAX(CASE WHEN i.field_name = '" . $field_name . "' THEN d.value ELSE m." . $field_name . " end) as " . $field_name);
        }*/
        $this->db->join("events as e", "e.id=" . $this->_table_alias . ".event_id");
        $this->db->join("event_subscriber_details as d", "d.subscriber_id=" . $this->_table_alias . ".id", "left");
        $this->db->join("event_forms_data as f", "d.event_form_id = f.id", "left");
        $this->db->join("event_form_fields as i", "i.id = f.field_id", "left");
        $this->db->join("mailing_details as m_d", "m_d.subscriber_id =" . $this->_table_alias . ".id AND m_d.deleted = 0", 'left');
        $this->db->where($this->_table_alias . ".email IS NOT NULL");
        $this->db->where($this->_table_alias . ".email NOT IN ( SELECT email FROM mail_black_list WHERE deleted = 0 )");
        $this->db->where("(m_d.status != 2 or m_d.status is null)");
        if ($this->_mailing_type) {
            $this->db->group_by($this->_table_alias . ".id");
        } else {
            $this->db->group_by($this->_table_alias . ".email");
        }
    }

    public function custom_select_booking_mailing_table() {
        $this->db->select($this->_table_alias . ".*");
        $this->db->join("mailing_subscriber_history as msh", "msh.subscriber_id =" . $this->_table_alias . ".id", 'left');
        $this->db->where($this->_table_alias . ".email IS NOT NULL");
        $this->db->where($this->_table_alias . ".email NOT IN ( SELECT email FROM mail_black_list WHERE deleted = 0 )");
        if ($this->_mailing_type) {
            $this->db->group_by($this->_table_alias . ".id");
        } else {
            $this->db->group_by($this->_table_alias . ".email");
        }
    }

    public function get_no_ticket($event_id) {
        $this->db->select("MAX(no_ticket)as max_ticket");
        $this->db->from("event_subscribers as m");
        $this->db->where("event_id =" . $this->db->escape($event_id));
        return $this->db->get()->row();
    }

    public function is_delegate($sub_id) {
        return $this->get_list_filter_count(['delegate' => $sub_id], [], []);
    }

    // check subscriber có phải người đại diện hay không (th có một mình hay là th có cả người đồng hành)
    public function is_delegate_custom($sub_id) {
        $this->db->select("es.id");
        $this->db->from("event_subscribers as es");
        $this->db->where("es.id = " . $this->db->escape($sub_id));
        $this->db->where("es.subno_ticket = 1 ");
        $this->db->where("es.deleted = 0");

        if (empty($this->db->get()->row())) return false;
        return true;
    }

    public function get_total_all_ticket_by_id($sub_id) {
        $this->db->select("SUM(total_ticket) as all_ticket, SUM(total_bill) as all_bill");
        $this->db->from("event_subscribers as m");
        $this->db->where("m.delegate =" . $this->db->escape($sub_id));
        $this->db->or_where("m.id =" . $this->db->escape($sub_id));
        $this->db->where("m.deleted = 0");
        return $this->db->get()->row();
    }

    public function get_total_ticket_event_by_id($event_id) {
        $this->db->select("COUNT(id) as all_ticket");
        $this->db->from("event_subscribers as m");
        $this->db->where("m.event_id =" . $this->db->escape($event_id));
        $this->db->where("m.deleted = 0");
        return $this->db->get()->row();
    }

    public function custom_select_booking_mailing_application() {
        $data_sub_form = $this->data_column_application;
        $this->db->select($this->_table_alias . ".id as subs_id, msh.*");
        foreach ($data_sub_form as $item) {
            $field_name = isset($item->field_name) ? $item->field_name : $item;
            $this->db->select("MAX(CASE WHEN i.field_name = '" . $field_name . "' THEN d.value ELSE m." . $field_name . " end) as " . $field_name);
            if ($field_name == 'transaction_last_time') {
                $this->db->select("MAX(CASE WHEN i.field_name = 'transaction_status' THEN d.value ELSE m.transaction_status end) transaction_status");
            }
        }
        $this->db->join("mailing_subscriber_history as msh", "msh.subscriber_id=" . $this->_table_alias . ".id");
        $this->db->join("event_subscriber_details as d", "d.subscriber_id=" . $this->_table_alias . ".id", "left");
        $this->db->join("event_forms_data as f", "d.event_form_id = f.id", "left");
        $this->db->join("event_form_fields as i", "i.id = f.field_id", "left");
        if ($this->_mailing_type) {
            $this->db->group_by($this->_table_alias . ".id");
        } else {
            $this->db->group_by($this->_table_alias . ".email");
        }
    }

    public function custom_select_api() {
        $data_sub_form = $this->data_column_application;
        $this->db->select($this->_table_alias . ".id as application_id");
        $this->db->select($this->_table_alias . ".checksum_code as application_checksum");
        $this->db->select($this->_table_alias . ".deleted as deleted");
        $this->db->select($this->_table_alias . ".vip_time as vip_time");
        $this->db->select($this->_table_alias . ".checkin_time as checkin_time");
        $this->db->select("CONCAT({$this->_table_alias}.no_ticket, '-', {$this->_table_alias}.subno_ticket) as no_ticket");

        $this->db->select("e.is_vip");
        $this->db->select("e.type_ticket");
        $this->db->select("e.type_checkin");
        $this->db->select("CONCAT(" . $this->_table_alias . ".first_name,' ', " . $this->_table_alias . ".last_name) as application_name");
        foreach ($data_sub_form as $item) {
            $this->db->select("MAX(CASE WHEN i.field_name = '" . $item->field_name . "' THEN d.value end) as " . $item->field_name);
        }
        $this->db->join("events as e", "e.id=" . $this->_table_alias . ".event_id");
        $this->db->join("event_subscriber_details as d", "d.subscriber_id=" . $this->_table_alias . ".id", "left");
        $this->db->join("event_forms_data as f", "d.event_form_id = f.id", "left");
        $this->db->join("event_form_fields as i", "i.id = f.field_id", "left");
        $this->db->group_by($this->_table_alias . ".id");
    }

    public function validate($data, $validate = NULL, $filter_schema = TRUE) {
        if (!$validate) {
            $schema = $this->schema;
        } else {
            $schema = $validate;
        }
        foreach ($data as $key => $item) {
            if (isset($schema[$key]['form']['type']) && $schema[$key]['form']['type'] == 'number') {
                $data[$key] = mb_convert_kana($data[$key], 'n');
            }
        }
        if ($filter_schema) {
            foreach ($data as $key => $item) {
                if (!isset($schema[$key]))
                    unset($data[$key]);
            }
        }
        return parent::validate($data, $validate);
    }

    public function count_subscribers($start_time, $end_time, $event_id) {
        $this->db->select('each_day, (CS.people_go + CS.people_go_with) as total');
        $this->db->from(
            '(SELECT DATE_FORMAT(created_time, "%Y-%m-%d") as each_day, COUNT(id) as people_go, SUM(IFNULL(people_count, 0)) AS people_go_with ' .
            ' FROM event_subscribers' .
            ' WHERE event_id =' . $this->db->escape($event_id) .
            ' AND created_time >= ' . $this->db->escape($start_time) .
            ' AND created_time < ' . $this->db->escape($end_time) .
            ' GROUP BY DATE_FORMAT(created_time, "%Y-%m-%d") ) AS CS'
        );

        return $this->db->get()->result();
    }

    // get event subscriber khi tạo ra booking mailing
    public function get_subscribers($whereInCondition) {
        $this->db->select($this->_table_alias . ".email, MAX(m.id) as id, m_d.status as status");
        $this->db->from("event_subscribers AS m");
        $this->db->join("mailing_details as m_d", "m_d.subscriber_id =" . $this->_table_alias . ".id AND m_d.deleted = 0", 'left');
        $this->db->where($this->_table_alias . ".deleted = 0");
        $this->db->where($this->_table_alias . ".email IS NOT NULL");
        $this->db->where($this->_table_alias . ".email NOT IN ( SELECT email FROM mail_black_list WHERE deleted = 0 )");
        if (is_array($whereInCondition) && count($whereInCondition) > 0) {
            foreach ($whereInCondition as $key => $value) {
                if ((is_array($value) && count($value)) ||
                (is_string($value) && strlen($value))
                ) {
                    $this->db->where_in($key, $value);
                }
            }
        }
        $this->db->group_by($this->_table_alias . ".email");
        return $this->db->get()->result();
    }
    
    /**
     * $sub_id là id của người đại diện muốn lấy dữ liệu của người đồng hành
     */
    public function get_children_subscriber($sub_id){
        $this->db->select("id")
        ->from($this->_table)
        ->where('delegate', $sub_id)
        ->or_where('id', $sub_id)
        ->order_by('id', 'DESC');
        $result = $this->db->get()->result();
        if (empty($result)) return FALSE;
        return $result;
    }

       /**
     * $sub_id là id của người đại diện muốn lấy dữ liệu của người đồng hành
     */
    public function get_children_subscriber_custom($sub_id){
        $where = "(event_subscribers.delegate = '" . $sub_id . "' OR event_subscribers.id = '" . $sub_id . "')";
        $this->db->select("event_subscribers.id,event_subscribers.email")
        ->from($this->_table)
        ->where($where);
        $this->db->where("event_subscribers_fee.total_cost > 0");
        $this->db->where("event_subscribers_fee.deleted = 0");
        $this->db->where("event_fee_items.deleted = 0");
        $this->db->select("event_fee_items.fee_item_type");
        $this->db->join("event_subscribers_fee", "event_subscribers_fee.subscriber_id=" . $this->_table . ".id");
        $this->db->join("event_fee_items", "event_fee_items.id=event_subscribers_fee.fee_item_id");
        $this->db->order_by($this->_table . '.id', 'DESC');
        $result = $this->db->get()->result();
        if (empty($result)) return FALSE;
        return $result;
    }

    public function set_data_column_application($data) {
        $this->data_column_application = $data;
    }

    public function set_data_last_sent_no($no) {
        $this->_last_sent_no = $no;
    }

    public function unset_all_before_get() {
        $this->before_get = [];
    }

    public function set_soft_delete($value = TRUE) {
        $this->soft_delete = $value;
    }

    public function set_mailing_type($value = 0) {
        $this->_mailing_type = $value;
    }

    public function get_gender_text($id) {
        $genders = [
            'male'   => my_lang('男性'),
            'female' => my_lang('女性'),
        ];
        if (isset($genders[$id])) {
            return $genders[$id];
        } else {
            return $id;
        }
    }

    public function get_status_email_text($id) {
        if ($id === null) {
            return '';
        }
        $status_email = [
            '0' => my_lang('未到達'),
            '1' => my_lang('到達'),
            '2' => my_lang('レスポンス待機中'),
        ];
        if (isset($status_email[$id])) {
            return $status_email[$id];
        } else {
            return $id;
        }
    }

    public function get_transaction_type() {
        //cmt if hidden combini transaction
        return [
            'CREDIT_CARD'  => my_lang('クレジットカード'),
            'COMBINI'      => my_lang('コンビ二'),
            'BANKING_CARD' => my_lang('銀行振込'),
            'CASHIER'      => my_lang('現金'),
        ];
    }

    public function get_transaction_type_text($id, $transaction_type = null, $record = null) {
        $types = $this->get_transaction_type();
        if(!empty($record->delegate)) {
            return "";
        }
        if (isset($types[$id])) {
            return $types[$id];
        } else {
            return my_lang('未設定');
        }
    }

    public function get_transaction_status_text($id) {
        $transaction_status = [
            '0' => my_lang('未決済'),
            '1' => my_lang('決済済み'),
            '2' => my_lang('取り消し'),
            '3' => my_lang('不足払い'),
            '4' => my_lang('超過払い'),
        ];
        if (isset($transaction_status[$id])) {
            return $transaction_status[$id];
        } else {
            return $id;
        }
    }

    public function get_combini() {
        return [
            '00001' => my_lang('ローソン'),
            '00002' => my_lang('ファミリーマート'),
            '00005' => my_lang('ミニストップ'),
        ];
    }

    public function get_mail_type() {
        return [
            '1' => 'application_success',
            '2' => 'update_information',
            '3' => 'sent_ticket',
            '4' => 'sent_all_ticket',
        ];
    }

    public function get_mailing_fee_field() {
        return [
            '1' => 'total_bill',
            '2' => 'total_paid',
            '3' => 'transaction_type',
            '4' => 'transaction_last_time',
        ];
    }

    public function get_list_combini_img() {
        return [
            '00001' => 'assets/manager/images/lawson.png',
            '00002' => 'assets/manager/images/family-mart.png',
            '00005' => 'assets/manager/images/mini-stop.png',
        ];
    }

    public function get_combini_text($id) {
        $types = $this->get_transaction_type();
        if (isset($types[$id])) {
            return $types[$id];
        } else {
            return '';
        }
    }

    public function standard_filter_data($post_filter) {
        $result = parent::standard_filter_data($post_filter);
        $session = $this->session->userdata('tmp_filter_subscribers');
        if ($session && count($session) && $this->_get_mailing_subs) {
            if ($this->_mailing_type == 1) {
                $result['where_in']['m.id NOT'] = $session;
            } else {
                $result['where_in']['email NOT'] = $session;
            }
        }
        return $result;
    }

    public function set_mailing_custom($value = FALSE) {
        $this->_get_mailing_subs = $value;
    }

    public function set_schema_mailing_select($value) {
        if (!$value) {
            return FALSE;
        }
        $this->schema['mailing_id'] = [
            'field'    => 'mailing_id',
            'db_field' => 'm_d.mailing_id',
            'label'    => 'mailing ID',
            'rules'    => '',
            'filter'   => [
                'type'        => 'text',
                'search_type' => 'where',
            ],
        ];
    }

    public function set_schema_booking_mailing_select($value) {
        if (!$value) {
            return FALSE;
        }
        $this->schema['booking_id'] = [
            'field'    => 'booking_id',
            'db_field' => 'msh.booking_id',
            'label'    => 'booking ID',
            'rules'    => '',
            'filter'   => [
                'type'        => 'text',
                'search_type' => 'where',
            ],
        ];
    }

    public function set_schema_id_subscribers($value) {
        if (!$value) {
            return FALSE;
        }
        $this->schema['id'] = [
            'field'    => 'id',
            'db_field' => 'id',
            'label'    => 'Subscribers ID',
            'rules'    => '',
            'filter'   => [
                'type'        => 'text',
                'search_type' => 'where_in',
            ],
        ];
    }

    public function get_schema_street_address($rules) {
        $schema = Array(
            Array(
                'field' => 'zipcode',
                'label' => my_lang('郵便番号'),
                'rules' => 'integer' . (empty($rules) ? '' : '|' . $rules),
            ),
            Array(
                'field' => 'address1',
                'label' => my_lang('都道府県'),
                'rules' => $rules,
            ),
            Array(
                'field' => 'address2',
                'label' => my_lang('区市町村'),
                'rules' => $rules,
            ),
            Array(
                'field' => 'address3',
                'label' => my_lang('番地以降'),
                'rules' => $rules,
            ),
        );

        return $schema;
    }

    public function get_schema_total_ticket() {
        $schema = [
            [
                'field' => 'all_total_ticket',
                'label' => my_lang('数量'),
                'rules' => '',
                'table' => [
                    // 'callback_render_data' => 'render_delegate_name',
                    'class'                => 'disable_sort',
                ],
            ],
        ];

        return $schema;
    }

    public function get_friend_schema($event_id) {
        $friend_fields = [
            'first_name',
            'last_name',
            'first_name_furi',
            'last_name_furi',
            'email',
        ];

        $friend_schema = [];

        foreach ($friend_fields as $field) {
            $friend_schema[$field] = $this->schema[$field];
            if (empty($friend_schema[$field]['rules'])) {
                $friend_schema[$field]['rules'] = 'required';
            } else {
                $friend_schema[$field]['rules'] = 'required|' . $friend_schema[$field]['rules'];
            }
        }

        return $friend_schema;
    }

    public function json_address2text($json_address) {
        $address = json_decode($json_address);
        if (empty($address->zipcode)) {
            return '';
        }
        $text = sprintf("〒%s-%s %s %s %s",
            substr($address->zipcode, 0, 3),
            substr($address->zipcode, 3),
            $address->address1,
            $address->address2,
            $address->address3);
        return $text;
    }

    public function get_many($values) {
        //TODO Move this function to Crud_model
        $this->_database->where_in($this->_table_alias . "." . $this->primary_key, $values);
        return $this->get_all();
    }

    public function get_event_subscriber($id){
        $this->db->select('id, first_name, email, first_name_furi, phone');
        $this->db->from($this->_table);
        $this->db->where('id', $id);
        $result = $this->db->get()->row();
        return $result;
    }

    public function update_status_email($id){
        // status_email = 1 (到達)
        $this->db->where('delegate', $id);
        $this->db->update('event_subscribers', ['status_email' => '1']);
    }

    public function list_event_send_mail($event_id, $list_transaction){
        $this->db->select();
        $this->db->from($this->_table);
        $this->db->where('event_id', $event_id);
        $this->db->where('send_ticket_time is null');
        $this->db->where_in('transaction_status', $list_transaction);
        $this->db->where('deleted', 0);
        $result = $this->db->get()->result();
        return $result;
    }

    public function checkEventSubscriberExists($event_id) {
        $this->db->where('event_id', $event_id);
        $query = $this->db->get($this->_table);

        return $query->num_rows() > 0;
    }

    public function total_application()
    {
        $this->db->from('event_subscribers m');
        $this->db->join('events e', 'e.id = m.event_id');
        $this->db->where('m.deleted', 0);
        $this->db->where('e.deleted', 0);

        $this->db->select('COUNT(*) AS numrows');

        $query = $this->db->get();
        $result = $query->row();
        return $result ? $result->numrows : 0;
    }
}
