<?php

/**
 * Created by IntelliJ IDEA.
 * User: locmx
 * Date: 05/07/16
 * Time: 15:33
 */
class M_event extends Crud_manager {

    public $_keyword = '';
    public $_keyword_2 = '';
    protected $_table = 'events';
    public $schema = [
        'id'                       => [
            'field'  => 'id',
            'label'  => 'イベントID',
            'rules'  => '',
            'filter' => [
                'type'        => 'text',
                'search_type' => 'where',
                'db_field'    => 'id',
            ],
            'table' => [],
        ],
        'name'                     => [
            'field'  => 'name',
            'label'  => 'イベント名',
            'rules'  => 'required|regex_match[/^[一-龥ぁ-んァ-ンa-zA-Z0-9!@#$%*()\/_\[\]\-:,.?+=！＠＃＄％＊（）＿「」ー：、。？＋＝\s]+$/]|max_length[64]|callback_check_name_event',
            'form'   => [
                'type' => 'text',
            ],
            'filter' => [
                'search_type' => 'like',
                'attr'        => 'data-test="filter"',
            ],
            'errors' => [
                'check_name_event' => 'イベント名欄は正しい形式ではありません。',
            ],
            'table'  => [],
        ],
        'start_date'               => [
            'field'  => 'start_date',
            'label'  => '開催日',
            'rules'  => 'required|callback_check_time_today_event',
            'errors' => [
                'check_time_today_event' => '過去の開催日は設定できません。',
            ],
            'form'   => [
                'type' => 'text',
            ],
            'table'  => [],
        ],
        'total_held_days'          => [
            'field' => 'total_held_days',
            'label' => '開催日数',
            //'rules' => 'required|greater_than[0]',
            'form'  => [
                'type' => 'number',
            ],
        ],
        'address1'                 => [
            'field' => 'address1',
            'label' => '開催場所名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [],
        ],
        'total_sub'                => [
            'field'    => 'total_sub',
            'label'    => '予約者数',
            'rules'    => '',
            'form'     => [
                'type' => 'text',
            ],
            'table'    => [
                'class' => '',
            ],
            'db_field' => 'total_sub',
        ],
        'total_ticket_release'     => [
            'field' => 'total_ticket_release',
            'label' => '発券数',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
            'table' => [],
            'db_field' => 'total_ticket_release',
        ],
        'total_checkin'            => [
            'field'    => 'total_checkin',
            'label'    => '受付数',
            'rules'    => '',
            'form'     => [
                'type' => 'text',
            ],
            'table'    => [
                'class' => '',
            ],
            'db_field' => 'total_checkin',
        ],
        'totalcancel'             => [
            'field'    => 'total_cancel',
            'label'    => 'キャンセル数',
            'rules'    => '',
            'form'     => [
                'type' => 'text',
            ],
            'table'    => [
                'class' => '',
            ],
            'db_field' => 'total_cancel',
        ],
        'payment_count'            => [
            'field'    => 'payment_count',
            'label'    => '決済完了数',
            'rules'    => '',
            'form'     => [
                'type' => 'text',
            ],
            'table'    => [
                'class' => '',
            ],
            'db_field' => 'payment_count',
        ],
        'status'                   => [
            'field'  => 'status',
            'label'  => 'イベントステータス',
            'form'   => [
                'type'            => 'select',
                'target_model'    => 'this',
                'target_function' => 'get_status',
                'class'           => '',
            ],
            'filter' => [
                'type' => 'multiple_select',
            ],
            'table'  => [
                'callback_render_data' => "get_status_html",
                'class'                => "hidden-480",
            ],
        ],
        'target'                   => [
            'field' => 'target',
            'label' => '対象',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'start_time'               => [
            'field' => 'start_time',
            'label' => '開場時間',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'opening_time'             => [
            'field'  => 'opening_time',
            'label'  => '開演時間',
            'rules'  => 'callback_check_start_time_event[start_time]',
            'errors' => [
                'check_start_time_event' => '開演時間は、開場時間以後しか選択できなくしてください。',
            ],
            'form'   => [
                'type' => 'text',
            ],
        ],
        'end_date'                 => [
            'field' => 'end_date',
            'label' => '閉会日',
            'rules' => 'required|callback_check_end_date[start_date]',
            'form'  => [
                'type' => 'text',
            ],
            'errors' => [
                'check_end_date' => '終了日は開始日より前にすることはできません',
            ]
        ],
        'end_time'                 => [
            'field' => 'end_time',
            'label' => '閉会時間',
            'rules' => 'callback_check_end_time',
            'form'  => [
                'type' => 'text',
            ],
            'errors' => [
                'check_end_time' => '終了時刻は開始時刻より前にすることはできません。',
            ]
        ],
        'address2'                 => [
            'field' => 'address2',
            'label' => '開催場所住所',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'subcrisebers_expected'    => [
            'field' => 'subcrisebers_expected',
            'label' => '参加予定人数',
            'rules' => 'integer',
            'form'  => [
                'type' => 'number',
            ],
        ],
        'company_name'             => [
            'field' => 'company_name',
            'label' => '主催者名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'sponsorship_name'         => [
            'field' => 'sponsorship_name',
            'label' => '後援・協賛名',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'url'                      => [
            'field' => 'url',
            'label' => 'イベントURL',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'phone'                    => [
            'field' => 'phone',
            'label' => 'お問い合わせ電話番号',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'start_subscribe'          => [
            'field'  => 'start_subscribe',
            'rules'  => 'callback_check_time_tomorrow_event',
            'errors' => [
                'check_subscribe_time_event_required' => '予約開始日時は必須となります。',
                'check_time_tomorrow_event'           => '予約開始日は明日以降で、過去は選択できなくしてください。',
            ],
            'label'  => '予約受付開始日時',
            'form'   => [
                'type' => 'text',
            ],
        ],
        'end_subscribe'            => [
            'field'  => 'end_subscribe',
            'rules'  => 'callback_check_time_tomorrow_event|callback_check_subscribe_time_event[start_subscribe]',
            'errors' => [
                'check_time_tomorrow_event'           => '予約終了日は明日以降で、過去は選択できなくしてください。',
                'check_subscribe_time_event_required' => '予約終了日時は必須となります。',
                'check_subscribe_time_event'          => '予約受付終了日時は、予約受付開始日以降としてください。',
            ],
            'label'  => '予約受付終了日時',
            'form'   => [
                'type' => 'text',
            ],
        ],
        'end_payment'            => [
            'field'  => 'end_payment',
            // 'rules'  => 'callback_check_time_tomorrow_event',
            'rules'  => 'callback_check_time_tomorrow_event|callback_check_payment_time_event[start_subscribe]',
            'errors' => [
                'check_time_tomorrow_event'           => '本申し込み日は明日以降で、過去は選択できなくしてください。',
                'check_payment_time_event_required' => '本申し込み日時は必須となります。',
                'check_payment_time_event'          => '本申し込み終了日時は、予約開始日時以降となります。',
            ],
            'label'  => '予約受付終了日時',
            'form'   => [
                'type' => 'text',
            ],
        ],
        'start_checkin'            => [
            'field'  => 'start_checkin',
            'rules'  => 'callback_check_time_tomorrow_event',
            'errors' => [
                'check_time_tomorrow_event' => '受付アプリ起動日は明日以降で、過去は選択できなくしてください。',
            ],
            'label'  => '受付アプリ起動日時',
            'form'   => [
                'type' => 'text',
            ],
        ],
        'end_checkin'              => [
            'field'  => 'end_checkin',
            'rules'  => 'callback_check_time_tomorrow_event|callback_check_checkin_time_event[start_checkin]',
            'errors' => [
                'check_time_tomorrow_event' => '受付アプリ終了日は明日以降で、過去は選択できなくしてください。',
                'check_checkin_time_event'  => '予約受付終了日時は、予約受付開始日以降としてください。',
            ],
            'label'  => '受付アプリ終了日時',
            'form'   => [
                'type' => 'text',
            ],
        ],
        'is_sent_mail'             => [
            'field' => 'is_sent_mail',
            'label' => 'is_sent_mail',
        ],
        'no_sent_mail'             => [
            'field' => 'no_sent_mail',
            'label' => 'no_sent_mail',
        ],
        'created_time'             => [
            'field' => 'created_time',
            'label' => 'created_time',
            'rules' => '',
        ],
        'updated_time'             => [
            'field' => 'updated_time',
            'label' => 'updated_time',
            'rules' => '',
        ],
        'type_ticket'              => [
            'field' => 'type_ticket',
            'label' => '発券方法',
            'rules' => '',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'list_email'               => [
            'field' => 'list_email',
            'label' => 'list_email',
            'rules' => '',
            'form'  => FALSE,
        ],
        'total_held_hours'         => [
            'field' => 'total_held_hours',
            'label' => 'total_held_hours',
            'rules' => '',
        ],
        'total_held_minutes_bonus' => [
            'field' => 'total_held_minutes_bonus',
            'label' => 'total_held_minutes_bonus',
            'rules' => '',
        ],
        'is_vip'                   => [
            'field' => 'is_vip',
            'label' => 'is_vip',
            'rules' => '',
        ],
        'is_update_link'                   => [
            'field' => 'is_update_link',
            'label' => 'is_update_link',
            'rules' => '',
        ],
        'is_stock'                 => [
            'field' => 'is_stock',
            'label' => 'is_stock',
            'rules' => '',
        ],
        'is_stock_msg'             => [
            'field' => 'is_stock_msg',
            'label' => 'is_stock_msg',
            'rules' => '',
        ],
        'stock_msg'                => [
            'field'  => 'stock_msg',
            'label'  => 'stock_msg',
            'rules'  => '',
            'errors' => [
                'check_is_stock_false' => 'データ型が一致しません。',
            ],
            'form'   => [
                'type' => 'text',
            ],
        ],
        'user_id'                  => [
            'field' => 'user_id',
            'label' => 'user_id',
            'rules' => '',
        ],
        'cat_id'                   => [
            'field' => 'cat_id',
            'label' => 'カテゴリ',
            'rules' => 'required',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'draft'                    => [
            'field' => 'draft',
            'label' => 'draft',
            'rules' => '',
        ],
        'e_ticket_mail'            => [
            'field' => 'e_ticket_mail',
            'label' => 'e_ticket_mail',
            'rules' => '',
        ],
        'title_e_ticket_mail'      => [
            'field' => 'title_e_ticket_mail',
            'label' => 'title_e_ticket_mail',
            'rules' => '',
        ],
        'announce_lose_mail'            => [
            'field' => 'announce_lose_mail',
            'label' => 'announce_lose_mail',
            'rules' => '',
        ],
        'title_announce_lose_mail'      => [
            'field' => 'title_announce_lose_mail',
            'label' => 'title_announce_lose_mail',
            'rules' => '',
        ],
        'payment_success_mail'            => [
            'field' => 'payment_success_mail',
            'label' => 'payment_success_mail',
            'rules' => '',
        ],
        'title_payment_success_mail'      => [
            'field' => 'title_payment_success_mail',
            'label' => 'title_payment_success_mail',
            'rules' => '',
        ],
        'announce_win_mail'            => [
            'field' => 'announce_win_mail',
            'label' => 'announce_win_mail',
            'rules' => '',
        ],
        'title_announce_win_mail'      => [
            'field' => 'title_announce_win_mail',
            'label' => 'title_announce_win_mail',
            'rules' => '',
        ],
        'register_success_mail'            => [
            'field' => 'register_success_mail',
            'label' => 'register_success_mail',
            'rules' => '',
        ],
        'settings'            => [
            'field' => 'settings',
            'label' => 'settings',
            'rules' => '',
        ],
        'vip_email_notify'         => [
            'field' => 'vip_email_notify',
            'label' => 'vip_email_notify',
            'rules' => '',
        ],
        'show_ticket_display'      => [
            'field' => 'show_ticket_display',
            'label' => 'show_ticket_display',
            'rules' => '',
        ],
//        'id'                       => [
//            'field'  => 'id',
//            'label'  => 'id',
//            'rules'  => '',
//            'filter' => [
//                'type'        => 'text',
//                'search_type' => 'where',
//                'db_field'    => 'id',
//            ],
//        ],
        'is_force'                 => [
            'field'  => 'is_force',
            'label'  => 'is_force',
            'rules'  => '',
        ],
          'is_scan_number'         => [
            'field'  => 'is_scan_number',
            'label'  => 'is_scan_number',
            'rules'  => '',
        ],
          'type_checkin'           => [
            'field'  => 'type_checkin',
            'label'  => 'type_checkin',
            'rules'  => '',
        ],
        'url_video' => [
            'field'  => 'url_video',
            'label'  => 'url_video',
            'rules'  => '',

            'form'   => [
                'type' => 'text',
            ],
        ],

        'session_user' => [
            'field'  => 'session_user',
            'label'  => 'session_user',
            'rules'  => '',
            'form'   => [
                'type' => 'text',
            ],
        ],

        'event_id_copy'                 => [
            'field'  => 'event_id_copy',
            'label'  => 'event_id_copy',
            'rules'  => '',
        ],

        'is_childcare_education'    => [
            'field'  => 'is_childcare_education',
            'label'  => 'is_childcare_education',
            'rules'  => '',
        ],
        'lang'  =>  [
            'field' =>  'lang',
            'label' =>  '言語',
            'rules' =>  'required',
            'form'  => [
                'type' => 'text',
            ],
        ]
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get['custom_select_table'] = "custom_select_table";
        $this->before_get['custom_select_count_subscriber'] = "custom_select_count_subscriber";
        $this->before_get['custom_format_select'] = "custom_format_select";
        $this->before_get['custom_select_for_user'] = "custom_select_for_user";
        $this->updateLabels();
    }
    public function updateLabels()
    {
        foreach ($this->schema as $key => $field) {
            if (isset($field['label'])) {
                $this->schema[$key]['label'] = my_lang($field['label']);
            }
        }
    }

    public function custom_select_table() {
        $this->db->select($this->_table_alias . ".*, cat.cat_name,cat.parent_id");
        $this->db->join("event_category as cat", "cat.id = " . $this->_table_alias . ".cat_id", "left");
    }

    public function api_custom_select_table() {
        $this->db->select($this->_table_alias . ".id");
		$replace_select = "REPLACE({$this->_table_alias}.name, '<br/>', '')";
        $this->db->select("SUBSTR({$replace_select}, 1, 20) as name");
        $this->db->select($this->_table_alias . ".start_date");
        $this->db->select($this->_table_alias . ".end_date");
        $this->db->select($this->_table_alias . ".created_time");
        $this->db->select($this->_table_alias . ".created_time");
        $this->db->select($this->_table_alias . ".updated_time");
        $this->db->select($this->_table_alias . ".start_subscribe");
        $this->db->select($this->_table_alias . ".end_subscribe");
        $this->db->select($this->_table_alias . ".end_payment");
        $this->db->select($this->_table_alias . ".start_checkin");
        $this->db->select($this->_table_alias . ".end_checkin");
        $this->db->select($this->_table_alias . ".status");
        $this->db->select($this->_table_alias . ".is_stock");
        $this->db->select($this->_table_alias . ".stock_msg");
        $this->db->select($this->_table_alias . ".type_checkin");
        $this->db->select($this->_table_alias . ".is_scan_number");
    }

    public function custom_select_count_subscriber() {
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.deleted = 0) as total_sub");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.is_winner = 1 AND sub.is_winner_handled = 1 AND sub.first_send_ticket = 1 AND sub.deleted = 0) as total_ticket_release");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.user_accept_id > 0 AND sub.deleted = 0) as total_checkin");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.canceled > 0 AND sub.deleted = 0) as total_cancel");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.is_winner = 1 AND sub.is_winner_handled = 1 AND sub.first_send_ticket = 1 AND sub.canceled > 0 AND sub.deleted = 0) as total_ticket_cancel");

        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'male' AND sub.deleted = 0) as total_male");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'female' AND sub.deleted = 0) as total_female");

        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'male' AND sub.is_winner = 1 AND sub.is_winner_handled = 1 AND sub.first_send_ticket = 1 AND sub.deleted = 0) as total_male_release");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'female' AND sub.is_winner = 1 AND sub.is_winner_handled = 1 AND sub.first_send_ticket = 1 AND sub.deleted = 0) as total_female_release");

        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'male' AND sub.canceled > 0 AND sub.deleted = 0) as total_male_cancel");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'male' AND sub.user_accept_id > 0 AND sub.deleted = 0) as total_male_checkin");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'female' AND sub.canceled > 0 AND sub.deleted = 0) as total_female_cancel");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.gender = 'female' AND sub.user_accept_id > 0 AND sub.deleted = 0) as total_female_checkin");
        $this->db->select("(SELECT COUNT(sub.id) FROM event_subscribers as sub WHERE sub.event_id = m.id AND sub.transaction_status = 1 AND sub.deleted = 0) as payment_count");
    }

    public function custom_format_select() {
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_date, '%Y/%m/%d') AS start_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_date, '%Y/%m/%d') AS end_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_time, '%H:%i') AS start_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_time, '%H') AS start_time_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_time, '%i') AS start_time_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".opening_time, '%H:%i') AS opening_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".opening_time, '%H') AS opening_time_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".opening_time, '%i') AS opening_time_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_time, '%H:%i') AS end_time");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_time, '%H') AS end_time_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_time, '%i') AS end_time_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_subscribe, '%Y/%m/%d %H:%i') AS start_subscribe");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_subscribe, '%Y/%m/%d') AS start_subscribe_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_subscribe, '%H') AS start_subscribe_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_subscribe, '%i') AS start_subscribe_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_subscribe, '%Y/%m/%d %H:%i') AS end_subscribe");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_subscribe, '%Y/%m/%d') AS end_subscribe_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_subscribe, '%H') AS end_subscribe_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_subscribe, '%i') AS end_subscribe_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_payment, '%Y/%m/%d %H:%i') AS end_payment");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_payment, '%Y/%m/%d') AS end_payment_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_payment, '%H') AS end_payment_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_payment, '%i') AS end_payment_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_checkin, '%Y/%m/%d %H:%i') AS start_checkin");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_checkin, '%Y/%m/%d') AS start_checkin_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_checkin, '%H') AS start_checkin_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".start_checkin, '%i') AS start_checkin_minute");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_checkin, '%Y/%m/%d %H:%i') AS end_checkin");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_checkin, '%Y/%m/%d') AS end_checkin_date");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_checkin, '%H') AS end_checkin_hour");
        $this->db->select(" DATE_FORMAT(" . $this->_table_alias . ".end_checkin, '%i') AS end_checkin_minute");
    }

    function custom_search_events() {
        $keyword = $this->session->userdata('keyword_search');
        $keyword_2 = $this->session->userdata('keyword_search_2');
        $keyword = ($keyword) ? $keyword : $this->_keyword;
        $keyword_2 = ($keyword_2) ? $keyword_2 : $this->_keyword_2;
        if ($keyword) {
            $this->db->select("sub.id as application_id, sub.checksum_code as application_checksum, sub.first_name as application_name, sub.company_name as sub_company_name, sub.checkin_time, CONCAT(sub.no_ticket, '-', sub.subno_ticket) as no_ticket, sub.total_ticket");
            $this->db->join("event_subscribers as sub", $this->_table_alias . ".id = sub.event_id");
            $this->db->group_start();
            $this->db->group_start();
            $this->db->or_like("CONCAT(IFNULL(sub.first_name, ''), IFNULL(sub.last_name, ''))", $keyword);
            $this->db->or_like("CONCAT(IFNULL(sub.last_name, ''), IFNULL(sub.first_name, ''))", $keyword);
            $this->db->or_like("CONCAT_WS(' ', IFNULL(sub.first_name, ''), IFNULL(sub.last_name, ''))", $keyword);
            $this->db->or_like("CONCAT_WS(' ', IFNULL(sub.last_name, ''), IFNULL(sub.first_name, ''))", $keyword);
            $this->db->or_like("CONCAT(IFNULL(sub.first_name_furi, ''), IFNULL(sub.last_name_furi, ''))", $keyword);
            $this->db->or_like("CONCAT(IFNULL(sub.last_name_furi, ''), IFNULL(sub.first_name_furi, ''))", $keyword);
            $this->db->or_like("CONCAT_WS(' ', IFNULL(sub.first_name_furi, ''), IFNULL(sub.last_name_furi, ''))", $keyword);
            $this->db->or_like("CONCAT_WS(' ', IFNULL(sub.last_name_furi, ''), IFNULL(sub.first_name_furi, ''))", $keyword);
            $this->db->or_like("sub.company_name", $keyword);
            $this->db->group_end();
            if($keyword_2 != "") {
                $this->db->or_group_start();
                $this->db->or_where("CONCAT(IFNULL(sub.first_name, ''), IFNULL(sub.last_name, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT(IFNULL(sub.last_name, ''), IFNULL(sub.first_name, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT_WS(' ', IFNULL(sub.first_name, ''), IFNULL(sub.last_name, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT_WS(' ', IFNULL(sub.last_name, ''), IFNULL(sub.first_name, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT(IFNULL(sub.first_name_furi, ''), IFNULL(sub.last_name_furi, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT(IFNULL(sub.last_name_furi, ''), IFNULL(sub.first_name_furi, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT_WS(' ', IFNULL(sub.first_name_furi, ''), IFNULL(sub.last_name_furi, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("CONCAT_WS(' ', IFNULL(sub.last_name_furi, ''), IFNULL(sub.first_name_furi, '')) REGEXP", ".*" . $keyword_2 . ".*");
                $this->db->or_where("sub.company_name REGEXP ", ".*" . $keyword_2 . ".*");
                $this->db->group_end();
            }
            $this->db->group_end();
            $this->db->group_by("sub.no_ticket");
            $this->db->group_by("sub.subno_ticket");
        }
    }

    public function custom_where_with_role() {
        $role = $this->session->userdata("role"); //ogrinazer, admin, employee
        $child = [];//TODO
        $child[] = $this->session->userdata("user_id");
        if ($role == 'employee') {
            $this->db->where($this->_table_alias . ".user_id", $this->session->userdata("user_id"));
        } elseif ($role == 'ogrinazer') {
            $this->db->where_in($this->_table_alias . ".user_id", $child);
        } elseif ($role == 'admin') {

        }
    }

    /**
     * Get event for user created
     */
    public function custom_select_for_user() {
        $current_user = $this->session->userdata('user_id');
        if ($current_user) {
            $current_parent_path = $this->session->userdata('current_parent_path');
            $parent_path = $current_parent_path . $current_user . '-';
            $this->db->join("users as u", $this->_table_alias . ".user_id = u.id");
            //Get list child
            $this->db->where("(u.parent_path LIKE " . $this->db->escape("%" . $parent_path . "%") . " OR " . $this->_table_alias . ".user_id = " . $this->db->escape($current_user) . ")");
        }
    }

    public function custom_select_change_info_event() {
        $this->db->select("sub.id, CONCAT(sub.no_ticket, '-', sub.total_ticket) as no_ticket, sub.total_ticket");
        $this->db->join("event_subscribers as sub", $this->_table_alias . ".id = sub.event_id");
        $this->db->order_by('sub.updated_time', 'DESC');
    }

    public function set_fee_form_type() {
        $this->db->select("(CASE WHEN `ef`.`deleted` = 0 THEN COUNT(`ef`.`id`) ELSE 0 end) as is_fee_form, ef.is_fee as is_fee");
        $this->db->join("event_fee as ef", "ef.event_id = " . $this->_table_alias . ".id", "left");
    }

    public function check_friend_form_type($event_id) {
        $this->db->select("eff.field_name");
        $this->db->from("event_forms_data as m");
        $this->db->join("event_form_fields as eff", "eff.id = m.field_id", "left");
        $this->db->where("m.event_id = " . $this->db->escape($event_id));
        $this->db->where("(eff.field_name = 'friend_ticket_with_email' OR eff.field_name = 'friend_ticket')");
        $result = $this->db->get()->row();
        if (!empty($result)) {
            return isset($result->field_name) ? $result->field_name : '';
        }
        return FALSE;
    }

    public function custom_list_name_event() {
        $this->db->select($this->_table_alias . ".id as event_id, " . $this->_table_alias . ".name");
    }

    public function custom_list_event_fee() {
        $this->db->select($this->_table_alias . ".id ," . $this->_table_alias . ".name, ef.is_fee as is_fee");
        $this->db->join("event_fee as ef", $this->_table_alias . ".id = ef.event_id");
    }

    public function get_event_fee($id) {
        $this->db->select($this->_table_alias . ".id ," . $this->_table_alias . ".name, ef.is_fee as is_fee");
        $this->db->from($this->_table . ' as ' . $this->_table_alias);
        $this->db->join("event_fee as ef", $this->_table_alias . ".id = ef.event_id");
        $this->db->where([$this->_table_alias.'.id' => $id]);
        $this->db->where(['ef.deleted' => 0]);
        return $this->db->get()->row();
    }

    public function get_status() {
        return [
            'in_preparation'  => my_lang('準備中'),
            'accepting'       => my_lang('予約受付中'),
            'accept_finished' => my_lang('予約受付終了'),
            'accept_out_stock'=> my_lang('在庫切れ'),
            'event_stoped'    => my_lang('イベント中止'),
            'event_finished'  => my_lang('イベント終了'),
        ];
    }

    public function get_status_text($id) {
        $status = $this->get_status();
        if (isset($status[$id])) {
            return $status[$id];
        } else {
            return $id;
        }
    }

    public function get_is_vip() {
        return [
            '1' => my_lang('有り'),
            '0' => my_lang('無し'),
        ];
    }

    public function get_is_vip_text($id) {
        $vips = $this->get_is_vip();
        if (isset($vips[$id])) {
            return $vips[$id];
        } else {
            return $id;
        }
    }

    public function get_list_step_event_create() {
        $steps = [
            '1' => '<div class="d-flex align-items-center justify-content-center fw-bold number-step">1</div>'.my_lang('イベント情報登録'),
            '2' => '<div class="d-flex align-items-center justify-content-center fw-bold number-step">2</div>'.my_lang('予約フォーム設定'),
            '3' => '<div class="d-flex align-items-center justify-content-center fw-bold number-step">3</div>'.my_lang('電子チケット設定'),
            '4' => '<div class="d-flex align-items-center justify-content-center fw-bold number-step">4</div>'.my_lang('送信メール設定'),
            // '5' => '05.予約フォーム設置',
            // '5' => '05.イベント予約期間',
            // '6' => '06.受付アプリ起動期間',
        ];
        return $steps;
    }

    public function get_list_step_link_event_create() {
        $step_link = [
            '1' => '',
            '2' => 'manager/event/application_form/',
            '3' => 'manager/event/ticket_form/',
            '4' => 'manager/event/overview/',
            // '5' => 'manager/event/embedded_code/',
            // '5' => 'manager/event/setting_time_subscriber/',
            // '6' => 'manager/event/setting_time_checkin/',
        ];
        return $step_link;
    }

    public function get_list_held_hours() {
        $list_held_hours = Array();
        for ($i = 1; $i <= 32; $i++) {
            if ($i < 25) {
                $list_held_hours[$i] = $i;
            } else {
                $list_held_hours[$i] = $i . '( 翌朝 ' . ($i - 24) . ' 時)';
            }
        }
        return $list_held_hours;
    }

    public function get_list_held_hours_text($number) {
        $list_held_hours = $this->get_list_held_hours();
        if (isset($list_held_hours[$number])) {
            return $list_held_hours[$number];
        } else {
            return $list_held_hours;
        }
    }

    public function get_type_ticket() {
        return [
            '1' => my_lang('自動発券'),
            '2' => my_lang('手動発券'),
            '0' => my_lang('発券無し<span style="color: red">（有料イベントには発券が必要になるために選択できません）</span>'),
        ];
    }

    public function get_type_ticket_description() {
        return [
            '1' => my_lang('自動発券<span class="fw-normal text-black text-14-16 text-start white-space-normal ms-2">（予約申し込み完了時又は決済完了時に自動送信）<br class="d-none d-xl-block">クレジットカード、コンビニ、銀行振り込み、現金決済に対応。</span>'),
            '2' => my_lang('手動発券<span class="fw-normal text-black text-14-16 text-start white-space-normal ms-2">（お申込者一覧画面からの手動送信）<br class="d-none d-xl-block">クレジットカード、コンビニ、銀行振り込み決済に対応。</span>'),
            '0' => my_lang('発券無し<span class="fw-normal text-14-16 text-start white-space-normal ms-2" style="color: red">（有料イベントには発券が必要になるために選択できません）</span>'),
        ];
    }

    public function get_type_ticket_text($id) {
        $type_tickets = $this->get_type_ticket();
        if (isset($type_tickets[$id])) {
            return $type_tickets[$id];
        } else {
            return $id;
        }
    }

    public function get_status_html($origin_column_value, $column_name, &$record, $column_data, $caller) {
        $primary_key = $this->model->get_primary_key();
        $updated_record = $this->event_lib->update_status_event($record->$primary_key, $record);

        $status = $this->model->get_status_text($updated_record->$column_name);
        return $status;
    }

    public function validate($data, $validate = NULL, $filter_schema = TRUE) {
        $schema = $this->schema;
        foreach ($data as $key => $item) {
            if (isset($schema[$key]['form']['type']) && $schema[$key]['form']['type'] == 'number') {
                $data[$key] = mb_convert_kana($data[$key], 'n');
            };
        }
        if ($filter_schema) {
            foreach ($data as $key => $item) {
                if (!isset($schema[$key]))
                    unset($data[$key]);
            }
        }
        return parent::validate($data, $validate);
    }

    public function unset_all_before_get() {
        $this->before_get = [];
    }

    public function unset_before_get($string) {
        if (trim($string))
            unset($this->before_get[$string]);
    }

    public function set_before_get($string) {
        if (trim($string))
            $this->before_get[$string] = $string;
    }

    public function set_schema($data) {
        $this->schema = $data;
    }

    public function stop_event($id) {
        $data = Array();
        $data['status'] = 'event_stoped';
        $result = $this->update($id, $data, TRUE);
        return $result;
    }

    public function get_schema_list_email() {
        return [
            'email'         => [
                'field'  => 'email',
                'label'  => my_lang('メールアドレス'),
                'rules'  => 'valid_email',
                'form'   => [
                    'type' => 'text',
                ],
                'errors' => [
                    'valid_email' => my_lang('メールアドレスが一致いたしません。'),
                ],
            ],
            'confirm_email' => [
                'field'  => 'confirm_email',
                'label'  => my_lang('再確認用メールアドレス'),
                'rules'  => 'valid_email|matches[email]',
                'form'   => [
                    'type' => 'text',
                ],
                'errors' => [
                    'valid_email' => my_lang('メールアドレスが一致いたしません。'),
                ],
            ],
        ];
    }

    public function get_template_show_ticket($event_id = 0) {
        $template = [
            'no_ticket'    => [
                'label_name'   => 'No.',
                'ticket_label' => 'No.',
                'style'        => 'padding: 5px 0 0 0;text-align: center;color: #000000;',
            ],
            'ticket_title' => [
                'label_name'   => '目受付にご提示ください。',
                'ticket_label' => '目受付にご提示ください。',
                'style'        => 'padding: 12px 0;text-align: center;color: #c22d2d;',
            ],
            'qr_code'      => [
                'label_name'   => '＊受付にご提示ください。',
                'ticket_label' => '＊受付にご提示ください。',
                'style'        => 'height: 133px;width: 133px;background-color: #ffffff;margin: 0 auto;padding: 6.5px;',
            ],
            'fields'       => [
                'name'                  => [
                    'label_name'   => 'イベント名',
                    'ticket_label' => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_1',
                    'style'        => 'font-weight: bold;padding-top: 22px;padding-bottom: 15px;text-align: center;',
                    'checked'      => 'checked',
                    'hidden'       => FALSE,
                ],
                'target'                => [
                    'label_name'   => '対象',
                    'ticket_label' => '対象',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'start_date'            => [
                    'label_name'   => '開催日',
                    'ticket_label' => '開催日',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'checked'      => 'checked',
                    'hidden'       => FALSE,
                ],
//                'start_time'            => [
//                    'label_name'   => '開場時間',
//                    'ticket_label' => '開場時間',
//                    'ticket_group' => 'group_2',
//                    'style'        => '',
//                    'hidden'       => FALSE,
//                ],
                'company_name'          => [
                    'label_name'   => '会社名',
                    'ticket_label' => '会社名',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => FALSE,
                ],
                'opening_time'          => [
                    'label_name'   => '開演時間',
                    'ticket_label' => '開演時間',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => FALSE,
                ],
                'end_date'              => [
                    'label_name'   => '閉会日',
                    'ticket_label' => '閉会日',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => FALSE,
                ],
                'end_time'              => [
                    'label_name'   => '閉会時間',
                    'ticket_label' => '閉会時間',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => FALSE,
                ],
                'address1'              => [
                    'label_name'   => '開催場所名',
                    'ticket_label' => '開催場所名',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'checked'      => 'checked',
                    'hidden'       => FALSE,
                ],
                'address2'              => [
                    'label_name'   => '開催場所住所',
                    'ticket_label' => '開催場所住所',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'subcrisebers_expected' => [
                    'label_name'   => '参加予定人数',
                    'ticket_label' => '参加予定人数',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_2',
                    'style'        => '',
                    'hidden'       => FALSE,
                ],
				'description'            => [
                    'label_name'   => 'チケット特記事項',
                    'ticket_label' => 'チケット特記事項',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_3',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],

                'total_bill'            => [
                    'label_name'   => '料金項目名',
                    'ticket_label' => '料金項目名・単価',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_3',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'payment_method'        => [
                    'label_name'   => '決済方法',
                    'ticket_label' => '決済方法',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_3',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'payment_date'          => [
                    'label_name'   => '決済完了日時',
                    'ticket_label' => '決済完了日時',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_3',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
//                'company_name'          => [
//                    'label_name'   => '主催者名',
//                    'ticket_label' => '主催者名',
//                    'label_class'  => 'checkbox-label-ticket',
//                    'ticket_group' => 'group_4',
//                    'style'        => '',
//                    'hidden'       => TRUE,
//                ],
                'phone'                 => [
                    'label_name'   => 'お問い合わせ電話番号',
                    'ticket_label' => 'お問い合わせTEL',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'sponsorship_name'      => [
                    'label_name'   => '後援・協賛名',
                    'ticket_label' => '後援・協賛名',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'url'                   => [
                    'label_name'   => 'イベントURL',
                    'ticket_label' => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'slot_number'                   => [
                    'label_name'   => '座席番号',
                    'ticket_label' => '座席番号',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'hidden'       => TRUE,
                ],
                'sms1'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms1_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => FALSE,
                ],
                'sms2'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms2_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => FALSE,
                ],
                'sms3'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms3_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms4'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms4_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms5'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms5_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms6'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms6_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms7'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms7_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms8'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms8_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms9'                  => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms9_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
                'sms10'                 => [
                    'label_name'   => '自由記入',
                    'ticket_label' => '',
                    'value'        => '',
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_4',
                    'style'        => '',
                    'custom_html'  => '<input name="sms10_content" type="text" value="" class="shadow_input short-input">',
                    'hidden'       => TRUE,
                ],
            ],

        ];

        $type_ticket = $this->get_type_ticket_by_id($event_id);
        if ($type_ticket == 1) {
            unset($template['fields']['slot_number']);
        }

        // get the form field
        $this->load->model('m_event_form_data');
        $results = $this->m_event_form_data->get_event_form_fields($event_id);
        $results = array_reverse($results);
        if (count($results) > 0) {
            // if has result
            foreach ($results as $result) {
                $more_data = json_decode($result->more_data);
                if (!$more_data) continue;
                $tmp = [
                    "label_name"   => my_lang($more_data->field_display_name),
                    "ticket_label" => my_lang($more_data->field_display_name),
                    'label_class'  => 'checkbox-label-ticket',
                    'ticket_group' => 'group_3',
                    'style'        => '',
                    'hidden'       => TRUE,
                ];
                if ($result->field_name == 'first_name' || $result->field_name == 'last_name') {
                    $tmp['label_name'] = my_lang("氏名");
                    $tmp['ticket_label'] = my_lang("氏名");
                    $template['fields']['subscriber_name'] = $tmp;
                } else if ($result->field_name == 'first_name_furi' ||
                    $result->field_name == 'last_name_furi'
                ) {
                    $tmp['label_name'] = my_lang("フリガナ");
                    $tmp['ticket_label'] = my_lang("フリガナ");
                    $template['fields']["subscriber_furigana"] = $tmp;
                } else if ($result->field_name == 'phone') {
                    $tmp['label_name'] = my_lang("お申込者電話番号");
                    $tmp['ticket_label'] = my_lang("お申込者電話番号");
                    $template['fields']['subscriber_phone'] = $tmp;
                } else if ($result->field_name == 'shipping_phone') {
                    $tmp['label_name'] = my_lang("送付先電話番号");
                    $tmp['ticket_label'] = my_lang("送付先電話番号");
                    $template['fields'][$result->field_name] = $tmp;
                } else {
                    $template['fields'][$result->field_name] = $tmp;
                }
            }
        }


        return $template;
    }

    public function get_type_ticket_by_id($event_id) {
        $this->db->select("type_ticket");
        $this->db->from($this->_table);
        $this->db->where('id', $event_id);
        $result = $this->db->get()->result();
        if (empty($result)) return FALSE;
        return (int) $result[0]->type_ticket;
    }

    public function get_event($id) {
        $this->db->select("");
        $this->db->from($this->_table);
        $this->db->where('id', $id);
        return $this->db->get()->row();
    }

    public function get_events($userId) {
        $this->db->select('m.id, m.name');
        $this->db->from('events AS m');
        $this->db->join('users AS u', 'm.user_id = u.id');
        $this->db->where('(u.parent_path LIKE "%' . $userId . '-%" OR m.user_id = "' . $userId . '")');
        $this->db->where('m.deleted', 0);
        $this->db->order_by('m.id', 'DESC');

        $query = $this->db->get();
        return $query->result_array();
    }

    public function get_lang($lang) {
        $list_lang = [
            'ja'  => '日本語',
            'en'  => 'English'
        ];
        return $list_lang[$lang];
    }

    public function search_utf_convert () {
        $this->db->select('id, name');
        $this->db->from($this->_table);
        $this->db->where('deleted', 0);
        $this->db->where('user_id', $this->session->userdata('user_id'));
        $query = $this->db->get();
        return $query->result_array();
    }
}
