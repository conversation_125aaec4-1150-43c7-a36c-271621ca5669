<?php

class M_event_fee_combini extends Crud_manager {

    protected $_table = 'event_fee_combini';
    public $schema = [
        'event_id'        => [
            'field' => 'event_id',
            'label' => 'event_id',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'fee_id'          => [
            'field' => 'fee_id',
            'label' => 'fee_id',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'reception_place' => [
            'field' => 'reception_place',
            'label' => '送付先住所',
            'rules' => 'required',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'reception_phone' => [
            'field' => 'reception_phone',
            'label' => '送付先電話',
            'rules' => 'required|callback_is_phone',
            'form'  => [
                'type' => 'text',
            ],
            'errors' => [
                'is_phone' => 'ハイフンは含みません。',
            ]
        ],
        'time_updated'    => [
            'field' => 'time_updated',
            'label' => 'time_updated',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'user_created'    => [
            'field' => 'user_created',
            'label' => 'user_created',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'time_modified'   => [
            'field' => 'time_modified',
            'label' => 'time_modified',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'user_modified'   => [
            'field' => 'user_modified',
            'label' => 'user_modified',
            'form'  => [
                'type' => 'text',
            ],
        ],
        'deleted'         => [
            'field' => 'deleted',
            'label' => 'deleted',
            'form'  => [
                'type' => 'text',
            ],
        ],
    ];

    public function __construct() {
        parent::__construct();
        $this->before_get['custom_select_table'] = "custom_select_table";
        $this->updateLabels();
    }

    public function updateLabels()
    {
        foreach ($this->schema as $key => $field) {
            if (isset($field['label'])) {
                $this->schema[$key]['label'] = my_lang($field['label']);
            }
        }
    }

    public function custom_select_table() {
        $this->db->select($this->_table_alias . ".*");
    }

    public function validate($data, $validate = NULL) {
        $schema = $this->schema;
        foreach ($data as $key => $item) {
            if (!isset($schema[$key]))
                unset($data[$key]);
        }
        return parent::validate($data, $validate);
    }
}