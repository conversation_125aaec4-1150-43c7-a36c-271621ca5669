<?php

/**
 * Created by PhpStorm.
 * User: a
 * Date: 8/11/16
 * Time: 17:12
 */

/**
 * Class Application_test
 * @property Application
 */
class Application_test extends TestCase {

    /**
     * @dataProvider data_application_list_success
     * @param array $data
     * @param array $expect
     */
    public function test_application_list_success($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_call_application_list();
        // Create controller
        $application_controller = $this->getMockBuilder('application')
            ->setMethods(array('_load_step_nav', 'manager'))
            ->getMock();
        $application_controller->method('_load_step_nav')
            ->with(5, $data_init['id'])
            ->willReturn('_load_step_nav_result');

        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_application_list($CI, $data_init);
        // Verify
        // Make data
        $data_verify = ARRAY();
        $data_verify['total_subscriber'] = $expect['total_subscriber'];
        $data_verify['total_subscriber_male'] = $expect['total_subscriber_male'];
        $data_verify['total_subscriber_female'] = $expect['total_subscriber_female'];
        $data_verify['total_subscriber_canceled'] = $expect['total_subscriber_canceled'];
        $data_verify['event_info'] = (object)$expect['event_info'];

        $data_verify['step_nav'] = $expect['_load_step_nav_result'];
        $data_verify["ajax_data_link"] = site_url($application_controller->name["class"] . "/ajax_list_data") . '?event_id=' . $expect['id'];
        $data_verify["view_file"] = $application_controller->path_theme_view . "application/manager_container";

        $this->verifyInvoked($application_controller, 'manager', [$data_verify]);
        // Call controller method
        $application_controller->application_list($data_init['id']);
    }

    public function data_application_list_success() {
        return ARRAY(
            'With have event id' => [
                [
                    '__data_init__' => [
                        'id' => 1,
                        'total_subscriber' => 10,
                        'total_subscriber_male' => 3,
                        'total_subscriber_female' => 4,
                        'total_subscriber_canceled' => 3,
                        'm_event.get' => [
                            "id" => "1",
                            "name" => "Event",
                            "status" => "accepting"
                        ],
                        'm_event.get_status_text' => "accepting",
                        '_load_step_nav_result' => '_load_step_nav_result',
                        'status_text' => '準備中',
                    ]
                ],
                [
                    'id' => 1,
                    'total_subscriber' => 10,
                    'total_subscriber_male' => 3,
                    'total_subscriber_female' => 4,
                    'total_subscriber_canceled' => 3,
                    '_load_step_nav_result' => '_load_step_nav_result',
                    'status_text' => '準備中',
                    'event_info' => [
                        "id" => "1",
                        "name" => "Event",
                        "status" => "accepting"
                    ]
                ]
            ]
        );
    }

    private function _prepare_constructor_call_application_list() {
        $session = $this->getMockBuilder('CI_Session')
            ->disableOriginalConstructor()
            ->getMock();
        $session->method("userdata")->withConsecutive(
            ['identity'],
            ['user_id']
        )->willReturnOnConsecutiveCalls(
            ['phpunit@unit'],
            [10]
        );

        // Inject mock object
        load_class_instance('session', $session);
    }

    private function _prepare_function_call_application_list($CI, $data_init) {
        $CI->m_event = $this->getMockBuilder('M_event')
            ->disableOriginalConstructor()
            ->getMock();
        if ($data_init['m_event.get']) {
            $CI->m_event->method('get')->with($data_init['id'])->willReturn(
                (object)$data_init['m_event.get']
            );
            $CI->m_event->method('get_status_text')->with($data_init['m_event.get']['status'])->willReturn($data_init['m_event.get']['status']);
        } else {
            $CI->m_event->method('get')->with($data_init['id'])->willReturn(
                NULL
            );
        }

        $CI->model = $this->getMockBuilder('M_event_subscriber')
            ->setMethods(array('get_list_filter_count'))
            ->getMock();
        $CI->model->method('get_list_filter_count')->willReturnOnConsecutiveCalls(
            [['m.event_id' => $data_init['id']], [], []],
            [['m.event_id' => $data_init['id'], 'm.gender' => 'male'], [], []],
            [['m.event_id' => $data_init['id'], 'm.gender' => 'female'], [], []],
            [['m.event_id' => $data_init['id'], 'm.canceled' => '1'], [], []]
        )->willReturnOnConsecutiveCalls(
            $data_init['total_subscriber'],
            $data_init['total_subscriber_male'],
            $data_init['total_subscriber_female'],
            $data_init['total_subscriber_canceled']
        );
    }

    /**
     * @dataProvider data_application_list_fail
     * @param array $data
     * @param array $expect
     */
    public function test_application_list_fail($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_call_application_list();
        // Create controller
        $application_controller = $this->getMockBuilder('application')
            ->setMethods(array('set_data_part', 'show_page'))
            ->getMock();
        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_application_list($CI, $data_init);
        // Verify
        // Make data
        $this->verifyInvoked($application_controller, 'show_page', [$expect['error_message']]);
        // Call controller method
        $application_controller->application_list($data_init['id']);
    }

    public function data_application_list_fail() {
        return ARRAY(
            'With haven\'t event id' => [
                [
                    '__data_init__' => [
                        'id' => 1,
                        'total_subscriber' => 10,
                        'total_subscriber_male' => 3,
                        'total_subscriber_female' => 4,
                        'total_subscriber_canceled' => 3,
                        'm_event.get' => NULL,
                        'm_event.get_status_text' => "accepting",
                        '_load_step_nav_result' => '_load_step_nav_result',
                        'status_text' => '準備中',
                    ]
                ],
                [
                    'error_message' => "<div style='text-align: center;padding: 50px;color: red'>イベントは存在しません。</div>"
                ]
            ]
        );
    }

    /**
     * @dataProvider data_ajax_list_data
     * @param $data
     * @param $expect
     */
    public function test_ajax_list_data($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_call_ajax_list_data($data_init);
        // Create controller
        $controller = $this->_make_controller_ajax_list_data($data_init);

        $CI =& get_instance();
        $CI->load = $this->getMockBuilder('CI_Loader')
            ->setMethods(array('view'))
            ->getMock();
        $data_input = $expect['CI_Loader']['view_argument']['data'];
        if (isset($data_input['view_file'])) {
            $view_file = $data_input['view_file'];
        } else {
            $view_file = $controller->path_theme_view . "base_manager/table_data";
        }

        $CI->load->method('view')
            ->with($view_file, $data_input, TRUE)
            ->willReturn('Content_html');
        $this->verifyInvoked($CI->load, 'view', [$view_file, $data_input, TRUE]);
        if ($data_init['MY_Input']['is_ajax_request_return']) {
            $this->expectOutputString($expect['Application']['ajax_list_data_json_output']);
        } else {
            $this->verifyInvoked($controller, 'show_page', ['Content_html']);
        }

        // Run callable
        $this->_prepare_function_call_ajax_list_data($CI, $data_init);
        // Verify
        // Make data
        //********//

        $controller->ajax_list_data($data_init['Application']['ajax_list_data_argument']);
    }

    public function data_ajax_list_data() {
        /**
         * output:
         *      $this->model->unset_before_get('custom_select_table');
         *      $this->model->set_before_get('custom_select_application');
         *      return:
         *          1. json
         *          2. HTML
         *
         * case 1:  all data right
         *          get_column_data function return not NULL
         *          $condition["filter"] return not NULL
         *          $data['view_file'] isn't NULL
         *          $this->input->is_ajax_request() return TRUE
         * case 2:  get_column_data function return NULL
         * case 3:  $condition["filter"] return NULL
         * case 4:  $data['view_file'] NULL
         * case 5:  $this->input->is_ajax_request() return false
         */
        $event_id = 10;
        $data_column = [(object)['user_id' => 1], (object)['user_id' => 2]];
        $filter_data = [
            'filter' => ['event_id' => '1']
        ];
        $data_paging_return = [
            'paging' => 'html',
            'from' => '0',
            'to' => '1',
            'limit' => '10',
            'post' => 'html',
            'total' => '100',
            'order_db' => TRUE
        ];
        $get_order_data = [
            "order_db" => [],
            "order_view" => []
        ];
        $get_column_data_return = [
            'first_name' => [],
            'last_name' => []
        ];
        $standard_record_data_return = [
            (object)['user_id' => 1, 'first_name' => 'anhpt 2'],
            (object)['user_id' => 2, 'first_name' => 'anhpt']
        ];

        $get_list_filter_return = [
            (object)['id' => 1, 'first_name' => 'anhpt 2'],
            (object)['id' => 2, 'first_name' => 'anhpt']
        ];
        $last_query_return = 'SELECT COUNT(*) AS `numrows`
FROM `events` AS `m`
WHERE `m`.`deleted` =0';
        $get_primary_key_return = 'id';

        $data = [
            'view_file' => 'manager/base_manager/table_data',
            'callback' => 'manager/base_manager/table_data'
        ];

        $data = array_merge($data, $data_paging_return, $get_order_data);
        $data['sql'] = $last_query_return;
        $data['columns'] = $get_column_data_return;
        $data['record'] = $standard_record_data_return;
        $data["key_name"] = $get_primary_key_return;
        $data_input_3 = $data;
        $data["filter"] = $filter_data['filter'];
        $data_input_3["filter"] = [];
        $data_input_4 = $data;
        unset($data_input_4['view_file']);

        return ARRAY(
            'case 1:  all data right' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [['phpunit@unit'], [$event_id]],
                            'set_userdata_verify_argument' => ['data_column_application', $data_column]
                        ],
                        'M_event_subscriber_form' => [
                            'get_list_filter_argument' => [['m.event_id' => $event_id], [], [], 0, 0, 'm.id'],
                            'get_list_filter_return' => $data_column
                        ],
                        'Crud_manager' => [
                            'unset_before_get_verify_argument' => ['custom_select_table'],
                            'set_before_get_verify_argument' => ['custom_select_application'],
                            'get_list_filter_argument' => [
                                'where' => ['m.event_id' => $event_id],
                                'where_in' => [],
                                'like' => [],
                                'limit' => $data_paging_return['limit'],
                                'post' => $data_paging_return['post'],
                                'order_db' => $get_order_data['order_db']
                            ],
                            'get_list_filter_return' => $get_list_filter_return,
                            'standard_filter_data_argument' => $filter_data['filter'],
                            'standard_filter_data_return' => [
                                'where' => ['m.event_id' => 10],
                                'where_in' => [],
                                'like' => []
                            ],
                            'get_primary_key_return' => 'id'
                        ],
                        'CI_DB_query_builder' => [
                            'last_query_return' => $last_query_return
                        ],
                        'Application' => [
                            'ajax_list_data_argument' => $data,
                            'get_filter_raw_condition_return' => $filter_data,
                            'get_paging_data_argument' => $filter_data,
                            'get_paging_data_return' => $data_paging_return,
                            'get_order_data_argument' => $filter_data,
                            'get_order_data_return' => $get_order_data,
                            'get_column_data_return' => $get_column_data_return,
                            'standard_record_data_argument' => [$get_list_filter_return, $get_column_data_return],
                            'standard_record_data_return' => $standard_record_data_return
                        ],
                        'MY_Input' => [
                            'get_argument' => 'event_id',
                            'get_return' => $event_id,
                            'is_ajax_request_return' => TRUE
                        ]
                    ]
                ],
                [
                    'CI_Loader' => [
                        'view_argument' => ['data' => $data]
                    ],
                    'Application' => [
                        'ajax_list_data_json_output' => '{"callback":"manager\/base_manager\/table_data","state":1,"html":"Content_html"}'
                    ]
                ]
            ],
            'case 2:  m_event_subscriber_form->get_list_filter function return array()' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [['phpunit@unit'], [$event_id]],
                            'set_userdata_verify_argument' => ['data_column_application', $data_column]
                        ],
                        'M_event_subscriber_form' => [
                            'get_list_filter_argument' => [['m.event_id' => $event_id], [], [], 0, 0, 'm.id'],
                            'get_list_filter_return' => Array()
                        ],
                        'Crud_manager' => [
                            'unset_before_get_verify_argument' => ['custom_select_table'],
                            'set_before_get_verify_argument' => ['custom_select_application'],
                            'get_list_filter_argument' => [
                                'where' => ['m.event_id' => $event_id],
                                'where_in' => [],
                                'like' => [],
                                'limit' => $data_paging_return['limit'],
                                'post' => $data_paging_return['post'],
                                'order_db' => $get_order_data['order_db']
                            ],
                            'get_list_filter_return' => $get_list_filter_return,
                            'standard_filter_data_argument' => $filter_data['filter'],
                            'standard_filter_data_return' => [
                                'where' => ['m.event_id' => 10],
                                'where_in' => [],
                                'like' => []
                            ],
                            'get_primary_key_return' => 'id'
                        ],
                        'CI_DB_query_builder' => [
                            'last_query_return' => $last_query_return
                        ],
                        'Application' => [
                            'ajax_list_data_argument' => $data,
                            'get_filter_raw_condition_return' => $filter_data,
                            'get_paging_data_argument' => $filter_data,
                            'get_paging_data_return' => $data_paging_return,
                            'get_order_data_argument' => $filter_data,
                            'get_order_data_return' => $get_order_data,
                            'get_column_data_return' => $get_column_data_return,
                            'standard_record_data_argument' => [$get_list_filter_return, $get_column_data_return],
                            'standard_record_data_return' => $standard_record_data_return
                        ],
                        'MY_Input' => [
                            'get_argument' => 'event_id',
                            'get_return' => $event_id,
                            'is_ajax_request_return' => TRUE
                        ]
                    ]
                ],
                [
                    'CI_Loader' => [
                        'view_argument' => ['data' => $data]
                    ],
                    'Application' => [
                        'ajax_list_data_json_output' => '{"callback":"manager\/base_manager\/table_data","state":1,"html":"Content_html"}'
                    ]
                ]
            ],
            'case 3:  $condition["filter"] return NULL' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [['phpunit@unit'], [$event_id]],
                            'set_userdata_verify_argument' => ['data_column_application', $data_column]
                        ],
                        'M_event_subscriber_form' => [
                            'get_list_filter_argument' => [['m.event_id' => $event_id], [], [], 0, 0, 'm.id'],
                            'get_list_filter_return' => Array()
                        ],
                        'Crud_manager' => [
                            'unset_before_get_verify_argument' => ['custom_select_table'],
                            'set_before_get_verify_argument' => ['custom_select_application'],
                            'get_list_filter_argument' => [
                                'where' => ['m.event_id' => $event_id],
                                'where_in' => [],
                                'like' => [],
                                'limit' => $data_paging_return['limit'],
                                'post' => $data_paging_return['post'],
                                'order_db' => $get_order_data['order_db']
                            ],
                            'get_list_filter_return' => $get_list_filter_return,
                            'standard_filter_data_argument' => [],
                            'standard_filter_data_return' => [
                                'where' => ['m.event_id' => 10],
                                'where_in' => [],
                                'like' => []
                            ],
                            'get_primary_key_return' => 'id'
                        ],
                        'CI_DB_query_builder' => [
                            'last_query_return' => $last_query_return
                        ],
                        'Application' => [
                            'ajax_list_data_argument' => $data,
                            'get_filter_raw_condition_return' => [],
                            'get_paging_data_argument' => [],
                            'get_paging_data_return' => $data_paging_return,
                            'get_order_data_argument' => [],
                            'get_order_data_return' => $get_order_data,
                            'get_column_data_return' => $get_column_data_return,
                            'standard_record_data_argument' => [$get_list_filter_return, $get_column_data_return],
                            'standard_record_data_return' => $standard_record_data_return
                        ],
                        'MY_Input' => [
                            'get_argument' => 'event_id',
                            'get_return' => $event_id,
                            'is_ajax_request_return' => TRUE
                        ]
                    ]
                ],
                [
                    'CI_Loader' => [
                        'view_argument' => [
                            'data' => $data_input_3
                        ]
                    ],
                    'Application' => [
                        'ajax_list_data_json_output' => '{"callback":"manager\/base_manager\/table_data","state":1,"html":"Content_html"}'
                    ]
                ]
            ],
            'case 4:  $data[\'view_file\'] NULL' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [['phpunit@unit'], [$event_id]],
                            'set_userdata_verify_argument' => ['data_column_application', $data_column]
                        ],
                        'M_event_subscriber_form' => [
                            'get_list_filter_argument' => [['m.event_id' => $event_id], [], [], 0, 0, 'm.id'],
                            'get_list_filter_return' => $data_column
                        ],
                        'Crud_manager' => [
                            'unset_before_get_verify_argument' => ['custom_select_table'],
                            'set_before_get_verify_argument' => ['custom_select_application'],
                            'get_list_filter_argument' => [
                                'where' => ['m.event_id' => $event_id],
                                'where_in' => [],
                                'like' => [],
                                'limit' => $data_paging_return['limit'],
                                'post' => $data_paging_return['post'],
                                'order_db' => $get_order_data['order_db']
                            ],
                            'get_list_filter_return' => $get_list_filter_return,
                            'standard_filter_data_argument' => $filter_data['filter'],
                            'standard_filter_data_return' => [
                                'where' => ['m.event_id' => 10],
                                'where_in' => [],
                                'like' => []
                            ],
                            'get_primary_key_return' => 'id'
                        ],
                        'CI_DB_query_builder' => [
                            'last_query_return' => $last_query_return
                        ],
                        'Application' => [
                            'ajax_list_data_argument' => $data_input_4,
                            'get_filter_raw_condition_return' => $filter_data,
                            'get_paging_data_argument' => $filter_data,
                            'get_paging_data_return' => $data_paging_return,
                            'get_order_data_argument' => $filter_data,
                            'get_order_data_return' => $get_order_data,
                            'get_column_data_return' => $get_column_data_return,
                            'standard_record_data_argument' => [$get_list_filter_return, $get_column_data_return],
                            'standard_record_data_return' => $standard_record_data_return
                        ],
                        'MY_Input' => [
                            'get_argument' => 'event_id',
                            'get_return' => $event_id,
                            'is_ajax_request_return' => TRUE
                        ]
                    ]
                ],
                [
                    'CI_Loader' => [
                        'view_argument' => ['data' => $data_input_4]
                    ],
                    'Application' => [
                        'ajax_list_data_json_output' => '{"callback":"manager\/base_manager\/table_data","state":1,"html":"Content_html"}'
                    ]
                ]
            ],
            'case 5:  $this->input->is_ajax_request() return false' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [['phpunit@unit'], [$event_id]],
                            'set_userdata_verify_argument' => ['data_column_application', $data_column]
                        ],
                        'M_event_subscriber_form' => [
                            'get_list_filter_argument' => [['m.event_id' => $event_id], [], [], 0, 0, 'm.id'],
                            'get_list_filter_return' => $data_column
                        ],
                        'Crud_manager' => [
                            'unset_before_get_verify_argument' => ['custom_select_table'],
                            'set_before_get_verify_argument' => ['custom_select_application'],
                            'get_list_filter_argument' => [
                                'where' => ['m.event_id' => $event_id],
                                'where_in' => [],
                                'like' => [],
                                'limit' => $data_paging_return['limit'],
                                'post' => $data_paging_return['post'],
                                'order_db' => $get_order_data['order_db']
                            ],
                            'get_list_filter_return' => $get_list_filter_return,
                            'standard_filter_data_argument' => $filter_data['filter'],
                            'standard_filter_data_return' => [
                                'where' => ['m.event_id' => 10],
                                'where_in' => [],
                                'like' => []
                            ],
                            'get_primary_key_return' => 'id'
                        ],
                        'CI_DB_query_builder' => [
                            'last_query_return' => $last_query_return
                        ],
                        'Application' => [
                            'ajax_list_data_argument' => $data,
                            'get_filter_raw_condition_return' => $filter_data,
                            'get_paging_data_argument' => $filter_data,
                            'get_paging_data_return' => $data_paging_return,
                            'get_order_data_argument' => $filter_data,
                            'get_order_data_return' => $get_order_data,
                            'get_column_data_return' => $get_column_data_return,
                            'standard_record_data_argument' => [$get_list_filter_return, $get_column_data_return],
                            'standard_record_data_return' => $standard_record_data_return
                        ],
                        'MY_Input' => [
                            'get_argument' => 'event_id',
                            'get_return' => $event_id,
                            'is_ajax_request_return' => FALSE
                        ]
                    ]
                ],
                [
                    'CI_Loader' => [
                        'view_argument' => ['data' => $data]
                    ],
                    'Application' => [
                        'ajax_list_data_json_output' => '{"callback":"manager\/base_manager\/table_data","state":1,"html":"Content_html"}'
                    ]
                ]
            ]
        );
    }

    private function _prepare_constructor_call_ajax_list_data($data) {
        $session = $this->getMockBuilder('CI_Session')
            ->disableOriginalConstructor()
            ->getMock();
        $session->method("userdata")->withConsecutive(
            $data['CI_Session']['userdata_argument'][0],
            $data['CI_Session']['userdata_argument'][1]
        )->willReturnOnConsecutiveCalls(
            $data['CI_Session']['userdata_return'][0],
            $data['CI_Session']['userdata_return'][1]
        );
        // Inject mock object
        load_class_instance('session', $session);
    }

    private function _prepare_function_call_ajax_list_data($CI, $data_init) {
        $CI->m_event_subscriber_form = $this->getMockBuilder('M_event_subscriber_form')
            ->setMethods(array('get_list_filter'))
            ->getMock();
        $CI->m_event_subscriber_form->method('get_list_filter')
            ->with(
                $data_init['M_event_subscriber_form']['get_list_filter_argument'][0],
                $data_init['M_event_subscriber_form']['get_list_filter_argument'][1],
                $data_init['M_event_subscriber_form']['get_list_filter_argument'][2],
                $data_init['M_event_subscriber_form']['get_list_filter_argument'][3],
                $data_init['M_event_subscriber_form']['get_list_filter_argument'][4]
            )
            ->willReturn($data_init['M_event_subscriber_form']['get_list_filter_return']);

        $CI->model = $this->getMockBuilder('Crud_manager')
            ->setMethods(array('unset_before_get', 'set_before_get', 'get_list_filter', 'standard_filter_data', 'get_primary_key'))
            ->getMock();
        $CI->model->method('get_list_filter')
            ->with(
                $data_init['Crud_manager']['get_list_filter_argument']['where'],
                $data_init['Crud_manager']['get_list_filter_argument']['where_in'],
                $data_init['Crud_manager']['get_list_filter_argument']['like'],
                $data_init['Crud_manager']['get_list_filter_argument']['limit'],
                $data_init['Crud_manager']['get_list_filter_argument']['post'],
                $data_init['Crud_manager']['get_list_filter_argument']['order_db']
            )
            ->willReturn($data_init['Crud_manager']['get_list_filter_return']);
        $CI->model->method('standard_filter_data')
            ->with($data_init['Crud_manager']['standard_filter_data_argument'])
            ->willReturn($data_init['Crud_manager']['standard_filter_data_return']);
        $CI->model->method('get_primary_key')
            ->willReturn($data_init['Crud_manager']['get_primary_key_return']);

        $CI->input = $this->getMockBuilder('MY_Input')
            ->setMethods(array('get', 'is_ajax_request'))
            ->getMock();
        $CI->input->method('get')
            ->with($data_init['MY_Input']['get_argument'])
            ->willReturn($data_init['MY_Input']['get_return']);
        $CI->input->method('is_ajax_request')
            ->willReturn($data_init['MY_Input']['is_ajax_request_return']);

        if (count($data_init['M_event_subscriber_form']['get_list_filter_return'])) {
            //verify model change
            $this->verifyInvoked($CI->session, 'set_userdata', $data_init['CI_Session']['set_userdata_verify_argument']);
            $this->verifyInvoked($CI->model, 'unset_before_get', $data_init['Crud_manager']['unset_before_get_verify_argument']);
            $this->verifyInvoked($CI->model, 'set_before_get', $data_init['Crud_manager']['set_before_get_verify_argument']);
        }
    }

    private function _make_controller_ajax_list_data($data) {
        $controller = $this->getMockBuilder('application')
            ->setMethods(ARRAY('get_filter_raw_condition', 'get_paging_data', 'get_order_data', 'get_column_data', 'standard_record_data', 'show_page'))
            ->getMock();
        $controller->method('get_filter_raw_condition')
            ->willReturn($data['Application']['get_filter_raw_condition_return']);
        $controller->method('get_paging_data')
            ->with($data['Application']['get_paging_data_argument'])
            ->willReturn($data['Application']['get_paging_data_return']);
        $controller->method('get_order_data')
            ->with($data['Application']['get_order_data_argument'])
            ->willReturn($data['Application']['get_order_data_return']);
        $controller->method('get_column_data')
            ->willReturn($data['Application']['get_column_data_return']);
        $controller->method('standard_record_data')
            ->with($data['Application']['standard_record_data_argument'][0], $data['Application']['standard_record_data_argument'][1])
            ->willReturn($data['Application']['standard_record_data_return']);

        return $controller;
    }

    /**
     * @dataProvider data_get_filter_raw_condition
     * @param $data
     * @param $expect
     */
    public function test_get_filter_raw_condition($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_call_get_filter_raw_condition($data_init);
        // Create controller and make protected method
        $class = new ReflectionClass('Application');
        $method = $class->getMethod('get_filter_raw_condition');
        $method->setAccessible(TRUE);

        $application_controller = New Application();

        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_get_filter_raw_condition($CI, $data_init);

        $output = $method->invoke($application_controller);
        $this->assertEquals($output, $expect['__expect__']['Appication']['get_filter_raw_condition_return']);
    }

    public function data_get_filter_raw_condition() {
        /**
         * case 1: post and get have data
         * case 2: post have data
         * case 3: get have data
         */
        $event_id = 10;
        $email = 'admin@admin';
        return Array(
            'case 1: post and get have data' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'MY_Input' => [
                            'post_return' => ['event_id' => $event_id],
                            'get_return' => ['email' => $email],
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Appication' => [
                            'get_filter_raw_condition_return' => [
                                'event_id' => $event_id,
                                'filter' => [
                                    'email' => $email
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            'case 2: only post have data' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'MY_Input' => [
                            'post_return' => ['event_id' => $event_id],
                            'get_return' => []
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Appication' => [
                            'get_filter_raw_condition_return' => [
                                'event_id' => $event_id
                            ]
                        ]
                    ]
                ]
            ],
            'case 3: only get have data' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'MY_Input' => [
                            'post_return' => [],
                            'get_return' => ['email' => $email],
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Appication' => [
                            'get_filter_raw_condition_return' => [
                                'filter' => [
                                    'email' => $email
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        );
    }

    private function _prepare_constructor_call_get_filter_raw_condition($data) {
        $session = $this->getMockBuilder('CI_Session')
            ->disableOriginalConstructor()
            ->getMock();
        $session->method("userdata")->withConsecutive(
            $data['CI_Session']['userdata_argument'][0],
            $data['CI_Session']['userdata_argument'][1]
        )->willReturnOnConsecutiveCalls(
            $data['CI_Session']['userdata_return'][0],
            $data['CI_Session']['userdata_return'][1]
        );
        // Inject mock object
        load_class_instance('session', $session);
    }

    private function _prepare_function_call_get_filter_raw_condition($CI, $data_init) {
        $CI->input = $this->getMockBuilder('MY_Input')
            ->setMethods(array('get', 'post'))
            ->getMock();
        $CI->input->method('post')
            ->willReturn($data_init['MY_Input']['post_return']);
        $CI->input->method('get')
            ->willReturn($data_init['MY_Input']['get_return']);
    }

    /**
     * @dataProvider data_detail
     * @param $data
     * @param $expect
     */
    public function test_detail($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_call_detail($data_init);
        // Create controller and make protected method
        $application_controller = New Application();

        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_detail($CI, $data_init);

        $this->expectOutputString($expect['__expect__']['Application']['detail_return']);
        $application_controller->detail(
            $data_init['Application_lib']['detail_argument']['id'],
            $data_init['Application_lib']['detail_argument']['checksum']
        );
    }

    public function data_detail() {
        $event_id = 10;
        $email = 'admin@admin';
        return Array(
            'case 1: post and get have data' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'Application_lib' => [
                            'detail_argument' => ['id' => $event_id, 'checksum' => '12345'],
                            'detail_return' => [
                                'state' => 0,
                                'data' => (object)['id' => 1],
                                'msg' => 'Checksum code user not exits'],
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'detail_return' => '{"state":0,"data":{"id":1},"msg":"Checksum code user not exits"}'
                        ]
                    ]
                ]
            ]
        );
    }

    private function _prepare_constructor_call_detail($data) {
        $session = $this->getMockBuilder('CI_Session')
            ->disableOriginalConstructor()
            ->getMock();
        $session->method("userdata")->withConsecutive(
            $data['CI_Session']['userdata_argument'][0],
            $data['CI_Session']['userdata_argument'][1]
        )->willReturnOnConsecutiveCalls(
            $data['CI_Session']['userdata_return'][0],
            $data['CI_Session']['userdata_return'][1]
        );
        // Inject mock object
        load_class_instance('session', $session);
    }

    private function _prepare_function_call_detail($CI, $data_init) {
        $CI->application_lib = $this->getMockBuilder('Application_lib')
            ->setMethods(array('detail'))
            ->getMock();
        $CI->application_lib->method('detail')
            ->with(
                $data_init['Application_lib']['detail_argument']['id'],
                $data_init['Application_lib']['detail_argument']['checksum']
            )
            ->willReturn($data_init['Application_lib']['detail_return']);
    }

    /**
     * @dataProvider data_edit_save
     * @param $data
     * @param $expect
     */
    public function test_edit_save($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_for_login($data_init);
        // Create controller and make protected method
        if (isset($data_init['Application']['standard_record_data_argument'])) {
            $application_controller = $this->getMockBuilder('application')
                ->setMethods(ARRAY('standard_record_data'))
                ->getMock();

            $application_controller->method('standard_record_data')
                ->with($data_init['Application']['standard_record_data_argument']['record'])
                ->willReturn($data_init['Application']['standard_record_data_return']);
        } else {
            $application_controller = new Application();
        }


        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_edit_save($CI, $data_init);

        if ($data_init['M_event_subscriber_form']['get_return']) {
            $this->expectOutputString($expect['__expect__']['Application']['edit_save_return']);
        }

        $return = $application_controller->edit_save(
            $data_init['Application']['edit_save_argument']['id'],
            $data_init['Application']['edit_save_argument']['data'],
            $data_init['Application']['edit_save_argument']['data_return'],
            $data_init['Application']['edit_save_argument']['skip_validate']
        );
        if (!$data_init['M_event_subscriber_form']['get_return']) {
            $this->assertEquals($expect['__expect__']['Application']['edit_save_return'], $return);
        }
    }

    public function data_edit_save() {
        /**
         * case 1: all data is right
         * case 2: not found $application_info
         * case 3: $result['record'] NULL
         */
        $event_id = 10;
        $event_name = 'event name';
        $email = 'admin@admin';
        return Array(
            'case 1: all data is right' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'M_event_subscriber_form' => [
                            'get_argument' => [
                                'id' => $event_id
                            ],
                            'get_return' => [
                                'id' => $event_id,
                                'name' => $event_name,
                                'checksum_code' => NULL
                            ]
                        ],
                        'CI_Input' => [
                            'post_return' => []
                        ],
                        'Application_lib' => [
                            'edit_application_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL,
                                'data' => [],
                                'data_return' => [
                                    'callback' => ''
                                ],
                                'skip_validate' => TRUE,
                                'skip_send_mail' => FALSE
                            ],
                            'edit_application_save_return' => [
                                'state' => 0,
                                'data' => (object)['id' => $event_id],
                                'record' => ['record'],
                                'msg' => 'Message test'],
                        ],
                        'Application' => [
                            'standard_record_data_argument' => ['record' => ['record']],
                            'standard_record_data_return' => ['record & standard_record_data'],
                            'edit_save_argument' => [
                                'id' => $event_id,
                                'data' => [],
                                'data_return' => [],
                                'skip_validate' => TRUE
                            ]
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'edit_save_return' => '{"state":0,"data":{"id":' . $event_id . '},"record":["record & standard_record_data"],"msg":"Message test"}'
                        ]
                    ]
                ]
            ],
            'case 2: not found $application_info' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'M_event_subscriber_form' => [
                            'get_argument' => [
                                'id' => $event_id
                            ],
                            'get_return' => NULL
                        ],
                        'CI_Input' => [
                            'post_return' => []
                        ],
                        'Application_lib' => [
                            'edit_application_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL,
                                'data' => [],
                                'data_return' => [
                                    'callback' => ''
                                ],
                                'skip_validate' => TRUE,
                                'skip_send_mail' => FALSE
                            ],
                            'edit_application_save_return' => [
                                'state' => 0,
                                'data' => (object)['id' => $event_id],
                                'record' => [],
                                'msg' => 'Message test'],
                        ],
                        'Application' => [
                            'standard_record_data_argument' => ['record' => []],
                            'standard_record_data_return' => ['record & standard_record_data'],
                            'edit_save_argument' => [
                                'id' => $event_id,
                                'data' => [],
                                'data_return' => [],
                                'skip_validate' => TRUE
                            ]
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'edit_save_return' => [
                                'callback' => '',
                                'state' => 0,
                                'msg' => 'お申し込者の情報が存在ません。'
                            ]
                        ]
                    ]
                ]
            ],
            'case 3: $result[\'record\'] NULL' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'M_event_subscriber_form' => [
                            'get_argument' => [
                                'id' => $event_id
                            ],
                            'get_return' => [
                                'id' => $event_id,
                                'name' => $event_name,
                                'checksum_code' => NULL
                            ]
                        ],
                        'CI_Input' => [
                            'post_return' => []
                        ],
                        'Application_lib' => [
                            'edit_application_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL,
                                'data' => [],
                                'data_return' => [
                                    'callback' => ''
                                ],
                                'skip_validate' => TRUE,
                                'skip_send_mail' => FALSE
                            ],
                            'edit_application_save_return' => [
                                'state' => 0,
                                'data' => (object)['id' => $event_id],
                                'record' => [],
                                'msg' => 'Message test'],
                        ],
                        'Application' => [
                            'edit_save_argument' => [
                                'id' => $event_id,
                                'data' => [],
                                'data_return' => [],
                                'skip_validate' => TRUE
                            ]
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'edit_save_return' => '{"state":0,"data":{"id":' . $event_id . '},"record":[],"msg":"Message test"}'
                        ]
                    ]
                ]
            ]
        );
    }

    private function _prepare_constructor_for_login($data) {
        $session = $this->getMockBuilder('CI_Session')
            ->disableOriginalConstructor()
            ->getMock();
        $session->method("userdata")->withConsecutive(
            $data['CI_Session']['userdata_argument'][0],
            $data['CI_Session']['userdata_argument'][1]
        )->willReturnOnConsecutiveCalls(
            $data['CI_Session']['userdata_return'][0],
            $data['CI_Session']['userdata_return'][1]
        );
        // Inject mock object
        load_class_instance('session', $session);
    }

    private function _prepare_function_call_edit_save($CI, $data_init) {
        $CI->model = $this->getMockBuilder('M_event_subscriber_form')
            ->setMethods(array('get'))
            ->getMock();
        if ($data_init['M_event_subscriber_form']['get_return']) {
            $CI->model->method('get')
                ->with($data_init['M_event_subscriber_form']['get_argument']['id'])
                ->willReturn((object)$data_init['M_event_subscriber_form']['get_return']);
        } else {
            $CI->model->method('get')
                ->with($data_init['M_event_subscriber_form']['get_argument']['id'])
                ->willReturn($data_init['M_event_subscriber_form']['get_return']);
        }

        $CI->application_lib = $this->getMockBuilder('Application_lib')
            ->setMethods(array('edit_application_save'))
            ->getMock();
        $CI->application_lib->method('edit_application_save')
            ->with(
                $data_init['Application_lib']['edit_application_save_argument']['id'],
                $data_init['Application_lib']['edit_application_save_argument']['checksum_code'],
                $data_init['Application_lib']['edit_application_save_argument']['data'],
                $data_init['Application_lib']['edit_application_save_argument']['data_return'],
                $data_init['Application_lib']['edit_application_save_argument']['skip_validate'],
                $data_init['Application_lib']['edit_application_save_argument']['skip_send_mail']
            )
            ->willReturn($data_init['Application_lib']['edit_application_save_return']);

        $CI->input = $this->getMockBuilder('MY_Input')
            ->setMethods(array('post'))
            ->getMock();
        $CI->input->method('post')
            ->willReturn($data_init['CI_Input']['post_return']);
    }

    /**
     * @dataProvider data_cancel_save
     * @param $data
     * @param $expect
     */
    public function test_cancel_save($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_for_login($data_init);
        // Create controller and make protected method
        if (isset($data_init['Application']['standard_record_data_argument'])) {
            $application_controller = $this->getMockBuilder('application')
                ->setMethods(ARRAY('standard_record_data'))
                ->getMock();

            $application_controller->method('standard_record_data')
                ->with($data_init['Application']['standard_record_data_argument']['record'])
                ->willReturn($data_init['Application']['standard_record_data_return']);
        } else {
            $application_controller = new Application();
        }

        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_cancel_save($CI, $data_init);
        $this->expectOutputString($expect['__expect__']['Application']['edit_save_return']);

        $application_controller->cancel_save(
            $data_init['Application']['edit_save_argument']['id'],
            $data_init['Application']['edit_save_argument']['checksum_code']
        );
    }

    public function data_cancel_save() {
        /**
         * case 1: all data is right
         * case 2: $result['record'] NULL
         */
        $event_id = 10;
        $email = 'admin@admin';
        return Array(
            'case 1: all data is right' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'Application_lib' => [
                            'cancel_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL,
                                'data' => [],
                                'skip_validate' => TRUE,
                                'skip_send_mail' => FALSE
                            ],
                            'cancel_save_return' => [
                                'state' => 0,
                                'data' => (object)['id' => $event_id],
                                'record' => ['record'],
                                'msg' => 'Message test']
                        ],
                        'Application' => [
                            'standard_record_data_argument' => ['record' => ['record']],
                            'standard_record_data_return' => ['record & standard_record_data'],
                            'edit_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL
                            ]
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'edit_save_return' => '{"callback":"save_form_edit_application_response","state":0,"data":{"id":10},"record":["record & standard_record_data"],"msg":"Message test"}'
                        ]
                    ]
                ]
            ],
            'case 2: $result[\'record\'] NULL' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'Application_lib' => [
                            'cancel_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL,
                                'data' => [],
                                'skip_validate' => TRUE,
                                'skip_send_mail' => FALSE
                            ],
                            'cancel_save_return' => [
                                'state' => 0,
                                'data' => (object)['id' => $event_id],
                                'record' => [],
                                'msg' => 'Message test']
                        ],
                        'Application' => [
                            'edit_save_argument' => [
                                'id' => $event_id,
                                'checksum_code' => NULL
                            ]
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'edit_save_return' => '{"callback":"save_form_edit_application_response","state":0,"data":{"id":10},"record":[],"msg":"Message test"}'
                        ]
                    ]
                ]
            ]
        );
    }

    public function _prepare_function_call_cancel_save($CI, $data_init) {
        $CI->application_lib = $this->getMockBuilder('Application_lib')
            ->setMethods(array('cancel_save'))
            ->getMock();

        $data = [
            'canceled' => '1',
            'canceled_time' => date('Y-m-d H:i:s'),
            'updated_time' => date('Y-m-d H:i:s'),
        ];
        $data_return['callback'] = 'save_form_edit_application_response';

        $CI->application_lib->method('cancel_save')
            ->with(
                $data_init['Application_lib']['cancel_save_argument']['id'],
                $data_init['Application_lib']['cancel_save_argument']['checksum_code'],
                $data,
                $data_return,
                $data_init['Application_lib']['cancel_save_argument']['skip_send_mail']
            )
            ->willReturn($data_init['Application_lib']['cancel_save_return']);
    }

    /**
     * @dataProvider data_vip_save
     * @param $data
     * @param $expect
     */
    public function test_vip_save($data, $expect) {
        $data_init = $data['__data_init__'];
        reset_instance();
        // Run callablePreConstructor
        $this->_prepare_constructor_for_login($data_init);
        // Create controller and make protected method
        $application_controller = new Application();

        $CI =& get_instance();
        // Run callable
        $this->_prepare_function_call_vip_save($CI, $data_init);

        $this->expectOutputString($expect['__expect__']['Application']['edit_save_return']);
        $application_controller->vip_save($data_init['Application']['vip_save_argument']['id']);
    }

    public function data_vip_save() {
        /**
         * case 1: all data is right
         * case 2: $this->model->get($id) return NULL
         */
        $event_id = 10;
        $event_name = 'event name';
        $email = 'admin@admin';
        return Array(
            'case 1: all data is right' => [
                [
                    '__data_init__' => [
                        'CI_Session' => [
                            'userdata_argument' => [['identity'], ['user_id']],
                            'userdata_return' => [[$email], [$event_id]]
                        ],
                        'Application' => [
                            'vip_save_argument' => [
                                'id' => $event_id
                            ]
                        ],
                        'M_event_subscriber_form' => [
                            'get_argument' => [
                                'id' => $event_id
                            ],
                            'get_return' => [
                                'id' => $event_id,
                                'name' => $event_name,
                                'vip_time' => '2016/08/29 22:06'
                            ]
                        ]
                    ]
                ],
                [
                    '__expect__' => [
                        'Application' => [
                            'edit_save_return' => '{"callback":"save_form_edit_application_response","state":0,"data":{"id":10},"record":["record & standard_record_data"],"msg":"Message test"}'
                        ]
                    ]
                ]
            ]
        );
    }

    private function _prepare_function_call_vip_save($CI, $data_init) {
        $CI->model = $this->getMockBuilder('M_event_subscriber_form')
            ->setMethods(array('get'))
            ->getMock();
        if ($data_init['M_event_subscriber_form']['get_return']) {
            $CI->model->method('get')
                ->with($data_init['M_event_subscriber_form']['get_argument']['id'])
                ->willReturn((object)$data_init['M_event_subscriber_form']['get_return']);
        } else {
            $CI->model->method('get')
                ->with($data_init['M_event_subscriber_form']['get_argument']['id'])
                ->willReturn($data_init['M_event_subscriber_form']['get_return']);
        }
    }
}