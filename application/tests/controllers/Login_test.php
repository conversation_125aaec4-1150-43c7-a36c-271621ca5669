<?php

/**
 * Created by PhpStorm.
 * User: miunh
 * Date: 15-Jun-16
 * Time: 3:56 PM
 */
class Login_test extends TestCase {

    protected function setUp() {
        $this->resetInstance();
    }

    /**
     * @dataProvider login_data_provider
     */
    public function test_index($input_callable, $check_function, $check_param) {
        $this->request->setCallable($input_callable);
        $output = $this->request('GET', 'manager/login/index');
        call_user_func_array([$this, $check_function], [$check_param, $output]);
    }

    protected function check_login_form($check_param, $output) {
        $this->request->setCallable(function ($CI) {
            $CI->load = $this->getMockBuilder('CI_Loader')
                ->disableOriginalConstructor()
                ->getMock();
            $data = Array();
            $data['login_url'] = site_url('manager/login/check');
            $data['register_url'] = site_url('manager/login');
            $this->verifyInvokedOnce($CI->load, 'view', [
                    "manager/login/login",
                    $data,
                    TRUE,
                ]
            );
        });
    }

    public function login_data_provider() {
        return [
            'chua login' =>
                [
                    function ($CI) {
                        $CI->session = $this->getMockBuilder('CI_Session')
                            ->disableOriginalConstructor()
                            ->getMock();
                        $CI->session->method("userdata")->with("user_id")->willReturn(NULL);
                    },
                    'check_login_form',
                    NULL
                ],
            'da login' =>
                [
                    function ($CI) {
                        $CI->session = $this->getMockBuilder('CI_Session')
                            ->disableOriginalConstructor()
                            ->getMock();
                        $CI->session->method("userdata")->with("user_id")->willReturn(10);
                    },
                    'assertRedirect',
                    'manager/event_list'
                ]
        ];
    }

    /**
     * @dataProvider data_test_redirect_check
     */
    public function test_redirect_check($call_function, $method, $param) {
        $this->$call_function($method, 'manager/login/check', $param);
        $this->assertRedirect('/');
    }

    public function data_test_redirect_check() {
        return ARRAY(
            'GET request' => ['request', 'GET', NULL],
            'POST request' => ['request', 'POST', [
                'usesname' => 'test',
                'password' => 'test',
            ]],
            'AJAX GET request' => ['ajaxRequest', 'GET', NULL],
        );
    }

    /**
     * @dataProvider data_check
     */
    public function test_check($method, $param, $expect) {
        $this->request->setCallable(
            function ($CI) {
                $init_data = $CI->input->post('__data_test__');
                $expect_data = $CI->input->post('__data_expect__');
                $email = $CI->input->post('username');
                $pass = $CI->input->post("password");

                $CI->session = $this->getMockBuilder('CI_Session')
                    ->disableOriginalConstructor()
                    ->getMock();
                $CI->session->method("userdata")->with("user_id")->willReturn($init_data['user_id']);

                $CI->ion_auth = $this->getMockBuilder('Ion_auth_model')
                    ->disableOriginalConstructor()
                    ->getMock();
                $CI->ion_auth->method("get_last_login")->with($email)->willReturn($init_data['last_login']);
                $CI->ion_auth->method("login")->with($email, $pass)->willReturn($init_data['login']);

                $CI->m_user = $this->getMockBuilder('M_user')
                    ->disableOriginalConstructor()
                    ->getMock();
                $CI->m_user->method("get_all_children")->with($init_data['user_id'])->willReturn($init_data['list_children']);

                $data_array = ARRAY();
                if (isset($expect_data['old_last_login'])) {
                    $data_array[] = ['old_last_login', $expect_data['old_last_login']];
                }
                if (isset($expect_data['all_children_user'])) {
                    $data_array[] = ['all_children_user', $expect_data['all_children_user']];
                }

                $this->verifyInvokedMultipleTimes(
                    $CI->session,
                    'set_userdata',
                    count($data_array),
                    $data_array
                );
            }
        );

        $json_output = $this->ajaxRequest($method, 'manager/login/check', $param);

        if (isset($expect['data_result'])) {
            $this->assertJsonStringEqualsJsonString($json_output, json_encode($expect['data_result']));
        }
    }

    public function data_check() {
        $last_login = time();
        $user_id = 10;
        $list_children = [11, 12, 13, 14, 15];
        $all_children_user = [11, 12, 13, 14, 15, 10];
        return ARRAY(
            'AJAX POST request success' =>
                [
                    'POST',
                    [
                        'username' => '<EMAIL>',
                        'password' => '1234qwer',
                        '__data_test__' =>
                            [
                                'last_login' => $last_login,
                                'login' => TRUE,
                                'user_id' => $user_id,
                                'list_children' => $list_children
                            ],
                        '__data_expect__' =>
                            [
                                'old_last_login' => $last_login,
                                'all_children_user' => $all_children_user
                            ]
                    ],
                    [
                        'data_result' => [
                            'callback' => "login_response",
                            'state' => 1,
                            'msg' => "Login success",
                            'redirect' => site_url('manager/event_list')
                        ]
                    ]
                ],
            'AJAX POST request success but don`t have last_login' =>
                [
                    'POST',
                    [
                        'username' => '<EMAIL>',
                        'password' => '1234qwer',
                        '__data_test__' =>
                            [
                                'last_login' => 0,
                                'login' => TRUE,
                                'user_id' => $user_id,
                                'list_children' => $list_children
                            ],
                        '__data_expect__' =>
                            [
                                'old_last_login' => 0,
                                'all_children_user' => $all_children_user
                            ]
                    ],
                    [
                        'data_result' => [
                            'callback' => "login_response",
                            'state' => 1,
                            'msg' => "Login success",
                            'redirect' => site_url('manager/event/create')
                        ]
                    ]
                ],
            'AJAX POST request success but don`t have user_id' =>
                [
                    'POST',
                    [
                        'username' => '<EMAIL>',
                        'password' => '1234qwer',
                        '__data_test__' =>
                            [
                                'last_login' => $last_login,
                                'login' => TRUE,
                                'user_id' => 0,
                                'list_children' => $all_children_user
                            ],
                        '__data_expect__' =>
                            [
                                'old_last_login' => $last_login
                            ]
                    ],
                    [
                        'data_result' => [
                            'callback' => "login_response",
                            'state' => 1,
                            'msg' => "Login success",
                            'redirect' => site_url('manager/event_list')
                        ]
                    ]
                ],
            'AJAX POST request fail' =>
                [
                    'POST',
                    [
                        'username' => '<EMAIL>',
                        'password' => '1234qwer',
                        '__data_test__' =>
                            [
                                'last_login' => $last_login,
                                'login' => FALSE,
                                'user_id' => 10,
                                'list_children' => $all_children_user
                            ]
                    ],
                    [
                        'data_result' => [
                            'callback' => "login_response",
                            'state' => 0,
                            'msg' => "ご入力した ID / パスワードが一致いたしません。"
                        ]
                    ]
                ]
        );
    }

    /**
     * @dataProvider data_form
     */
    public function test_form($method, $param, $expect) {

        $this->request($method, 'manager/login/form', $param);
        $this->assertRedirect($expect['home_site_url']);
    }

    public function data_form() {
        $CI =& get_instance();
        $CI->config->load('site_settings');
        $home_url = $CI->config->item('home_site_url');
        return ARRAY(
            [
                'GET',
                ['__data_test__' =>
                    [
                        'home_site_url' => $home_url
                    ]
                ],
                [
                    'home_site_url' => $home_url . '?action=createnewaccount'
                ]
            ],
            [
                'POST',
                ['__data_test__' =>
                    [
                        'home_site_url' => $home_url
                    ]
                ],
                [
                    'home_site_url' => $home_url . '?action=createnewaccount'
                ]
            ]

        );
    }

    /**
     * @dataProvider data_logout
     * @param $method
     * @param $param
     * @param $expect
     */
    public function test_logout($method, $param, $expect){
        $this->request->setCallable(function ($CI) {
            $CI->session = $this->getMockBuilder('CI_Session')
                ->disableOriginalConstructor()
                ->getMock();

            $this->verifyInvokedOnce(
                $CI->session,
                'sess_destroy'
            );
        });

        $this->request($method, 'manager/login/logout', $param);
        $this->assertRedirect(site_url('manager/login'));
    }

    public function data_logout(){
        return ARRAY(
            [
                'GET',
                [],
                []
            ],
            [
                'POST',
                [],
                []
            ]

        );
    }
}