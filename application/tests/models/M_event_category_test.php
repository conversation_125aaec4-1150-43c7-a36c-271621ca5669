<?php

/**
 * Created by PhpStorm.
 * User: a
 * Date: 9/5/16
 * Time: 16:16
 */
class M_event_category_test extends TestCase {
    var $model = NULL;

    public function setUp() {
        $this->resetInstance();
        $this->CI->load->model('M_event_category');
        $this->model = $this->CI->M_event_category;
        $this->CI->db = $this->getMockBuilder('CI_DB_mysqli_driver')
            ->disableOriginalConstructor()
            ->disableOriginalClone()
            ->disableArgumentCloning()
            ->disallowMockingUnknownTypes()
            ->getMock();
    }

    public function test_order_by_table() {
        $table_alias = 'm';
        $this->CI->db->expects($this->exactly(1))
            ->method("select")
            ->with($table_alias . ".id, " . $table_alias . ".cat_name as name, " . $table_alias . ".parent_id");
        $this->CI->db->expects($this->exactly(1))
            ->method("order_by")
            ->with($table_alias . ".id", 'ASC');
        $this->model->custom_select_api();
    }

    public function test_get_all_children() {
        $parent_id = 10;
        $get_many_by_return = ARRAY(
            '1' => [
                (object)['id' => 101],
                (object)['id' => 102]
            ],
            '2' => [
                (object)['id' => 1011],
                (object)['id' => 1012]
            ],
            '3' => [],
            '4' => [],
            '5' => []
        );
        $expected = [101, 102, 1011, 1012];
        $this->model = $this->getMockBuilder('M_user')
            ->setMethods(array('get_many_by'))
            ->getMock();
        $this->model->method('get_many_by')
            ->withConsecutive(
                [['parent_id' => $parent_id]],
                [['parent_id' => 101]],
                [['parent_id' => 102]],
                [['parent_id' => 1011]],
                [['parent_id' => 1012]])
            ->willReturnOnConsecutiveCalls(
                $get_many_by_return['1'],
                $get_many_by_return['2'],
                $get_many_by_return['3'],
                $get_many_by_return['4'],
                $get_many_by_return['5']
            );

        $result = $this->model->get_all_children($parent_id);
        $this->assertEquals($expected, $result);
    }
}