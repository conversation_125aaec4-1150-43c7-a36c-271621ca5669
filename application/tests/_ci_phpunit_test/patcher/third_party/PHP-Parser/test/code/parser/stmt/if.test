If/Elseif/Else
-----
<?php

if      ($a) {}
elseif  ($b) {}
elseif  ($c) {}
else         {}

if ($a) {} // without else

if      ($a):
elseif  ($b):
elseif  ($c):
else        :
endif;

if ($a): endif; // without else
-----
array(
    0: Stmt_If(
        cond: Expr_Variable(
            name: a
        )
        stmts: array(
        )
        elseifs: array(
            0: Stmt_ElseIf(
                cond: Expr_Variable(
                    name: b
                )
                stmts: array(
                )
            )
            1: Stmt_ElseIf(
                cond: Expr_Variable(
                    name: c
                )
                stmts: array(
                )
            )
        )
        else: Stmt_Else(
            stmts: array(
            )
        )
    )
    1: Stmt_If(
        cond: Expr_Variable(
            name: a
        )
        stmts: array(
        )
        elseifs: array(
        )
        else: null
    )
    2: Stmt_If(
        cond: Expr_Variable(
            name: a
        )
        stmts: array(
        )
        elseifs: array(
            0: Stmt_ElseIf(
                cond: Expr_Variable(
                    name: b
                )
                stmts: array(
                )
            )
            1: Stmt_ElseIf(
                cond: Expr_Variable(
                    name: c
                )
                stmts: array(
                )
            )
        )
        else: Stmt_Else(
            stmts: array(
            )
        )
    )
    3: Stmt_If(
        cond: Expr_Variable(
            name: a
        )
        stmts: array(
        )
        elseifs: array(
        )
        else: null
    )
)