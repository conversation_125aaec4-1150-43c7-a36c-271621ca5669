Yield with unary operator argument
-----
<?php

function gen() {
    yield +1;
    yield -1;
    yield * -1;
}
-----
array(
    0: Stmt_Function(
        byRef: false
        name: gen
        params: array(
        )
        returnType: null
        stmts: array(
            0: Expr_Yield(
                key: null
                value: Expr_UnaryPlus(
                    expr: Scalar_LNumber(
                        value: 1
                    )
                )
            )
            1: Expr_Yield(
                key: null
                value: Expr_UnaryMinus(
                    expr: Scalar_LNumber(
                        value: 1
                    )
                )
            )
            2: Expr_BinaryOp_Mul(
                left: Expr_Yield(
                    key: null
                    value: null
                )
                right: Expr_UnaryMinus(
                    expr: Scalar_LNumber(
                        value: 1
                    )
                )
            )
        )
    )
)
