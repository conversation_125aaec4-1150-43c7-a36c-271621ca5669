<?php
class File_lib {
	protected $_ci;
	protected $config = [
		"upload_path" => './upload/',
		"allowed_types" => "jpg|jpeg|png|bmp",
		"encrypt_name" => TRUE,
		"max_size" => "3072", // 3M
	];

    public function __construct($config = []) {
        $this->_ci = &get_instance();
        $this->config = array_merge($this->config, $config);
        $this->_ci->load->library('upload', $this->config);
        if (!file_exists($this->config["upload_path"])) {
		    mkdir($this->config["upload_path"], 0777, TRUE);
		}
    }

    public function upload($field_name){
    	$ret = [
    		"error" => NULL,
    		"data" => NULL
    	];

    	if (! $this->_ci->upload->do_upload($field_name)) {
            $ret["error"] = $this->_ci->upload->display_errors();
        } else {
        	$ret["data"] = $this->_ci->upload->data();
        }

    	return $ret;
    }
}