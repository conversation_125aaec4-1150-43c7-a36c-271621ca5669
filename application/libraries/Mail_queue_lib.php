<?php

// AnhLD - 20190514
class Mail_queue_lib {

	private $mail_queue_dir = "./mail_queue";
	protected $_ci;
	protected $status = array(
		'unsent'	=>	0,
		'sent'		=>	1,
		'sending'	=>	2,
		'folder_not_exist'	=>	3
	);

	public function __construct() {
        $this->_ci = &get_instance();
        $this->_ci->load->library('Email_lib');
        $this->_ci->load->library('S3_upload');
		$this->_ci->load->model('m_mail_queue');
		$this->_ci->config->load('s3', TRUE);
		$s3_config = $this->_ci->config->item('s3');
		$this->bucket_name = $s3_config['bucket_name'];
		$this->folder_name = $s3_config['folder_name'] ? $s3_config['folder_name'] . "/mail_queue" : "mail_queue";
		$this->s3_url = $s3_config['s3_url'];
    }

    function create_email_package(Array $data){
    	$mail_item_folder_name = $this->generate_uuid();

    	$content = [
			'event_id'      => isset($data['event_id']) ? $data['event_id'] : '',
    	    'subscriber_id' => isset($data['subscriber_id']) ? $data['subscriber_id'] : '',
    		'subject' 		=> isset($data['subject']) ? $data['subject'] : 'Default subject',
    		'content' 		=> isset($data['content']) ? $data['content'] : 'Default content',
    		'email_to' 		=> isset($data['email_to']) ? $data['email_to'] : 'email_to',
    		'email_reply' 	=> isset($data['email_reply']) ? $data['email_reply'] : 'email_reply',
    		'name_reply' 	=> isset($data['name_reply']) ? $data['name_reply'] : 'name_reply',
    		'bcc_list' 		=> isset($data['bcc_list']) && is_array($data['bcc_list']) ? $data['bcc_list'] : [],
    		'attach_files' 	=> isset($data['attach_files']) && is_array($data['attach_files']) ? $data['attach_files'] : [],
			'urlVideo'		=>	isset($data['urlVideo']) ? $data['urlVideo'] : '',
		];

    	if (isset($data['image_attach_objects']) && is_array($data['image_attach_objects'])) {
            foreach ($data['image_attach_objects'] as $key => $image_object) {
            	$image_dir = "s3://$this->bucket_name/$this->folder_name/$mail_item_folder_name" . '/attachments/IMG_' . $key . '.png';
                $image_object->save($image_dir);
                $content['attach_files'] []= $image_dir;
            }
        }
		$status_add_mail_queue = $this->_ci->m_mail_queue->insert([
			'subscriber_id' => $content['subscriber_id'],
			'code'			=> $mail_item_folder_name,
		], TRUE);
		file_put_contents("s3://$this->bucket_name/$this->folder_name/$mail_item_folder_name/content.json", json_encode($content));
    }

    function write_content($item_dir, $content){
    	$content_file = fopen($item_dir . "/content.json", "w") or die("Unable to open file!");
		$content_text = json_encode($content);
		fwrite($content_file, $content_text);
		fclose($content_file);
    }

    function generate_uuid(){
    	return uniqid(date('YmdHis_'), TRUE);
    }

    function create_dir($name){
    	$dir = $this->mail_queue_dir . "/" . $name;

    	if (!file_exists($dir)) {
		    mkdir($dir, 0777, true);
		    mkdir($dir . '/attachments', 0777, true);
		}

		return $dir;
    }

    function get_email_data($limit = 0){
    	$email_data = [];
    	$directories = $this->get_list_item_name($this->mail_queue_dir, $limit);
		if($directories){
			$update = $this->_ci->m_mail_queue->update($directories,['status' => $this->status['sending']]);
			foreach ($directories as $directory) {
				$json_content = file_get_contents("s3://$this->bucket_name/$this->folder_name/$directory/content.json");
				if($json_content){
					$data = json_decode($json_content);
					$data->email_item_dir = $directory;
					$email_data []= $data;
				}else{
					$update = $this->_ci->m_mail_queue->update($directory,['status' => $this->status['folder_not_exist']]);
				}
			}
		}
    	return $email_data;
    }

    function get_list_item_name($dir, $limit = 0){
		// get all mail queue wait send
		$directories = [];
		$records = $this->_ci->m_mail_queue->get_list_filter(array("m.status" => $this->status['unsent']), array(), array(), $limit);
		foreach ($records as $record) {
			$directories[] = $record->code;
		}
    	return $directories;
    }

    function delete_directory($dirname) {
        if (is_dir($dirname))
           $dir_handle = opendir($dirname);
		if (!$dir_handle)
		    return false;
		while($file = readdir($dir_handle)) {
	        if ($file != "." && $file != "..") {
                if (!is_dir($dirname."/".$file))
                     unlink($dirname."/".$file);
                else
                     $this->delete_directory($dirname.'/'.$file);
	        }
	    }
	    closedir($dir_handle);
	    rmdir($dirname);
	    return true;
	}

    function get_mail_and_send($limit = 0){
    	$email_data = $this->get_email_data($limit);
    	foreach ($email_data as $email) {
    		$ret = $this->_ci->email_lib->send_email($email->subject, $email->content, $email->email_to, '', '', $email->attach_files, array(), array(), $email->subscriber_id);
    		if($ret->status){
				$update = $this->_ci->m_mail_queue->update($email->email_item_dir, ['status' => $this->status['sent']]);
				if (isset($update) && $update) {
					$this->delete_directory($this->mail_queue_dir . '/' . $email->email_item_dir);
				}
    		}
			$this->_ci->s3_upload->log_insert(json_encode([
				'status'		=>	$ret->status,
				'data-email'	=>	$email
			], JSON_UNESCAPED_UNICODE), 'mail_queue', $email->event_id);
    	}
    }

	protected function log_insert($result, $event_id, $name_file = 'mail_queue') {
        $folderLog = APPPATH . "logs_new/$event_id";
        if (!is_dir($folderLog)) {
            $old = umask(0);
            mkdir($folderLog, 0755, true);
            umask($old);
        }
		$myFile = APPPATH . "logs_new/$event_id/log-" . $name_file . "-" . date('Y-m-d') . ".txt";
        $fh = fopen($myFile, 'a');
        fwrite($fh, date('Y-m-d H:i:s') . "," . $result . PHP_EOL);
        fclose($fh);
    }
}

