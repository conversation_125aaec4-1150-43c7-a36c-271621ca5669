<?php

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 07/10/2016
 * Time: 4:19 CH
 *
 * @property M_user    m_user
 * @property Email_lib email_lib
 */
class Contact_lib {

    protected $_ci;

    public function __construct() {
        $this->_ci = &get_instance();
        $this->_ci->load->model("m_user");
        $this->_ci->load->library("Email_lib");
    }

    function index($data = []) {
        $save_link = site_url('manager/contact/contact_form_check');
        $data['save_link'] = isset($data['save_link']) ? $data['save_link'] : $save_link;

        $data['send_email_success'] = $this->_ci->load->view("front/contact/send_email_success", $data_success = [], TRUE);
        $html = $this->_ci->load->view("front/contact/contact_form", $data, TRUE);
        $data_return["state"] = 1;
        $data_return["html"] = $html;
        return $data_return;
    }

    public function contact_form_check($data) {
        $data_return["callback"] = 'temple_contact_check';
        $validated = $this->_ci->m_user->contact_validate($data);
        if (!$validated) {
            $data_return["data"] = $data;
            $data_return["state"] = 0;
            $data_return["test"] = $data['email'];
            $data_return["error"] = $this->_ci->m_user->get_validate_error();
            return $data_return;
        }
        $subject = my_lang("【フリッパ】お問い合わせ");
        $this->_ci->config->load('site_settings');
        $admin_email = $this->_ci->config->item('contact_admin_email');
        $admin_bcc_email = $this->_ci->config->item('contact_bcc_admin_email');
        $content = $this->_ci->load->view("email/contact_email", $data, TRUE);
        $content = trim($content);
        $return = $this->_ci->email_lib->send_email(
            $subject, $content,
            $admin_email, $data['email'],
            NULL, NULL,
            NULL, $admin_bcc_email
        );
        if (is_object($return) && !$return->state) {
            $data_return["state"] = 0;
            $data_return["msg"] = my_lang('メールの送信に失敗しました。');
        } else {
            ;
            $data_return["state"] = 1;
            $data_return["msg"] = my_lang('操作が成功しました。');
        }
        return $data_return;
    }
}