#!/bin/sh

iptables -F                     # Flush
iptables -X                     # Reset
iptables -P OUTPUT ACCEPT
iptables -P FORWARD DROP

iptables -A INPUT -i eth0 -m state --state RELATED,ESTABLISHED -j ACCEPT
iptables -A INPUT -s 127.0.0.1 -j ACCEPT

iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

iptables -A INPUT -s *************** -j ACCEPT  #Gisoft IP address
iptables -A INPUT -s ************ -j ACCEPT     #Digra IP address
iptables -A INPUT -s ************* -j ACCEPT    #Flippa Homepage Prod Server
iptables -A INPUT -s ************* -j ACCEPT    #Flippa Organizer Prod Server
iptables -A INPUT -s ************* -j ACCEPT    #Flippa API Prod Server
iptables -A INPUT -s ************** -j ACCEPT   #<PERSON><PERSON><PERSON> DB Prod Server
iptables -A INPUT -s ************** -j ACCEPT   #Flippa Test Server
iptables -A INPUT -s ************/24 -j ACCEPT  #AWS SNS IP address
iptables -A INPUT -s ************/24 -j ACCEPT  #Sakura VPS Monitor IP address
iptables -A INPUT -s *************** -j ACCEPT  #Sakura SSL service IP address

iptables -A INPUT -m limit --limit 1/s -j LOG --log-prefix '[IPTABLES INPUT DROP] : '
iptables -P INPUT DROP
