.privacy-policy-container {
    width: 1000px;
    margin: 50px auto 0px auto;
    padding-bottom: 115px;
}

.privacy-policy-container .title {
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, .3);
    font-size: 35px;
    padding: 24px;
    font-weight: bold;
}
.privacy-policy-container .top{
    margin-top: 20px;
}
.privacy-policy-container .size-top{
    font-size: 8px;
}
.privacy-policy-container .list-privacy{
    margin-top: 10px;
}
.privacy-policy-container .round {
    border-radius: 8px;
    box-shadow: 0px 5px 0px #0aa5df;
}

.privacy-policy-container .round .bg-color{
    text-align: center;
    margin-left: 10px;
    background: #0aa5df;
    width: 15px;
    height: 15px;
    border-radius: 50px;
}
.round .glyphicon{
    color: aliceblue;
    padding: 1px;
    font-size: 10px;
}
.privacy-policy-container .iblock {
    display: inline-block;
}
.privacy-policy-container .text-name {
    font-weight: bold;
    font-size: 10pt;
    margin-left: 5px;
}
.privacy-policy-container .line-menu {
    width: 1000px;
    line-height: 30px;
    border-radius: 1px;
    background: rgba(254,254,254,1);
    background: -moz-linear-gradient(top, rgba(254,254,254,1) 0%, rgba(219,219,219,1) 49%, rgba(209,209,209,1) 49%, rgba(226,226,226,1) 100%);
    background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(254,254,254,1)), color-stop(49%, rgba(219,219,219,1)), color-stop(49%, rgba(209,209,209,1)), color-stop(100%, rgba(226,226,226,1)));
    background: -webkit-linear-gradient(top, rgba(254,254,254,1) 0%, rgba(219,219,219,1) 49%, rgba(209,209,209,1) 49%, rgba(226,226,226,1) 100%);
    background: -o-linear-gradient(top, rgba(254,254,254,1) 0%, rgba(219,219,219,1) 49%, rgba(209,209,209,1) 49%, rgba(226,226,226,1) 100%);
    background: -ms-linear-gradient(top, rgba(254,254,254,1) 0%, rgba(219,219,219,1) 49%, rgba(209,209,209,1) 49%, rgba(226,226,226,1) 100%);
    background: linear-gradient(to bottom, rgba(254,254,254,1) 0%, rgba(219,219,219,1) 49%, rgba(209,209,209,1) 49%, rgba(226,226,226,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fefefe', endColorstr='#e2e2e2', GradientType=0 );
}
.privacy-policy-container .section{
     width: 960px;
     margin-right: auto;
     margin-left: auto;
     margin-top: 10px;
     margin-bottom: 10px;
 }
.privacy-policy-container .section p{
    float: left;
}
.privacy-policy-container .section h3{
    border-left-width: 4px;
    border-left-style: solid;
    border-left-color: #57a9fc;
    padding-left: 10px;
    font-weight: bolder;
    margin-bottom: 10px;
    margin-top: 20px;
    border-bottom-width: 1px;
    border-bottom-style: dotted;
    border-bottom-color: #CCC;
    font-size: 110%;
}
.privacy-policy-container .section .p-top{
    float: right;
}
.privacy-policy-container .list-content{
     width: 960px;
     margin-right: auto;
     margin-left: auto;
     margin-top: 90px;
     margin-bottom: 10px;
 }
.privacy-policy-container .kc{
    margin-top: 140px;

}
.privacy-policy-container .list-content p{
    float: left;
}
.privacy-policy-container .list-content h3{
    border-left-width: 4px;
    border-left-style: solid;
    border-left-color: #57a9fc;
    padding-left: 10px;
    font-weight: bolder;
    margin-bottom: 10px;
    margin-top: 20px;
    border-bottom-width: 1px;
    border-bottom-style: dotted;
    border-bottom-color: #CCC;
    font-size: 110%;
}
.privacy-policy-container .list-content .p-sign{
    padding-top: 100px;
    text-align: right;
    line-height: 2em
}
.privacy-policy-container .text-description{
    margin-top: 30px;
}
.privacy-policy-container .list-privacy1{
    margin-top: 60px;
    margin-bottom: 20px;
}

@media (min-width: 320px) and (max-width: 768px) { 
    .text-container .title_box {
        border: 2px solid #9A9A9A;
        font-size: 18px;
        height: 47px;
        margin-top: 48px;
        text-align: center;
        padding-top: 5px;
        min-width: 377px;
        display: inline-block;
        padding: 5px 20px 0px;
        font-family: "Yu go";
    }
}