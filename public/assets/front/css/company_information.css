.container.company-info-container {
    background: #fff;
    border-radius: 40px;
    padding: 40px 64px 0 64px;
}

.wrapTextInfor .title {
    color: #999999;
    font-size: 18px;
    font-weight: bold;
}

.wrapTextInfor .content {
    color: #111111;
    font-size: 18px;
    font-weight: bold;
}
.anchor_link-box {
    margin-top: 40px;
}
@media (min-width: 768px) {
    .company-info-container {
        margin-top: 64px !important;
        margin: 50px auto 50px auto;
        padding: 8px;
    }

    .textContent h3 {
        font-family: "Yu go";
        border-bottom: 1px solid rgba(0, 0, 0, .3);
        height: 47px;
        font-size: 22px;
        font-weight: bold;
        line-height: 47px;
        margin-left: 6px;
        margin: 10px 0px;
        padding: 0;
    }

    .textContent h3::before {
        content: " ";
        border-bottom: 7px solid transparent;
        border-top: 7px solid transparent;
        /*border-left: 13px solid #FEE200;*/
        font-size: 1px;
        display: inline-block;
        padding-right: 10px;
    }

    .company-info-container .title {
        font-family: "Yu Go";
        text-align: center;
        border-bottom: 1px solid rgba(0, 0, 0, .3);
        font-size: 40px;
        padding: 0 24px 40px 24px;
        font-weight: bold;
    }
    .company-info-container .text-top li{
        float: left;
        margin-right: 5px;
        margin-bottom: 10px;
    }
    .company-info-container li {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    .company-info-container .text-top li a{
        float: left;
        background-image: url(../../images/arrow03.gif);
        background-repeat: no-repeat;
        background-position: right center;
        padding-right: 10px;
    }
    .company-info-container .container-content{
        width: 100%;
        padding-bottom: 18px;
        clear: both;
        margin-top: 0;
        margin-right: auto;
        margin-bottom: 0;
        margin-left: auto;
    }
    .company-info-container .container-content h1{
        background-image: url(../../images/h1_bg.gif);
        background-repeat: no-repeat;
    }
    .company-info-container h1{
        height: 35px;
        width: 100%;
        font-weight: bolder;
        padding-top: 5px;
        padding-left: 25px;
        clear: both;
    }

    .company-info-container .wrapTextInfor {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #ccc;
    }

    .company-info-container .wrapTextInfor .title {
        font-family: "Yu go";
        text-align: left;
        font-size: 20px;
        padding: 24px 24px 24px 0;
        font-weight: bold;
        border-bottom: 0;
        min-width: 200px;
    }

    .company-info-container .wrapTextInfor .content {
        font-family: "Yu go";
        text-align: left;
        font-size: 18px;
        padding: 24px;
        width: 70%;
    }

    .company-info-container p.gotop {
        float: right;
        clear: both;
        padding-top: 0px;
        padding-bottom: 20px;
        padding-right: 20px;
    }
    .company-info-container p {
        text-align: justify;
    }
    .company-info-container dl.recruitment-info {
        width: 70%;
        margin-right: auto;
        margin-left: auto;
        margin-top: 20px;
        margin-bottom: 20px;
        clear: both;
    }
    .company-info-container dl.recruitment-info dt {
        float: left;
        width: 5em;
        padding-top: 0px;
        padding-bottom: 0;
        padding-right: 0;
        font-weight: bolder;
        border-right-width: 1px;
        border-right-style: solid;
        border-right-color: #CCC;
        margin-right: 10px;
    }
    .company-info-container dl.recruitment-info dd {
        width: 78em;
        padding-top: 0px;
        padding-bottom: 10px;
        padding-right: 10px;
    }
    .company-info-container #w960 {
        width: 960px;
        margin-right: auto;
        margin-left: auto;
        margin-top: 10px;
        margin-bottom: 60px;
        clear: both;
    }
    .company-info-container .right {
        float: right;
        display: inline-block;
    }

    .company-info-container .mB30 {
        margin-bottom: 30px;
    }
    .company-info-container small {
        font-size: smaller;
    }
    .company-info-container p {
        text-align: justify;
    }
    .company-info-container strong, em {
        font-weight: bold;
    }
    .company-info-container .mT20 {
        margin-top: 20px;
    }
}
@media (min-width: 320px) and (max-width: 768px) {
    .company-info-container {
        margin: 50px auto 50px auto;
        padding: 16px 16px 0 16px !important;
    }

    .anchor_link-box {
        margin-top: 10px;
    }

    .company-info-container .wrapTextInfor {
        display: block !important;
        padding: 20px 0 !important;
    }

    .textContent h3 {
        border-bottom: 1px dotted black;
        height: 47px;
        font-size: 16px;
        font-weight: bold;
        line-height: 47px;
        margin-left: 6px;
        margin: 10px 0px;
        padding: 0;
    }

    .textContent h3::before {
        content: "";
        border-bottom: 7px solid transparent;
        border-top: 7px solid transparent;
        border-left: 13px solid #FEE200;
        font-size: 1px;
        display: inline-block;
        padding-right: 10px;
    }

    .company-info-container .title {
        font-family: "Yu Go";
        text-align: center;
        border-bottom: 1px solid rgba(0, 0, 0, .3);
        font-size: 26px;
        padding: 12px 24px 24px 24px;
        font-weight: bold;
    }
    .company-info-container .text-top li{
        float: left;
        margin-right: 5px;
        margin-bottom: 10px;
    }
    .company-info-container li {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    .company-info-container .text-top li a{
        float: left;
        background-image: url(../../images/arrow03.gif);
        background-repeat: no-repeat;
        background-position: right center;
        padding-right: 10px;
    }
    .company-info-container .container-content{
        width: 100%;
        padding-bottom: 18px;
        clear: both;
        margin-top: 0;
        margin-right: auto;
        margin-bottom: 0;
        margin-left: auto;
    }
    .company-info-container .container-content h1{
        background-image: url(../../images/h1_bg.gif);
        background-repeat: no-repeat;
    }
    .company-info-container h1{
        height: 35px;
        width: 100%;
        font-weight: bolder;
        padding-top: 5px;
        padding-left: 25px;
        clear: both;
    }

    .company-info-container .wrapTextInfor {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #ccc;
        padding-bottom: 8px;
    }

    .company-info-container .wrapTextInfor .title {
        font-family: "Yu go";
        text-align: left;
        font-size: 18px;
        font-weight: bold;
        border-bottom: 0;
        /*width: 30%;*/
        padding: 10px 0 0 0;

    }

    .company-info-container .wrapTextInfor .content {
        font-family: "Yu go";
        text-align: left;
        font-size: 16px;
        padding: 24px;
        width: 70%;
        padding: 10px 0 0 0;

    }

    .company-info-container .wrapTextInfor .title {
        font-size: 16px;
    }

    .company-info-container p.gotop {
        float: right;
        clear: both;
        padding-top: 0px;
        padding-bottom: 20px;
        padding-right: 20px;
    }
    .company-info-container p {
        text-align: justify;
    }
    .company-info-container dl.recruitment-info {
        width: 70%;
        margin-right: auto;
        margin-left: auto;
        margin-top: 20px;
        margin-bottom: 20px;
        clear: both;
    }
    .company-info-container dl.recruitment-info dt {
        float: left;
        width: 5em;
        padding-top: 0px;
        padding-bottom: 0;
        padding-right: 0;
        font-weight: bolder;
        border-right-width: 1px;
        border-right-style: solid;
        border-right-color: #CCC;
        margin-right: 10px;
    }
    .company-info-container dl.recruitment-info dd {
        width: 78em;
        padding-top: 0px;
        padding-bottom: 10px;
        padding-right: 10px;
    }
    .company-info-container #w960 {
        width: 960px;
        margin-right: auto;
        margin-left: auto;
        margin-top: 10px;
        margin-bottom: 60px;
        clear: both;
    }
    .company-info-container .right {
        float: right;
        display: inline-block;
    }

    .company-info-container .mB30 {
        margin-bottom: 30px;
    }
    .company-info-container small {
        font-size: smaller;
    }
    .company-info-container p {
        text-align: justify;
    }
    .company-info-container strong, em {
        font-weight: bold;
    }
    .company-info-container .mT20 {
        margin-top: 20px;
    }
}