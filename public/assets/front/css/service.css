.merit {
    position: relative;
}

.textUnder {
    position: absolute;
    bottom: -50px;
    right: 26.5%;
    font-size: 13px;
    word-break: break-all;
}
.wrapButton .e_ajax_link {
    position: relative;
}


@media (max-width: 576px) {
    .textUnder {
        right: 0%;
        width: 49%;
    }
}

@media (min-width: 320px) and (max-width: 991px) {
    #bannerSp {
        width: 100%;
        margin-bottom: 10%;
    }

    #bannerSp img {
        width: 100%;
    }

    #bannerPc {
        display: none;
    }

    #screenPc {
        display: none;
    }

    #smartPhone {
        text-align: center;
        margin: 40px auto 0;
        padding-bottom: 10px;
        border-radius: 10px;
    }

    #smartPhone .wrapSp {
        background-color: #ebfbff;
        padding: 10px;
    }

    #smartPhone h1 {
        font-size: 15px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #333333;
        font-weight: bolder;
        font-family: "Yu Go";
        text-align: center;
        margin: 5px auto 0px;
    }

    #smartPhone p {
        font-size: 13px;
        letter-spacing: 2px;
        line-height: 20px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
        margin: 0;
    }

    .title {
        width: 90%;
        margin: 50px auto 0;
    }

    .title p {
        font-size: 13px;
        margin: 0;
    }

    .title .wrap {
        margin-left: 20px;
    }

    .title .wrap p {
        margin: 0;
        font-weight: 500;
    }

    .title span {
        font-size: 13px;
        letter-spacing: 1px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        position: relative;
    }

    .title .horizontal::before {
        content: "";
        width: 15px;
        height: 2px;
        position: absolute;
        top: 7px;
        left: -33px;
        background-image: linear-gradient(to right, #00ccff 20%, #fff200 30%);
    }

    .title .usage {
        font-size: 30px;
        letter-spacing: 2px;
        color: #333333;
        font-weight: 300;
        font-family: "D IN Condensed";
        margin: 0;
    }

    .title .security {
        background-color: #ffbc00;
        color: #ffff;
        padding: 10px 25px;
        border: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-radius: 50px;
        margin: 25px 0 15px;
    }

    .title .security img {
        width: 6%;
    }

    .title .security span {
        font-size: 13px;
        letter-spacing: 2px;
        color: #ffffff;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
        width: 100%;
    }

    .pattern {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-top: 100px;
        margin: 0 auto;
        padding-top: 55px;
    }

    .pattern .item {
        width: 48%;
        background-color: #ebfbff;
        text-align: center;
        position: relative;
        padding-top: 50px;
    }

    .pattern .item .textSmallSp {
        font-size: 13px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
        padding: 0px 27px 5px;
        margin: 0;
    }

    .pattern .item .textSmallPc {
        display: none;
    }

    .pattern .item .circle {
        width: 70px;
        height: 70px;
        border-radius: 50px;
        background-color: #ffbc00;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffff;
        margin: 0;
        position: absolute;
        top: -1%;
        left: 50%;
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }

    .pattern .item .title1 {
        font-size: 22px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
    }

    .pattern .item p {
        font-size: 13px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
        padding: 0px 27px 5px;
        margin: 0;
        line-height: 20px;
    }

    .pattern .item .circle span {
        font-size: 13px;
        color: #ffffff;
        font-family: "D IN Condensed";
        line-height: 25px;
    }

    .pattern .item .circle .number {
        font-size: 40px;
        color: #ffffff;
        font-family: "D IN Condensed";
    }

    .content {
        margin-top: 50px;
        font-size: 22px;
        letter-spacing: 2px;
        line-height: 32px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
    }

    .buttonDocument {
        width: 90%;
        display: block;
        margin: 0 auto;
    }

    .buttonDocument .wrapButton {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 15px auto;
    }

   

    .wrapButton .e_ajax_link::after {
        width: 25px;
    }


    .buttonDocument button {
        margin: 0 auto;
        width: 250px;
        height: 50px;
        border-radius: 40px;
        background-color: #ffbc00;
        margin: auto 20px;
        font-size: 13px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #ffffff;
        font-weight: bold;
        font-family: "Yu Go";
        text-align: center;
        border: 1px;
        cursor: pointer;
    }

    .descriptionText {
        margin: 0px auto 60px;
        width: 90%;
    }

    .descriptionText img {
        width: 100%;
        margin: 10px 0;
    }

    .twoImg {
        display: flex;
        width: 100%;
        justify-content: space-between;
    }

    .twoImg img {
        width: 49%;
    }
}

@media (min-width: 992px) {
    #bannerPc {
        width: 100%;
        margin-bottom: 80px;
        position: relative;
    }

    #bannerPc .wrapSpan {
        position: absolute;
        font-family: "Yu go";
        top: 50%;
        left: 50%;
        color: #fff;
        font-size: 52px;
        transform: translate(-50%, -50%);
    }

    #bannerPc img {
        width: 100%;
    }

    #bannerSp {
        display: none;
    }

    #screen {
        display: none;
    }

    #screenPc {
        background-color: #ebfbff;
        text-align: center;
        padding-bottom: 40px;
        border-radius: 10px;
    }

    #screenPc h1 {
        font-size: 28px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #333333;
        font-weight: bold;
        font-family: "Yu Go";
        text-align: center;
        padding-top: 40px;

    }

    #screenPc p {
        font-size: 22px;
        letter-spacing: 2px;
        line-height: 32px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
        margin: 0;
    }

    #smartPhone {
        display: none;
    }

    .title {
        width: 100%;
        margin-top: 80px;
    }

    .title p {

        font-size: 20px;
        letter-spacing: 3px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        margin: 0;
    }

    .title .wrap {
        margin-left: 40px;
    }

    .title span {
        font-size: 18px;
        letter-spacing: 1px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        position: relative;
    }

    .title .horizontal::before {
        content: "";
        width: 20px;
        height: 2px;
        position: absolute;
        top: 7px;
        left: -33px;
        background-image: linear-gradient(to right, #00ccff 30%, #fff200 30%);
    }

    .title .usage {
        font-size: 64px;
        letter-spacing: 2px;
        color: #333333;
        font-weight: 300;
        font-family: "D IN Condensed";
        margin: 0;
    }

    .pattern {
        width: 100%;
        display: flex;
        justify-content: space-between;

        margin: 65px auto 0;
    }

    .pattern .item {
        width: 49%;
        background-color: #ebfbff;
        text-align: center;
        position: relative;
        padding-top: 50px;
        padding-bottom: 30px;
    }

    .pattern .item .circle {
        margin: 0 auto;
        position: absolute;
        width: 100px;
        height: 100px;
        border-radius: 50px;
        background-color: #ffbc00;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffff;
        margin: 0;
        position: absolute;
        top: -1%;
        left: 50%;
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }

    .pattern .item .title1 {
        font-size: 22px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
    }

    .pattern .item p {
        font-size: 22px;
        letter-spacing: 1px;
        line-height: 10px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu go";
        text-align: center;
        padding-top: 30px;
        margin: 0;
        line-height: 20px;
    }
    .pattern .item .textSmall {
        font-size: 18px;
    }

    .pattern .item .textSmallPc {
        margin: 0;
        font-size: 18px;
        letter-spacing: 1px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
        padding-bottom: 40px;
    }

    .pattern .item .textSmallSp {
        display: none;
    }

    .pattern .item .circle span {
        font-size: 22px;
        color: #ffffff;
        font-family: "D IN Condensed";
        line-height: 45px;
    }

    .pattern .item .circle .number {
        font-size: 62px;
        color: #ffffff;
        font-family: "D IN Condensed";
        line-height: 30px;
    }

    .content {
        margin-top: 50px;
        font-size: 22px;
        letter-spacing: 2px;
        line-height: 32px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
    }

    .security {
        width: 326px;
        height: 60px;
        border-radius: 30px;
        background-color: #ffbc00;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        border: 0;
        margin-bottom: 15px;
    }

    .security img {
        width: 10%;
        margin-right: 3%;
    }

    .security span {
        font-size: 20px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #ffffff;
        font-weight: 500;
        font-family: "Yu Go";
        text-align: center;
    }

    .contentSecurity {
        margin-top: 20px;
        font-size: 20px;
        letter-spacing: 2px;
        line-height: 32px;
        color: #333333;
        font-weight: 500;
        font-family: "Yu Go";
    }

    .buttonDocument {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 80px;
    }

    .buttonDocument button {
        width: 386px;
        height: 80px;
        border-radius: 40px;
        background-color: #ffbc00;
        margin: auto 20px;
        font-size: 22px;
        letter-spacing: 2px;
        line-height: 26px;
        color: #ffffff;
        font-weight: bold;
        font-family: "Yu Go";
        text-align: center;
        margin-top: 60px;
        border: 1px;
        cursor: pointer;
    }

    .descriptionText {
        font-size: 15px;
        margin: 0px auto;
        width: 85%;
        padding: 0px 50px;
        margin-bottom: 70px;

    }

    .descriptionText img {
        width: 100%;
        margin: 10px 0;
        cursor: pointer;
    }

    .twoImg {
        display: flex;
        width: 100%;
        justify-content: space-between;
    }

    .twoImg img {
        width: 49%;
        cursor: pointer;
    }
}

.buttonDocument .wrapButton a:hover {
    background-color: transparent;
}

.title ul li{
    margin-bottom: 15px;
}