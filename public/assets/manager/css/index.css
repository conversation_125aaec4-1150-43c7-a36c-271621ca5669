body {
    font-family: "Noto Sans CJK JP", sans-serif;
    background-color: #FDBC00;
    font-size: 14px;
}

@font-face {
    font-family: "MS PGothic";
    src: url("../front/fonts/MS\ PGothic.ttf");
}

@font-face {
    font-family: "Yu go";
    src: url("../front/fonts/Yu\ Gothic\ Medium.otf");
}

@font-face {
    font-family: 'Hiragino Pro W3';
    src: url('../front/fonts/hiragino-mincho-pro-w3.otf');
}

@font-face {
    font-family: 'DIN-Special';
    src: url('../front/fonts/DIN-BlackAlternate.ttf') format('woff2'),
    url('../front/fonts/DIN-BlackAlternate.ttf') format('woff');
}

@font-face {
    font-family: 'DIN-Light';
    src: url('../front/fonts/DIN-Light.ttf') format('woff2'),
    url('../front/fonts/DIN-Light.ttf') format('woff');
}

@font-face {
    font-family: 'DIN-Bold';
    src: url('../front/fonts/DIN-Bold.ttf') format('woff2'),
    url('../front/fonts/DIN-Bold.ttf') format('woff');
}

@font-face {
    font-family: 'DIN-Medium';
    src: url('../front/fonts/DIN-Medium.ttf') format('woff2'),
    url('../front/fonts/DIN-Medium.ttf') format('woff');
}

@font-face {
    font-family: 'D IN Condensed';
    src: url('../front/fonts/637-font.otf') format('woff2'),
    url('../front/fonts/637-font.otf') format('woff');
}

.no-bold {
    font-weight: normal;
}

table.background-write {
    background-color: #ffffff;
}

.manager_table_scroll {
    overflow-x: scroll;
    width: 100%;
}

textarea {
    resize: none;
}

.text-bold {
    font-weight: bold;
}

.margin-top-15 {
    margin-top: 15px;
}

.iblock {
    display: inline-block;
    /*vertical-align: top;*/
}
.w-maxcontent {
    width: max-content !important;
}
.h-maxcontent {
    height: max-content !important;
}
.max-w-unset {
    max-width: unset !important;
}
.max-w-800{
    max-width: 800px;
}
.text-red {
    color: red;
}
.text-442A1D {
    color: #442A1D;
}
.min-h-48 {
    min-height: 48px !important;
}
@media (min-width: 1024px) {
    .w-xl-maxcontent {
        width: max-content !important;
    }
    .fw-xl-normal {
        font-weight: 500 !important;
    }
    .d-xl-flex {
        display: flex !important;
    }
    .d-xl-block {
        display: block !important;
    }
    .d-xl-none {
        display: none !important;
    }
    .flex-xl-row {
        flex-direction: row !important;
    }
    .flex-lg-column {
        flex-direction: column !important;
    }
    .w-xl-50 {
        min-width: 50%;
    }
    .justify-content-xl-end {
        justify-content: end;
    }
    .align-items-xl-center {
        align-items: center;
    }
    .w-xl-50 {
        width: 50%;
    }
    .text-xl-end {
        text-align: end !important;
    }
    .mb-lg-3{
        margin-bottom: 1.5rem !important;
    }
    .mb-xl-5 {
        margin-bottom: 3rem !important;
    }
    .mb-xl-44 {
        margin-bottom: 44px !important;
    }
    .p-xl-5 {
        padding: 3rem !important;
    }
}
.form-over.event_container {
    width: auto;
}

/********* form area *********/

.event_container .form_area {
    width: 100%;
    margin: 30px auto 0px auto;
    font-size: 15px;
    border: none;
}

.form-over.event_container .form_area {
    width: auto;
}

.event_container .form_area .guide_text {
    line-height: 25px;
}

.event_container .form_area .form_box {
    width: 100%;
    border: 8px solid #ECECEC;
    margin-top: 25px;
    padding-bottom: 40px;
}

.event_container .form_area .form_box.ticket-form {
    margin-top: 0px;
}

.col-xs-5.no-gap-col.no_noted_input {
    line-height: 1px;
}

#i_form_confirm .application_form_conf .input_title {
    margin-bottom: 0;
}

.event_container .form_area .form_box_content .input_row.sort_field_row {
    position: relative;
}

.sort_field {
    position: absolute;
    top: calc(50% - 12px);
    left: -40px;
    background-color: #F5F5F5;
    border-radius: 8px;
    padding: 10px 8.5px;
    flex-direction: column;
    width: max-content;
}

.sort_field .glyphicon:hover {
    color: #FDBC00;
    cursor: pointer;
}

.form_box .form_box_title {
    width: 100%;
    padding: 15px 36px;
    background-color: #ECECEC;
    font-size: 21px;
    font-weight: bold;
}

.form_box_content {
    margin: 19px auto 0px auto;
    width: 92%;
}

.friend_ticket_content {
    margin-top: 20px;
    padding: 15px 0px;
    border-bottom: 1.25px solid #929292;

}

.friend-ticket-detail-container {
    display: none;
}

.form_box_content > form > .last_row {
    padding: 15px;
}

.no-outline:focus {
    outline: none;
}

.bottom-border-none {
    border-bottom: none !important;
}

.soft_hide {
    display: none;
}

#i_content_mail_e_ticket {
    width: 75%;
}

.main-container.body-container {
    margin: 0 auto;
    width: 100%;
}
@media (min-width: 1024px) {
    .main-container.body-container {
        margin-bottom: 80px;
    }
    .main-container.body-container.has-manager-login {
        margin-bottom: 0;
    }
}

.margin_none_b {
    margin-bottom: 0px !important;
}

.manager-container, .navbar-container {
    width: 1000px;
    margin: 0px auto;
    background-color: white;
}

.footer-box {
    height: 120px;
    padding: 28px 24px;
    display: flex;
    justify-content: flex-start;
    gap: 40px;
    align-items: center;
}

.footer-relative {
    display: flex;
    align-items: center;
    gap: 16px;
}
.top-title{
    font-size: 18px;
    font-weight: bold;
    color: #000000;
}
.back-to-top{
    background: #ffffff;
    padding: 13px 16px;
    border-radius: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
.footer-logo{
    height: 65px;
    width: 65px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #FFFFFF;
    border-radius: 16px;
}
.box-link{
    display: flex;
    gap: 28px;
}
.box-link a{
    text-decoration: none;
    font-size: 14px;
    color: #000000;
}
.divider {
    width: 1px;
    height: 64px;
    background-color: white;
}
.manager-container .footer-text {
    position: absolute;
    bottom: 0;
    right: 0px;
    left: 50px;
    top: 0;
    padding-left: 10px;
}

.footer-text > div {
    font-size: 12px;
}

.footer-list {
    -webkit-padding-start: 0;
    -moz-padding-start: 0;
    display: inline-flex;
    padding-left: 0;
    list-style-type: none;
    margin-bottom: 0px;
    margin-left: -5px;
}

.footer-list > li {
    /* padding-right: 35px; */
    /* padding-top: 5px; */
    list-style-type: square;
    list-style-position: inside;
    margin-right: 5px;
    /* border-radius: 5px; */
    margin-top: 13px;
    padding-left: 5px;
    white-space: nowrap;
}

.manager-container .footer-list > li:hover, .footer-list > li:active {
    background-color: #fbe52e;
}

.manager-container .footer-list > li > a {
    color: #6b6b6b;
    text-decoration: none;
    padding: 3px 35px 2px 0;
    /*display: block;*/
    margin-left: -8px;
}

.page-top-button {
    position: absolute;
    bottom: 22px;
    right: 0px;
    background-color: #ffffff;
    line-height: 23px;
}

.page-top-button:hover {
    background-color: #FFFFE0;
}

.page-top-button .page-top-a {
    font-size: 12px;
    padding: 3px 6px;
    color: #3a3a3a;
    text-decoration: none;
    text-transform: uppercase;
    border: 1px solid #c0c0c0;
}

.page-top-button .page-top-a:focus {
    outline: none;
}

.page-top-a .glyphicon.glyphicon-triangle-top {
    color: #ffae00;
    padding-right: 10px;
}

.header-container {
    position: relative;
    height: 115px;
}

.header-logo {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0px;
    right: calc(100% - 153px);
    padding-top: 21px;
    padding-bottom: 21px;
}

.header-text {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 153px;
    right: 0px;
}

.header-row {
    height: 100%;
    margin-right: 0px;
    font-size: 12px;
}

.header-text-left {
    padding-top: 42px;
}

.header-text-right {
    line-height: 109px;
}

.header-p {
    margin-bottom: 2px;
}

.header-navbar {
    background-color: #e9e8e4;
    border-bottom: #d5d5d5 5px solid;
    border-top: solid 1px #e9e8e4;
    min-width: 1000px;
}

.no-border-important {
    border: none !important;
}

.header-menu {
    width: 100%;
    background-color: #e9e8e4;
    white-space: nowrap;
    margin: 0;
    display: flex;
}

.header-menu > li {
    height: 60px;
    display: inline-flex;
}

.header-menu > li.border-navbar {
    background-color: #ccc;
    width: 1px;
    height: 22px;
    margin: 19px 0;
}

.header-menu > li > a {
    line-height: 30px;
    font-weight: 600;
    padding: 10px 18px;
    color: #3a3a3a;
    font-size: 14px;
}

.header-menu > li > a:focus, .header-menu > li > a:hover {
    background-color: transparent;
}

.header-hover-bg {
    background-color: transparent;
    border: 2px solid transparent;
    border-radius: 7px;
    padding: 5px 7px;
}

.header-menu > li > a.active .header-hover-bg,
.header-menu > li > a:hover .header-hover-bg {
    border: 2px solid #ffe000;
    border-radius: 7px;
    padding: 5px 7px 2px;
}

.header-menu > li:first-child > a {
    padding-left: 0px;
}

.header-menu > li:last-child > a {
    padding-right: 0px;
}

.bg-white {
    background-color: white;
}

.text-container {
    width: 1000px;
    margin: 50px auto 0px auto;
    padding-bottom: 115px;
}

.text-container .title {
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, .3);
    padding: 24px;
    font-weight: bold;
}

.text-container .title_box {
    border: 2px solid #9A9A9A;
    font-size: 24px;
    /* font-weight: bold; */
    height: 47px;
    margin-top: 48px;
    text-align: center;
    padding-top: 5px;
    min-width: 377px;
    display: inline-block;
    padding: 5px 20px 0px;
}

.text-container .sub_title_box {
    border-bottom: 1px dotted black;
    height: 47px;
}

.text-container h3 {
    border-bottom: 1px dotted black;
    height: 47px;
    font-size: 17px;
    font-weight: bold;
    line-height: 47px;
    margin-left: 6px;
    margin: 10px 0px;
    padding: 0;
}

.text-container h3:before {
    content: " ";
    border-bottom: 7px solid transparent;
    border-top: 7px solid transparent;
    border-left: 13px solid #FEE200;
    font-size: 1px;
    display: inline-block;
    padding-right: 10px;
}

.text-container h3 + p {
    line-height: 32px;
    margin-left: 58px;
    margin-bottom: 20px;
}

.text-container .title_description {
    margin: 20px 0px 10px 0px;
    line-height: 30px;
}

.text-container p .more_space {
    width: 40px;
    display: inline-block;
}

.text-container .sub_title_box .triangle {
    border-bottom: 7px solid transparent;
    border-top: 7px solid transparent;
    border-left: 13px solid #FEE200;
}

.text-container .sub_title_box .text {
    font-size: 17px;
    font-weight: bold;
    line-height: 47px;
    margin-left: 6px;
}

.pre_white_space {
    white-space: pre-line;
}

.manager-container .text-line {
    border-bottom: 1px solid rgba(0, 0, 0, .3);
    margin-left: 102px;
}

.manager-container .clear {
    clear: both;
}

.manager-container .search-button {
    font-size: 14px;
    vertical-align: middle;
    position: relative;
    margin-left: 5px;
    padding: 0px 10px;
    height: 30px;
    line-height: 30px;
    color: #ffffff;
    border: none;
    border-bottom: 1px solid #6f6c64;
    border-right: 1px solid #6f6c64;
    background: rgba(152, 148, 139, 1);
    background: -moz-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
    background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(152, 148, 139, 1)), color-stop(100%, rgba(128, 123, 112, 1)));
    background: -webkit-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
    background: -o-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
    background: -ms-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
    background: linear-gradient(to bottom, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#98948b', endColorstr='#807b70', GradientType=0);
    -webkit-box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);
    -moz-box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);
    box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);
}

.manager-container .search-button:hover {
    background: rgba(87, 89, 91, 1);
    background: -moz-linear-gradient(top, rgba(87, 89, 91, 1) 0%, rgba(140, 140, 140, 1) 100%);
    background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(87, 89, 91, 1)), color-stop(100%, rgba(140, 140, 140, 1)));
    background: -webkit-linear-gradient(top, rgba(87, 89, 91, 1) 0%, rgba(140, 140, 140, 1) 100%);
    background: -o-linear-gradient(top, rgba(87, 89, 91, 1) 0%, rgba(140, 140, 140, 1) 100%);
    background: -ms-linear-gradient(top, rgba(87, 89, 91, 1) 0%, rgba(140, 140, 140, 1) 100%);
    background: linear-gradient(to bottom, rgba(87, 89, 91, 1) 0%, rgba(140, 140, 140, 1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#57595b', endColorstr='#8c8c8c', GradientType=0);
}

.manager-container .search-button:focus {
    outline: none;
}

.manager-container .search-button .login-text {
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0;
    right: 14px;
}

.manager-container .search-button > span.glyphicon {
    position: absolute;
    top: 12px;
    right: 7px;
    font-size: 7px;
    color: #ffe400;
}

.manager-container .input-select {
    height: 30px;
    width: 150px;
}
.input-search-topbar {
    width: 100%;
    height: 64px;
    border-radius: 16px;
    font-size: 16px;
}

.manager-container .input-search {
    height: 30px;
    width: 170px;
    margin-left: 10px;
    padding-left: 5px;
}

.submit-search-button {
    width: 122px;
    height: 64px;
    border-radius: 40px;
    background-color: black;
    color: white;
    text-align: center;
    font-size: 16px;
}

.logout-button {
    text-decoration: none;
    color: black;
    width: 160px;
    height: 64px;
    border: 4px solid black;
    border-radius: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.manager-container .kc-top {
    margin-top: 10px;
}

.manager-container .idisplay {
    display: inline-block;
    vertical-align: top;
}

.navbar-container .manager-icon5 {
    background-image: url("../manager/images/icon_question.png");
    width: 21px;
    height: 21px;
}

.navbar-container .manager-icon1 {
    background-image: url("../manager/images/manager_icon1.png");
    width: 24px;
    height: 22px;
}

.navbar-container .manager-icon2 {
    background-image: url("../manager/images/dark_list_icon.png");
    width: 20px;
    height: 18px;
}

.navbar-container .manager-icon3 {
    background-image: url("../manager/images/manager_icon3.png");
    width: 16px;
    height: 22px;
}

.navbar-container .manager-icon4 {
    background-image: url("../manager/images/manager_icon4.png");
    width: 23px;
    height: 18px;
    background-size: 23px 18px;
}

.navbar-container .manager-icon6 {
    background-image: url("../manager/images/icon_support.png");
    width: 21px;
    height: 21px;
}

.navbar-container .manager-icon7 {
    background-image: url("../manager/images/icon_user.png");
    width: 21px;
    height: 21px;
}

.navbar-container .icon-manager {
    width: 21px;
    height: 21px;
}

.navbar-container .bg-icon {
    margin-right: 5px;
    margin-top: 3px;
    background-repeat: no-repeat;
}

.navbar-container .bg-display {
    display: flex;
}

.right_logo_header {
    position: relative;
    padding-right: 0px;
    right: 0px;
    top: 13px;
}

.upper_logo_header {
    height: 50px;
    padding-right: 0px;
    padding-top: 10px;
}

.message-loading-overlay {
    z-index: 9998;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: rgba(136, 136, 136, 0.41);
    cursor: progress;
    text-align: center;
}

.message-loading-overlay > .ace-icon {
    position: absolute;
    top: 15%;
    left: 0;
    right: 0;
    text-align: center;
}

.message-loading-overlay .big-spin {
    top: 50%;
    margin-top: -40px;
    height: 50px;
    width: 50px;
    left: 50%;
    margin-left: -25px;
    font-size: 50px;
}

/*.message-loading-overlay .fa-spin {*/
/*-webkit-animation: fa-spin 2s infinite linear;*/
/*animation: fa-spin 2s infinite linear;*/
/*}*/

/*.message-loading-overlay .fa {*/
/*display: inline-block;*/
/*font: normal normal normal 14px/1 FontAwesome;*/
/*text-rendering: auto;*/
/*-webkit-font-smoothing: antialiased;*/
/*-moz-osx-font-smoothing: grayscale;*/
/*font-size: 50px;*/
/*}*/

/*.message-loading-overlay .fa-refresh:before {*/
/*content: "\f021";*/
/*}*/

.little-box-title {
    border-bottom: 1px dotted #000000;
    height: 50px;
    line-height: 54px;
    position: relative;
    padding-left: 24px;
    font-size: 16px;
}

.little-box-title span.arrow-left {
    padding-right: 5px;
    position: absolute;
    top: calc(50% - 7px);
    left: 0;
}

.arrow-left {
    font-size: 0px;
    border-left: 12px solid #ffe400;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
}

/*Modal*/

@media only screen and (min-width: 1000px) {
    .manager-big-modal.modal-dialog {
        width: 924px;
    }

    .success-msm {
        padding: 35px 150px;
        font-size: 15px;
    }
}

@media only screen and (max-width: 1000px) {
    #i_event_fee_item .event-fee-btn-container{
        margin: 30px 0 20px 0;
        width: 100%;
    }
    #i_event_fee_item .event-fee-btn-note{
        max-width: 100px;
        top: -5px;
    }
}
@media only screen and (max-width: 644px) {
    #i_event_fee_item .event-fee-btn-note{
        max-width: 100%;
        position: inherit;
        text-align: center;
    }
}

.success-msm {
    padding: 35px 40px;
    font-size: 15px;
}

.medium-modal.modal-dialog {
    width: 655px;
}

.manager-modal .modal-content, .rule_modal .modal-content {
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    border-radius: 40px !important;
}

@media (max-width: 768px) {
    .notification_modal {
        width: 85%;
        margin-top: 50% !important;
    }
}

.header-modal {
    display: flex;
    justify-content: end;
    position: relative;
    right: 8px;
}

.manager-modal .modal-header .close, .modal-header .close.rule-modal-close {
    position: absolute;
    top: 25px;
    right: 25px;
    font-size: 20px;
    margin: 0;
    padding: 0;
    /*background: #dfdcd3;*/
    /*color: #333;*/
    /*border: 1px solid #dfdcd3;*/
    border-radius: 100%;
    display: inline-block;
    opacity: 1;
    z-index: 1;
    /*box-shadow: 0px 0px 2px 0px #333;*/
}

.align-center {
    text-align: center !important;
}

.align-left {
    text-align: left !important;
}

.align-right {
    text-align: right !important;
}
.modal-header {
    padding: 0 !important;
    border-bottom: none !important;
}
.no-padding {
    padding: 0 !important;
}

.no-padding-left {
    padding-left: 0px;
}

.no-padding-right {
    padding-right: 0px;
}

.no-padding-bottom {
    padding-bottom: 0px;
}

.no-padding-top {
    padding-top: 0px;
}

.full_width {
    width: 100%;
}

.shadow_input {
    line-height: 30px;
    box-shadow: inset 1px 1px 2px 0px #CCCCCC;
    border: 1px solid #C4C4C4;
    padding-left: 7px;
    padding-right: 7px;
}

.shadow_input:disabled {
    background-color: #eee;
}

.manager-modal .modal-body {
    font-size: 14px;
}

.manager-modal .modal-footer {
    border-top: none;
}

.manager-form .form-group {
    margin: 0;
}

.manager-form .form-group label {
    /*font-family: Tahoma, Helvetica, Arial;*/

}

.manager-form .form-group label.error {
    font-weight: normal;
    margin-bottom: 0px;
    color: red;
    margin-top: 5px;
    font-size: 14px;
}

.manager-form .gray_button.big {
    font-size: 17px;
    font-weight: bold;
}

.manager-form .gray_button.last_triangle {
    padding: 9px 51px 9px 39px;
    position: relative;
}

.manager-form .gray_button.last_triangle.left {
    padding: 9px 39px 9px 51px;
}

.manager-form .gray_button.last_triangle.right {

}

.manager-form .gray_button.last_triangle:after {
    content: " ";
    border-left: 9px solid #FEE300;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    position: absolute;
    top: 14px;
    right: 14px;
}

.manager-form .gray_button.last_triangle.left:after {
    left: 14px;
    border-left: 6px solid transparent;
    border-right: 9px solid #FEE300;
    right: auto;
}

.manager-form .gray_button.last_triangle.right:after {

}

.manager-form .more-padding {
    padding-bottom: 60px;
}

.regist-content-box {
    border: 8px solid #ececec;
    margin-top: 33px;
    margin-bottom: 50px;
}

.regist-content-title {
    background-color: #ececec;
    padding-left: 30px;
    line-height: 68px;
    color: #4a4a4a;
    font-weight: bold;
    font-size: 20px;
    height: 68px;
}

.regist-content-body {
    padding: 29px 50px 45px
}

.regist-content-row {
    border-bottom: 1px solid #dbdcdd;
    height: 55px;
    margin-bottom: 0;
}

.regist-content-row .col-xs-6 {
    display: inline-flex;
    padding: 0;
}

.right-input {
    padding-top: 12px;
    padding-left: 10px;
}

.right-input input[type=radio] {
    margin: 9px 5px 0 20px;
}

.right-input .shadow_input {
    height: 30px;
    line-height: 30px;
}

.shadow_select {
    width: 89px;
    height: 30px;
    line-height: 30px;
    box-shadow: inset -1px -1px 2px 0px #CCCCCC;
    border: 1px solid #C4C4C4;
    padding-left: 7px;
    padding-right: 7px;
    margin-right: 5px;
}

.small-input {
    width: 165px;
}

.big-input {
    width: 236px;
}

.regist-content-row .right-col {
    padding-left: 9px;
}

.first-description {
    padding-top: 28px;
    padding-bottom: 19px;
    margin-bottom: 0;
    font-size: 14px;
}

/*Modal-success*/
.button-container {
    text-align: center;
}

.success-form-title {
    color: #c22d2d;
    font-size: 19px;
    font-weight: 600;
    padding-left: 48px;
    height: 81px;
    line-height: 81px;
    margin-top: -10px;
}

.success-form-content {
    text-align: center;
}

.success-img-box {
    background-color: #fff000;
    height: 126px;
    width: 126px;
    margin: 0 auto;
    margin-top: 15px;
    border-radius: 63px 63px;
}

.success-img-box img {
    margin-top: calc(50% - 28.5px);
    height: 57px;
    margin-left: 1px;
}

.red-word {
    color: #c22d2d;
}

.padding-left-10 {
    padding-left: 10px;
}

.step_nav_container.step_margin_botton_none {
    margin-bottom: 0px;
}

.step_container {
    margin-top: 10px;
    font-size: 14px;
    white-space: nowrap;
    position: relative;
    display: flex;
}

.event_create_step.step_container {
    width: 900px;
    display: block;
}

.event_create_step.step_container.custom_step {
    width: 900px;
    display: flex;
    align-items: center;
}

.step_nav_container a, .step_nav_container a:hover, .step_nav_container a:focus {
    color: black;
    text-decoration: none;
}

.step_nav_container .cursor-nomal {
    cursor: default;
}

.step_nav_container .step_container a {
    position: relative;
    float: left;
}

.step_body {
    height: 36px;
    margin: 5px 0;
    margin-right: 20px;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 36px;
    vertical-align: top;
    background-color: #E6E6E6;
    position: relative;
}

.step_body.step_over_link {
    float: left;
}

.step_body.focus_step::before {
    border-left: 18px solid #fcff20;
}

.step_event::before {
    content: '';
    font-size: 0px;
    position: absolute;
    top: 0;
    left: 100%;
    border-left: 18px solid #E6E6E6;
    border-top: 18px solid transparent;
    border-bottom: 18px solid transparent;
}

.step_event_detail, .step_user_list {
    margin-right: 30px;
    padding: 0 25px;
    margin-bottom: 7px;
}

.step_event_detail::before, .step_user_list::before {
    content: '';
    font-size: 0px;
    position: absolute;
    top: 0;
    left: 100%;
    bottom: 0;
    border-left: 18px solid #E6E6E6;
    border-top: 36px solid transparent;

}

.account_information::before {
    content: '';
    font-size: 0px;
    position: absolute;
    top: 0;
    left: 100%;
    bottom: 0;
    border-left: 18px solid #ffe403;
    border-top: 36px solid transparent;
}

.focus_step {
    background-color: #FDBC00 !important;
}
.button-edit{
    background-color: #ffbc00;
    color: #000000;
    padding: 11px 16px;
    min-height: 40px;
}
@media (min-width: 1024px) {
    .button-edit{
        padding: 14px 24px;
        min-height: 48px;
    }
}
.item-step{
    display: flex;
}

.focus_step_information {
    background-color: #ffe403;
}

.nav_button_holder {
    height: 36px;
    position: absolute;
    right: 0;
}

.nav_button_holder.custom_button_holder {
    margin-left: auto;
    position: static;
    height: auto;
}

.nav_button_holder a:hover {
    text-decoration: none;
}

.black-button.small-button.detail-nav-button {
    right: 0;
    margin-right: 0;
}

.black-button.small-button.step-button {
    padding: 25px 17px 25px 18px;
    margin: 5px 0;
}

.black-button.small-button.detail-nav-button.step-button {
    padding: 9px 10px 8px 18px;
}

.black-button.small-button.detail-nav-button.button_first {
    padding: 25px 17px 25px 18px;
    margin: 5px 0;
}

.black-button.small-button.detail-nav-button.step-button.button_home {
    padding: 30px 17px 30px 17px;
    margin: 8px 0;
}

.step-button.to-event-top-button, .step-button.to-event-top-button:hover {
    background: #ffc000;
    border: none;
    color: #ffffff;
    right: 0;
    margin-right: 0;
}

/*Black button css*/
.black-button {
    margin: 0 25px;
    position: relative;
    cursor: pointer;
    color: black;
    /*color: #ffffff;*/
    /*border: none;*/
    /*border-bottom: 1px solid #6f6c64;*/
    /*border-right: 1px solid #6f6c64;*/
    /*background: rgba(152, 148, 139, 1);*/
    /*background: -moz-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);*/
    /*background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(152, 148, 139, 1)), color-stop(100%, rgba(128, 123, 112, 1)));*/
    /*background: -webkit-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);*/
    /*background: -o-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);*/
    /*background: -ms-linear-gradient(top, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);*/
    /*background: linear-gradient(to bottom, rgba(152, 148, 139, 1) 0%, rgba(128, 123, 112, 1) 100%);*/
    /*filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#98948b', endColorstr='#807b70', GradientType=0);*/
    /*-webkit-box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);*/
    /*-moz-box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);*/
    /*box-shadow: 2px 2px 0px rgba(213, 212, 212, 1), 0px 0px 0px rgba(0, 0, 0, 0);*/
}
.black-button:hover{
    color: black;
}

.black-button.big-button {
    min-width: 160px;
    padding: 19px 30px;
    background-color: #FDBC00;
    border-radius: 40px;
    color: #000000;
}
@media (max-width: 1023.9px) {
    .black-button.big-button{
        padding: 19px 22px;
    }
}
@media (max-width: 992px) {
    .black-button.big-button{
        padding: 17px 18px;
    }
}

.black-button.big-button.back-button {
    min-width: 160px;
    padding: 10px 30px 9px 42px;
}

.black-button.small-button {
    padding: 3px 35px 2px 25px;
}

.black-button.small-button.back-button {
    padding: 3px 25px 2px 35px;
}

.black-button.small-button.medium-button, .black-button.small-button.application-button {
    padding: 6px 35px 5px 25px;
    margin: 0;
}

.button_column .black-button.small-button.medium-button {
    margin-right: 50px;
}

.button-container a:hover, .button-container a:focus, .button-container a,
.black-button a, .black-button a:hover, .black-button a:focus {
    text-decoration: none;
}

.black-button:focus {
    outline: none;
}

.black-button.big-button span.glyphicon {
    position: absolute;
    top: calc(50% - 4.5px);
    right: 8px;
    font-size: 9px;
    color: #ffe400;
}

.black-button.small-button span.glyphicon {
    position: absolute;
    top: calc(50% - 3.5px);
    right: 6px;
    font-size: 7px;
    color: #ffe400;
}

.black-button.big-button.back-button span.glyphicon, .black-button.small-button.back-button span.glyphicon {
    left: 8px;
    right: calc(100% - 17px);
}

.success-send-button.button-container {
    margin: 30px 0 0;
}

/*popup confirm*/
.notification_popup_container {
    width: 100%;
    text-align: center;
    margin: 50px 0;
}

.notification_popup_container .button-container {
    margin-top: 50px;
}

.notification_modal {
    /*width: 640px;*/
    margin-top: 15%;
    margin-left: auto;
    margin-right: auto;
}

/*is-vip*/
.width-vip-row.col-xs-9.row {
    width: 78%;
}

.vip-container {
    margin-top: 10px;
}

.mail-container {
    padding: 3px;
    margin-bottom: 0px;
}

.mail-container > input {
    width: 100%;
    padding-left: 5px;
}
.set-mail-container .mail_container-box input {
    margin-bottom: 16px;
}
.set-mail-container .mail_container-box:last-child input {
    margin-bottom: 0;
}

/*overview*/
.email-button-container {
    position: absolute;
    top: 26px;
    right: 0;
    width: 25%;
    text-align: right;
}

.email-button-container .black-button {
    margin-right: 10px;
}

.email-button-container p {
    font-size: 12px;
    margin-top: 15px;
    color: #c22d2d;
}

/********* guide holder *********/
.guide_holder {
    font-size: 16px;
    line-height: 27px;
    font-weight: bold;
}

.guide_holder .red_text {
    color: #C74A4A;
}

.guide_holder .arrow_right {
    padding-left: 8px;
    padding-right: 8px;
    color: #FEE200;
    font-size: 17px;
    position: relative;
    top: 3px;
}

.decription-text {
    margin-top: 10px;
    font-size: 11px;
    margin-left: 3px;
}

/*little-box-content
User in embedded and overview*/

.little-box-content {
    position: relative;
}

.little-box-content .text-center p {
    margin: 27px auto 50px auto;
    font-size: 12px;
    color: #717171;
}

.little-box-content .form_box {
    width: 100%;
    border: 8px solid #ECECEC;
    margin-top: 25px;
    padding-bottom: 30px;
}

.notify_content {
    line-height: 30px;
    text-align: center;
    padding-top: 40px;
    padding-bottom: 150px;
    color: red;
    font-size: 15px;
}

.bottom-event-action {
    display: inline-block;
    position: relative;
    margin-left: 15px;
}

.notify-event-left {
    width: 240px;
    line-height: normal;
    text-align: left;
    position: absolute;
    top: 0px;
    right: 105%;
    color: #c22d2d;
}

.notify-event-right {
    width: 240px;
    line-height: normal;
    text-align: left;
    position: absolute;
    top: 0px;
    left: 105%;
    color: #c22d2d;
}

.notify-event-right.embedded_text {
    width: 130px;
    left: 100%;
}

#modal-view-html-script {
    width: 1000px;
}

.event_wrap .input_field_holder .select_button.list_held_hours {
    width: 166px;
}

/********************* css functional buttons **************************/
.questionnaire-button {
    display: block;
    padding: 3px 30px;
    font-size: large;
    float: left;
    margin-right: 25px;
    margin-top: 3px;
    background-color: rgba(140, 140, 140, 1);
    border: none;
    color: white;
}

.questionnaire-button > span.glyphicon {
    font-size: small;
    color: #fcff20;
}

.functional-buttons > .focus-button {
    background-color: #fcff20;
    color: black;
}

.focus-button > span.glyphicon {
    font-size: small;
    color: black;
}

.functional-buttons a:focus {
    outline: none;
}

.questionnaire-button:hover {
    background: rgba(140, 140, 140, 1); /* For browsers that do not support gradients */
    background: -webkit-linear-gradient(rgba(140, 140, 140, 1), rgba(159, 159, 159, 1)); /* For Safari 5.1 to 6.0 */
    background: -o-linear-gradient(rgba(140, 140, 140, 1), rgba(159, 159, 159, 1)); /* For Opera 11.1 to 12.0 */
    background: -moz-linear-gradient(rgba(140, 140, 140, 1), rgba(159, 159, 159, 1)); /* For Firefox 3.6 to 15 */
    background: linear-gradient(rgba(140, 140, 140, 1), rgba(159, 159, 159, 1)); /* Standard syntax */
}

.focus-button:hover {
    background: #fcff20; /* For browsers that do not support gradients */
    background: -webkit-linear-gradient(#fcff20, #fcff91); /* For Safari 5.1 to 6.0 */
    background: -o-linear-gradient(#fcff20, #fcff91); /* For Opera 11.1 to 12.0 */
    background: -moz-linear-gradient(#fcff20, #fcff91); /* For Firefox 3.6 to 15 */
    background: linear-gradient(#fcff20, #fcff91); /* Standard syntax */
}

.bold_text {
    font-weight: 600;
}

.dticket-modal.modal-dialog.rule_modal {
    width: 800px;
    margin-top: 96px;
}

@media only screen and (max-width: 924px) {
    .dticket-modal.modal-dialog.rule_modal {
        width: 100%;
        margin-top: 25px;
        margin: 0;
    }

    .privacy_container .privacy_contain {
        max-height: 50%;
        height: auto;
        font-size: 13px;
        overflow-y: scroll;
    }
}

.privacy_container .privacy_contain {
    height: 800px;
    overflow-y: scroll;
}

.privacy_contain {
    border: solid #F2F2F2;
    padding: 20px;
    margin-bottom: 20px;
}

.button_rule {
    text-align: center;
}

.button_rule .gray_button {
    padding: 5px 35px 4px 25px;
}

.rule_field .error.col-xs-offset-4 {
    margin-left: 0;
}

.rule_modal .rule_modal_title {
    margin: 10px 0px;
}

span.bold_text {
    font-weight: bold;
}

.dticket-modal.modal-dialog.rule_modal {
    margin-top: 60px;
}

/*banner event_index_top*/
.banner-container {
    width: 1000px;
    margin: 0 auto;
    padding-bottom: 80px;
}

.description-text {
    font-size: 15px;
    margin: 0px auto;
    width: 85%;
    padding: 0px 50px;
}

.description-text div.item-text {
    text-align: center;
    color: #fff;
    margin: 10px 0;
    padding-left: 10px;
    padding-right: 10px;
}

.description-text div.item-text img {
    width: 100%;
}

.m-t-20 {
    margin-top: 20px;
}

::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 7px;
}
::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: rgba(0,0,0,.5);
    box-shadow: 0 0 1px rgba(255,255,255,.5);
}
.end-description-conten {
    padding-top: 28px;
    padding-bottom: 19px;
    margin-bottom: 0;
    margin-left: 30px;
    font-size: 14px;
}
.black-form {
    display: flex;
    align-items: center;
}
.small-button.double {
    padding: 6px 35px 4px 25px
}
.edit-container {
    display: flex
}

#inputUrl.inputUrl_disabled {
    border: 0px !important;
    border-bottom: 1px solid #C4C4C4 !important;
    box-shadow: none !important;
}
.add-text{
    margin-top: -2rem;
}
.error-form-title {
    color: black;
    font-size: 19px;
    font-weight: 600;
    padding-left: 48px;
    height: 70px;
    line-height: 70px;
    background-color: #ECECEC;
}

.step_container_admin {
    margin-top: 10px;
    font-size: 14px;
    white-space: nowrap;
    position: relative;
    display: grid;
    grid-template-columns: 1.2fr 1fr 1.2fr;
}

.event_create_step.step_container_admin {
    width: 900px;
    display: block;
}

.step_admin {
    text-align: center;
}

.focus_step_admin {
    color: white;
}

.step_nav_container_admin a, .step_nav_container_admin_sum a{
    color: white;
}

.step_body_admin {
    height: 36px;
    margin: 5px 0;
    margin-right: 3px;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 36px;
    vertical-align: top;
    background-color: #E6E6E6;
    position: relative;
}
/*
    hungnt
    fix scroll modal list excel
*/
.list_excel #confirm-modal-template #content_excel{
    max-height: 330px;
    overflow-y: scroll;
}

.list_excel .manager-modal .modal-body {
    padding: 30px!important;
}

.list_excel .button-container{
    padding: 0!important;
}
.disabled {
    pointer-events: none;
    cursor: default;
    color: #6f6c64!important;
}
.list_excel .btn-download-excel,
.list_excel .btn-download-excel:hover{
    color: white;
}

.list_excel .btn-download-excel{
    display: block;
    width: fit-content;
    margin: 25px auto 0;
    padding: 10px 30px;
}

#data_excel .progress-bar-warning{
    background-color: #fcff20;
    color: black;
}

.modal__close__application {
    position: absolute;
    top: 5px;
    right: 10px;
    color: #585858;
    text-decoration: none !important;
}

.modal__close__step {
    position: absolute;
    top: -3px;
    right: 10px;
    color: #585858;
    text-decoration: none !important;
}

.modal__close__ticket {
    position: absolute;
    top: 4px;
    right: 10px;
    color: #585858;
    text-decoration: none !important;
}

.show_tip {
    margin-top: 12px;
}
.lang-switch{
    color: #000;
    background: #f5f5f5;
    border-radius: 40px;
    padding: 3px 5px;
    font-size: 10px;
    width: 100%;
    display: block;
    text-align: center;
    text-decoration: none;
}

.lang-switch.active {
    color: #fff;
    background: #000;
    font-weight: bold;
}
.header_no_login{
    padding: 0 24px 0 40px;
}
.logo-header{
    height: 176px;
    background-color: #FFFFFF;
    border-radius: 0 0 40px 40px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}
.logo-header  img{
    padding: 22px 31px;
}
.header_no_login .language{
    padding-top: 28px;
}

.shadow_input {
    line-height: 30px;
    border: none;
    padding-left: 7px;
    padding-right: 7px;
    height: 56px;
    border-radius:16px;
    background:#F5F5F5;
    width: 100%;
}

.sidenav .left-bottom-text {
    position: relative;
    margin-bottom: 24px;
}

li.breadcrumb-mobile-item a {
    font-size: 16px !important;
    display: flex;
    gap: 8px;
    height: 48px;
    padding: 8px 16px;
    margin: 8px 0;
    width: 100%;
    justify-content: start;
}

textarea.shadow_input {
    height: unset;
}

.logout-btn-mobile {
    height: 56px !important;
    display: flex !important;
    justify-content: center !important;
    margin-top: 32px !important;
}

.shadow_input:disabled {
    background-color: #eee;
}
.footer-copy-right{
    padding-top: 17px;
    border-top: 1px solid rgba(255, 255, 255, 0.4);
    text-align: center;
    margin-bottom: 47px;
}
@media (min-width: 1024px) {
    .footer-copy-right{
        margin-bottom: 0;
    }
}
.footer-copy-right_none{
    padding: 0 24px;
    width: 100%;
}
.footer-copy-right span{
    font-size: 12px;
}
.header-login{
    padding: 9px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 26px;
}
@media (min-width: 1024px) {
    .header-login {
        padding: 15px 24px 15px 40px;
        height: 120px;
        margin-bottom: 0;
    }
}
.header-login .left{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 32px;
}

.header-login .right form{
    display: flex;
    align-items: center;
    gap: 16px;
}

.text-header-left{
    font-size: 18px;
}

.sidenav {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 3;
    top: 0px;
    right: 0px;
    background-color: rgba(255, 195, 26);
    overflow-x: hidden;
    transition: 0.5s;
    border: 0;
    transform: translateX(100%);
    padding-top: 72px;
    border-radius: 0;
}

.breadcrumb-mobile {
    text-align: center;
    font-size: 24px;
}
.breadcrumb-pc-item {
    border-radius: 16px;
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    padding: 0 18px;
    text-decoration: none;
    color: #000000;
}
@media (min-width: 1024px) {
    .breadcrumb-pc-item {
        height: 56px;
    }
}
.breadcrumb-pc-item:hover{
    text-decoration: none;
    color: #000000;
    background: #FDBC00;
}
.breadcrumb-pc-item.active{
    background: #FDBC00;
}

.sidenav ul {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 0 16px;
    background-color: rgba(255, 195, 26);
}

.sidenav ul li .active {
    border-top: 1px solid #FFFFFF;
    background: white;
    border-radius: 8px;
}

.sidenav ul li {
    border-top: 1px solid #fed766;
    /*padding: 16px;*/
    margin: 0;
}

.sidenav .closebtn {
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 2;
    font-weight: 900;
}

.form-filter {
    margin-bottom: 24px;
}
.sidenav a {
    text-decoration: none;
    font-size: 20px;
    color: black;
    display: inline-flex;
    align-items: center;
    transition: 0.3s;
    justify-content: center;
}

@media (min-width: 1024px) {
    .sidenav {
        display: none;
    }
    .breadcrumb-mobile {
        display: none;
    }
    .breadcrumb-pc{
        display: flex;
        justify-content: center;
        gap: 20px;
        height: 88px;
        background-color: white;
        align-items: center;
        font-size: 18px;
    }
    .lang-switch {
        width: 74px;
        height: 28px;
        font-size: 14px;
    }
    .header-login .right{
        display: flex;
        align-items: center;
        gap: 16px;
    }
    .right-mobile {
        display: none;
    }
}
.header-menu-mobile > li{
    font-weight: bold;
}
@media (max-width: 1023.9px) {

    .text-header-left {
        font-size: 14px;
    }

    .header-menu-mobile > li {
        display: inline-flex;
        gap: 8px;
    }

    .header-menu-mobile{
        margin: 0;
    }

    .input-search-topbar {
        width: 100%;
        height: 48px;
    }

    .right-mobile {
        display: flex;
        align-items: center;
        gap: 24px;
    }

    .breadcrumb-pc {
        display: none;
    }

    .breadcrumb-mobile {
        display: flex;
        font-size: 24px;
        justify-content: center;
        gap: 8px;
        align-items: center;
    }

    .left .right-text {
        display: none;
    }

    .header-login .right{
        display: none;
    }
    .logo-header{
        height: 88px;
        border-radius: 0 0 16px 16px;
    }
    .logo-header img {
        padding: 11px 15px;
    }
    .header_no_login {
        padding: 0 16px 0 24px;
    }
    .header_no_login .language{
        padding-top: 14px;
    }
    .footer-box{
        padding: 0;
        gap: 18px;
        flex-direction: column;
    }
    .box-link{
        flex-wrap: wrap;
        gap: 16px;
        padding: 0 45px;
        justify-content: center;
        margin-bottom: 15px;
    }
    .box-link a{
        font-size: 12px;
    }
}

/* input radio */
.radio input[type=radio] {
    position: absolute;
    opacity: 0;
}
.radio input[type=radio] + .radio-label {
    color: #000;
}
.radio input[type=radio] + .radio-label:before {
    content: "";
    background: #fff;
    border-radius: 100%;
    border: 2px solid #666666;
    display: flex;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    position: relative;
    margin-right: 4px;
    cursor: pointer;
    text-align: center;
    transition: all 250ms ease;
}
@media (min-width: 1024px) {
    .radio input[type=radio] + .radio-label:before {
        margin-right: 8px;
        width: 24px;
        height: 24px;
    }
}
.radio input[type=radio]:checked + .radio-label:before {
    background-color: #FDBC00;
    box-shadow: inset 0 0 0 3px #fff;
    border: 2px solid #FDBC00;
}
.radio input[type=radio]:disabled + .radio-label:before {
    background-color: #666666;
    box-shadow: inset 0 0 0 3px #fff;
    border: 2px solid #666666;
}
.radio input[type=radio]:focus + .radio-label:before {
    outline: none;
    border-color: #FDBC00;
}
.radio input[type=radio] + .radio-label:empty:before {
    margin-right: 0;
}
.checkbox-parents input[type="checkbox"]:checked ~ label .checkbox-icon {
    border: 2px solid #FFB700;
}
.checkbox-parents input[type="checkbox"] {
    display: none;
}
.checkbox-parents .checkbox-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 2px solid #000;
    border-radius: 3px;
    position: relative;
    flex-shrink: 0;
    margin-right: 8px;
}
.checkbox-parents input[type="checkbox"]:checked ~ label .checkbox-icon:after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #FFB700;
    font-size: 16px;
    font-weight: bold;
}

/* check input */

.checkbox-parents .checkbox-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 2px solid #000;
    border-radius: 3px;
    position: relative;
    flex-shrink: 0;
    margin-right: 8px;
}
.checkbox-parents input[type="checkbox"]:checked ~ label .checkbox-icon:after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #FFB700;
    font-size: 16px;
    font-weight: bold;
}
/* checkbox input */
.checkbox-custom input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #000 !important;
    border-radius: 4px !important;
    display: flex;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;
    background-color: #fff !important;
    margin: 0 auto;
    min-width: auto !important;
}
@media (min-width: 1024px) {
    .checkbox-custom input[type="checkbox"] {
        width: 24px !important;
        height: 24px !important;
        margin-top: -3px !important;
    }
}

.checkbox-custom input[type="checkbox"]:checked {
    background-color: #fff;
    border: 2px solid #FDBC00 !important;
    box-shadow: unset;
    min-height: auto !important;
}
.checkbox-custom input[type="checkbox"]:disabled {
    border: 2px solid #666 !important;
    box-shadow: unset;
    background-color: #666 !important;
}
.checkbox-custom input[type="checkbox"]:focus {
    outline: unset;
    outline-offset: 0;
}
.checkbox-custom input[type="checkbox"]:checked::after {
    content: "✔";
    font-size: 14px;
    color: #FDBC00;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.checkbox-custom input[type="checkbox"]:disabled::after {
    content: "✔";
    font-size: 14px;
    color: #ccc;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

@media (min-width: 1400px) {
    .container {
        max-width: 1230px !important;
    }
}
.modal-dialog-centered {
    top: 50%;
    transform: translateY(-50%) !important;
}
.modal_design-new.modal-dialog {
    width: 100%;
    margin: 0;
}
@media (min-width: 1024px) {
    .modal_design-new.modal-dialog {
        width: 640px;
        margin: 0 auto;
    }
}
.modal_design-new .modal-content {
    border: 0;
    margin: 0 10px;
    border-radius: 32px !important;
}
.modal_design-new .manager-form .form-group label {
    font-size: 20px;
    line-height: 27px;
    margin-bottom: 26px;
}
@media (min-width: 1024px) {
    .modal_design-new .manager-form .form-group label {
        font-size: 28px;
        line-height: 37px;
        margin-bottom: 44px;
    }
}
.modal_design-new .modal-body {
    padding: 40px 24px;
}
@media (min-width: 1024px) {
    .modal_design-new .modal-body {
        padding: 66px 64px;
    }
}
.modal_design-new .modal-header .close-modal {
    top: 24px;
    right: 24px;
    box-shadow: unset;
}
@media (min-width: 1024px) {
    .modal_design-new .modal-header .close-modal {
        top: 32px;
        right: 32px;
    }
}
.modal_design-new .content-box p {
    font-size: 14px;
    line-height: 19px;
    margin-bottom: 8px;
}
@media (min-width: 1024px) {
    .modal_design-new .content-box p {
        font-size: 16px;
        line-height: 21px;
    }
}
.modal_design-new .modal-body .select2-container--default .select2-selection--single {
    background-color: #F5F5F5;
    border: 0;
    border-radius: 16px;
    display: flex;
    align-items: center;
    height: 56px;
}
@media (min-width: 1024px) {
    .modal_design-new .modal-body .select2-container--default .select2-selection--single {
        height: 64px;
    }
}
.modal_design-new .modal-body .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #000;
    line-height: 19px;
    font-size: 14px;
    padding: 18.5px 30px 18.5px 16px;
    display: flex;
    align-items: center;
}
@media (min-width: 1024px) {
    .modal_design-new .modal-body .select2-container--default .select2-selection--single .select2-selection__rendered {
        font-size: 16px;
        line-height: 21px;
    }
}
.modal_design-new .modal-body .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 56px;
    top: 0px;
    right: 20px;
}
@media (min-width: 1024px) {
    .modal_design-new .modal-body .select2-container--default .select2-selection--single .select2-selection__arrow  {
        height: 64px;
    }
}
.modal_design-new .modal-body .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #000 transparent transparent transparent;
    border-width: 6px 6px 0 6px;
    margin: 0;
    transform: translateY(-50%);
}
.modal_design-new .modal-body .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-width: 0 6px 6px 6px;
    border-color: transparent transparent #000 transparent;
}
.select2-container--default .select2-results__option[aria-selected=true] {
    word-break: break-all;
}
.modal_design-new .modal-body .txt {
    margin: 17px 0 25px;
}
.modal_design-new .modal-body .txt {
    font-size: 14px;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .modal_design-new .modal-body .txt {
        font-size: 16px;
        line-height: 21px;
    }
}
.modal_design-new .modal-body .button-container-copy .big-button {
    width: max-content;
    border: 0;
    border-radius: 40px;
    padding: 17.5px 16px;
    margin: 0;
    box-shadow: unset;
    min-width: 122px;
}
.modal_design-new .modal-body .button-container-copy button {
    background: #FDBC00;
    color: #000;
}
.modal_design-new .modal-body .button-container-copy a {
    background: #000;
}
.text-gray{
    color: #666666;
}
.select2-search--dropdown {
    padding: 0;
    margin-bottom: 4px;
}
.select2-results__option[aria-selected] {
    word-break: break-all;
}
.select2-container--default .select2-search--dropdown .select2-search__field:focus {
    outline: none;
    box-shadow: none;
}
.select2-dropdown {
    border: 0;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    padding: 12px;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ccc;
}

.select2_header-parents .select2-container .select2-selection--single {
    height: 48px;
}
@media (min-width: 1024px) {
    .select2_header-parents .select2-container .select2-selection--single {
        height: 64px;
    }
}
.select2_header-parents .select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 48px;
    padding-left: 16px;
    padding-right: 30px;
}
@media (min-width: 1024px) {
    .select2_header-parents .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 64px;
    }
}
.select2_header-parents .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 46px;
    right: 10px;
}
@media (min-width: 1024px) {
    .select2_header-parents .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 62px;
    }
}
.select2_header-parents .select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 0;
    border-radius: 16px;
}
.select2_header-parents .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #000 transparent transparent transparent;
    border-width: 6px 6px 0 6px;
}
.select2_header-parents .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #000 transparent;
    border-width: 0 6px 6px 6px;
}
.select2_header-parents .select2-selection__clear {
    display: none;
}
.form_navbar-box .box-item .btn_submit-navbar {
    min-height: 48px;
    max-height: 48px;
}
@media (min-width: 1024px) {
    .form_navbar-box .box-item .btn_submit-navbar {
        min-height: 64px;
        max-height: 64px;
    }
}
.all_event-boxselect .select2-dropdown {
    width: 96.5% !important;
}
@media (min-width: 1024px) {
    .all_event-boxselect .select2-dropdown {
        width: auto !important;
        max-width: 490px;
        padding: 12px;
    }

    .w-640 {
        width: 640px !important;
    }
}
/*email_template*/
.box_content-bg-fff {
    background: #fff;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 16%);
    border-radius: 30px;
    padding: 40px 16px;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}
.box_content-bg-fff .steps__common .step_container {
    margin-top: 0;
    gap: 8px;
    border-bottom: 4px solid #FDBC00;
    overflow-x: auto;
    padding-right: 16px;
    margin-right: -16px;
    scroll-behavior: smooth;
}
.box_content-bg-fff .steps__common .step_container::-webkit-scrollbar {
    display: none;
}

.box_content-bg-fff .steps__common .step_container::-webkit-scrollbar-thumb {
    display: none;
}
.box_content-bg-fff .steps__common {
    margin-bottom: 24px;
}

.box_content-bg-fff .steps__common .email-template-box {
    border: 0;
    padding: 0;
    max-width: 640px;
    margin: 0 auto 80px;
}

.box_content-bg-fff .steps__common .step_event_detail {
    margin: 0;
    font-size: 12px;
    line-height: 16px;
    padding: 12px 16px;
    height: auto;
    font-weight: 700;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    background-color: #000;
    color: #fff;
}

.box_content-bg-fff .steps__common .step_event_detail::before {
    content: unset;
}
.box_content-bg-fff .steps__common .focus_step {
    background-color: #FDBC00;
    color: #000;
}

.box_content-bg-fff .email-template-box {
    border: 0;
    padding: 0;
    margin: auto;
    font-size: 14px;
    line-height: 20px;
}

.box_content-bg-fff .et-guide-text {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 16px;
}

.box_content-bg-fff .et-input-title {
    background-color: transparent;
    padding: 0;
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    margin-bottom: 8px;
}

.select-box .select2-container--default .select2-selection--single {
    background-color: #F5F5F5;
    border: 1px solid #F5F5F5;
    border-radius: 16px;
    padding: 16px 20px;
    height: 56px;
}

.select-box .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #000 transparent transparent transparent;
    border-width: 7px 7px 0 7px;
    margin-left: -24px;
    margin-top: -4px;
}

.select-box .select2-container--default .select2-selection--single .select2-selection__rendered {
    font-size: 16px;
    line-height: 24px;
    padding-left: 0;
    color: #000;
}

.select-box .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 56px;
}

.select-box .select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #000 transparent;
    border-width: 0 7px 7px 7px;
}

.box_content-bg-fff .et-input {
    height: 56px;
    background: #F5F5F5;
    border-radius: 16px;
    padding: 20px 24px;
    font-size: 14px;
    line-height: 20px;
    display: block;
}

.box_content-bg-fff .et-chunk {
    margin-top: 0;
    border: 0;
    padding: 20px 24px;
    background: #F5F5F5;
    border-radius: 16px;
    font-size: 14px;
    line-height: 20px;
}

.box_content-bg-fff .e_mail_template textarea[name="mail_content"],.e_mail_template.mail_preview label {
    min-height: 320px;
    width: 100%;
    color: #000;
    font-size: 14px;
    line-height: 21px;
    padding: 16px;
    background: #F5F5F5;
}
.box_content-bg-fff .e_mail_template textarea[name="mail_content"]:placeholder {
    color: #999999;
}
.box_content-bg-fff .e_mail_template {
    font-size: 14px;
}
@media (min-width: 1024px) {
    .box_content-bg-fff .e_mail_template {
        font-size: 15px;
    }
    .box_content-bg-fff .e_mail_template textarea[name="mail_content"], .e_mail_template.mail_preview label {
        padding: 24px;
        font-size: 15px;
        min-height: 480px;
    }
}
@media (min-width: 1200px) {
    .survey_question_custom{
        margin-left: -215px;
        margin-right: -215px;
    }
}

.box_content-bg-fff .e_temp_form-box {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.box_content-bg-fff .black-button {
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    padding: 19px 24px;
    border-radius: 40px;
    width: max-content;
    margin: 0;
    color: #fff;
    background-color: #000;
    flex-shrink: 0;
}

.box_content-bg-fff .black-button.small-button.medium-button,
.box_content-bg-fff .black-button.small-button.application-button {
    padding: 19px 24px;
}

.box_content-bg-fff .email-template-box .small-button.medium-button.yellow-btn {
    background: #FDBC00;
}

.box_content-bg-fff .email-template-box .form-group {
    margin: 0;
    width: 100%;
}

.box_content-bg-fff .et-button-container {
    display: flex;
    justify-content: center;
    gap: 16px;
}

.line-x {
    height: 1px;
    width: 100%;
    background-color: #ccc;
}

.box_content-bg-fff .back-to-top {
    flex-direction: row;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    background-color: #F5F5F5;
    padding: 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 0;
    cursor: pointer;
}

.box_content-bg-fff .back-to-top svg {
    width: 20px;
    height: 20px;
}

.has-back-to-top {
    padding-bottom: 96px;
}

@media (min-width: 1024px) {
    .box_content-bg-fff {
        padding: 64px;
        margin-top: 65px;
        margin-bottom: 40px;
    }

    .box_content-bg-fff .steps__common .step_event_detail {
        font-size: 18px;
        line-height: 26px;
        padding: 19px 24px;
    }

    .box_content-bg-fff .steps__common .step_container {
        gap: 16px;
        border-bottom: 8px solid #FDBC00;
        padding-right: 0;
        margin-right: 0;
    }
    .box_content-bg-fff .steps__common {
        margin-bottom: 40px;
    }
    .has-back-to-top {
        padding-bottom: 144px;
    }

    .box_content-bg-fff .e_temp_form-box {
        gap: 24px;
    }

    .select-box .select2-container--default .select2-selection--single {
        padding: 20px 24px;
        height: 64px;
    }
    .select-box .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 64px;
    }
    .box_content-bg-fff .et-input {
        font-size: 16px;
        line-height: 24px;
        height: 64px;
    }
    .box_content-bg-fff .et-guide-text {
        font-size: 15px;
        line-height: 21px;
        margin-bottom: 27px;
    }

    .box_content-bg-fff .et-input-title {
        font-size: 16px;
        line-height: 24px;
    }

    .box_content-bg-fff .et-chunk {
        font-size: 16px;
        line-height: 24px;
    }
    .box_content-bg-fff .email-template-box {
        font-size: 16px;
        line-height: 24px;
    }
    .box_content-bg-fff .email-template-box .small-button.medium-button.black-button {
        font-size: 18px;
        line-height: 26px;
        font-weight: 700;
        padding: 19px;
        min-width: 58px;
        text-align: center;
    }
    .box_content-bg-fff .back-to-top {
        font-size: 16px;
        line-height: 24px;
    }
    .box_content-bg-fff .back-to-top svg {
        width: 24px;
        height: 24px;
    }
}

@media (min-width: 1366px) {
    .min-w-240 {
        width: 240px;
    }
}
.rearch_results-boxmodal .search_result_title  {
    padding: 0;
    font-size: 20px;
    line-height: 27px;
    margin-bottom: 29px;
}
@media (min-width: 1024px) {
    .rearch_results-boxmodal .search_result_title  {
        padding: 0;
        font-size: 28px;
        line-height: 37px;
        margin-bottom: 44px;
    }
}
.rearch_results-boxmodal .info_repetition {
    margin: 0 auto 25px;
    font-size: 14px;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .rearch_results-boxmodal .info_repetition {
        margin: 0 auto 36px;
        font-size: 16px;
        line-height: 21px;
    }
}
.rearch_results-boxmodal .modal-body {
    padding: 40px 24px;
}
@media (min-width: 1024px) {
    .rearch_results-boxmodal .modal-body {
        padding: 64px;
    }
}
.rearch_results-boxmodal .result_box > .result_box_title {
    height: 48px;
    background-color: #666666;
    line-height: 36px;
    font-size: 14px;
}
@media (min-width: 1024px) {
    .rearch_results-boxmodal .result_box > .result_box_title {
        height: 56px;
        font-size: 16px;
    }
}
.rearch_results-boxmodal .result_box > .result_box_content {
    padding: 17px 16px;
    font-size: 14px;
    line-height: 19px;
    border: 1px solid #666666;
}
@media (min-width: 1024px) {
    .rearch_results-boxmodal .result_box > .result_box_content {
        padding: 18px 17px;
        font-size: 16px;
        line-height: 21px;
    }
}
.box-item .btn-general {
    font-size: 14px;
    line-height: 19px;
    font-weight: 700;
    padding: 0 20px;
    border-radius: 40px;
    width: 50%;
    min-height: 56px;
    box-sizing: border-box;
    max-height: 56px;
    cursor: pointer;
    border: 0;
}
.box-item .btn-general:hover {
    opacity: 0.8;
}
@media (min-width: 1024px) {
    .box-item .btn-general {
        font-size: 18px;
        line-height: 24px;
        min-height: 64px;
        max-height: 64px;
        padding: 16px 33px;
    }
    .box-item .btn-general.btn-search-header{
        font-size: 16px;
    }
}
.box-item-2 .btn-general {
    font-size: 14px;
    line-height: 19px;
    font-weight: 700;
    padding: 0 20px;
    border-radius: 40px;
    width: 50%;
    min-height: 56px;
    box-sizing: border-box;
    cursor: pointer;
}
.box-item-2 .btn-general:hover {
    opacity: 0.8;
}
.min-w-158 {
    min-width: 158px;
}
.min-w-120{
    min-width: 120px;
}
@media (min-width: 1024px) {
    .box-item-2 .btn-general {
        font-size: 16px;
        line-height: 21px;
        min-height: 64px;
        max-height: 64px;
        padding: 16px 33px;
    }
    .w-192 {
        min-width: 192px;
    }
}
.box-item .btn-black , .box-item-2 .btn-black {
    background-color: #000;
    color: #fff;
    margin-left: 8px;
}
.box-item .btn-yellow , .box-item-2 .btn-yellow {
    background-color: #FDBC00;
    color: #000;
    margin-left: 8px;
}
.box-item-2 .btn-yellow-3 {
    background-color: #fff;
    color: #000;
    border: 4px solid #FDBC00;
}
.box-item .btn-yellow:hover , .box-item-2 .btn-yellow:hover {
    color: #000;
}
.box-item .btn-yellow-2 , .box-item-2 .btn-yellow-2 {
    background-color: #FDBC00;
    color: #000;
    border: 4px solid #000;
}
.box-item .btn-orange , .box-item-2 .btn-orange {
    background-color: #FD6600;
    color: #fff;
}
.box-item .active {
    background-color: #FDBC00;
    color: #000;
}


@media (min-width: 1024px) {
    .box_content-bg-fff  #add_template_form {
        padding-top: 24px;
    }
}

.title-175 {
    width: 175px;
}
.title-210 {
    width: 210px;
}
.grid-template-columns-four {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-template-columns-three {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-template-columns-two {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}
@media (min-width: 1024px) {
    .grid-template-columns-xl-seven {
        grid-template-columns: repeat(7, minmax(0, 1fr));
    }
}
.required-text {
    font-size: 12px;
    line-height: 16px;
}
@media (min-width: 1024px) {
    .required-text {
        font-size: 14px;
        line-height: 19px;
    }
}
.event-fee-modal-new .wrap_custom_field .input_required-box {
    border: 0;
    box-shadow: unset;
    background-color: #f5f5f5;
    width: 100% !important;
    padding: 18px 16px;
    min-height: 56px;
    display: flex;
    align-items: center;
    border-radius: 16px;
    font-size: 14px;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .event-fee-modal-new .wrap_custom_field .input_required-box {
        font-size: 16px;
        line-height: 21px ;
    }
}
.banner_container-new {
    margin-bottom: 40px;
}
.banner_container-new .txt-content{
    font-size: 12px;
}
.banner_container-new .item {
    border-radius: 16px;
    width: 100%;
    height: max-content;
    box-shadow: 0 3px 6px rgba(0, 0, 0, .1);
    padding: 20px 15px;
    min-height: 110px;
}
@media (min-width: 1024px) {
    min-height: 120px;
}
.banner_container-new .item.item-1 {
    background: url("/assets/images/banner-index-top-1.png")no-repeat;
    background-size: 100% 100%;
}
.banner_container-new .item.item-2 {
    background: url("/assets/images/banner-index-top-2.png")no-repeat;
    background-size: 100% 100%;
}
.banner_container-new .item.item-3 {
    background: url("/assets/images/banner-index-top-3.png")no-repeat;
    background-size: 100% 100%;
}
.banner_container-new .item.item-4 {
    background: url("/assets/images/banner-index-top-4.png")no-repeat;
    background-size: 100% 100%;
}
.banner_container-new .item.item-5 {
    background: url("/assets/images/banner-index-top-5.png")no-repeat;
    background-size: 100% 100%;
}
.banner_container-new .item.item-6 {
    background: url("/assets/images/banner-index-top-6.png")no-repeat;
    background-size: 100% 100%;
}
.banner_container-new .item p {
    font-size: 14px;
    line-height: 19px;
}
.banner_container-new .item .price-box {
    font-size: 24px;
    line-height: 1;
}
.banner_container-new .item .price-box span {
    font-size: 14px;
}
.banner_container-new .item .txt {
    font-size: 14px;
    line-height: 19px;
}

@media (min-width: 1024px) {
    #result-back-btn{
        font-size: 16px;
    }
}
.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.dl-horizontal dd:after,
.container:before,
.container:after,
.container-fluid:before,
.container-fluid:after,
.row:before,
.row:after,
.form-horizontal .form-group:before,
.form-horizontal .form-group:after,
.btn-toolbar:before,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:before,
.btn-group-vertical > .btn-group:after,
.nav:before,
.nav:after,
.navbar:before,
.navbar:after,
.navbar-header:before,
.navbar-header:after,
.navbar-collapse:before,
.navbar-collapse:after,
.pager:before,
.pager:after,
.panel-body:before,
.panel-body:after,
.modal-header:before,
.modal-header:after,
.modal-footer:before,
.modal-footer:after {
    display: none;
    content: " ";
}

.max-content-640 {
    max-width: 640px;
    margin: auto;
}
.max-content-800 {
    max-width: 800px;
    margin: auto;
}

@media (min-width: 1024px) {
    .title-175 {
        width: 100%;
    }
    .title-210 {
        width: 100%;
    }
}

.box_content-bg-fff .dataTable {
    border: 0;
}

.box_content-bg-fff .dataTables_wrapper tbody {
    border-top: 0 !important;
}

.box_content-bg-fff .widget-body .dataTables_wrapper .table tr td:first-child {
    border-left-color: #666;
}

.box_content-bg-fff .widget-body .dataTables_wrapper .table tr td:last-child {
    border-right-color: #666;
}

.box_content-bg-fff .table > thead:first-child > tr:first-child > th {
    border: 1px solid #666;
}
.box_content-bg-fff .table-none > thead:first-child > tr:first-child > th {
    border: none;
    border-bottom: 2px solid #ccc;
}

.box_content-bg-fff .dataTable > thead > tr > th:last-child {
    border-right: 1px solid #666 !important;
}

/*.box_content-bg-fff .table-striped > tbody > tr:nth-of-type(odd) > * {*/
/*    --bs-table-accent-bg: #fff;*/
/*}*/

.box_content-bg-fff tr td,
.box_content-bg-fff tr td a {
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    text-underline-offset: 2px;
}

.box_content-bg-fff .manager_table_scroll td[for_key="detail"] {
    text-align: center;
}
.box_content-bg-fff .manager_table_scroll td[for_key="custom_action_delete"] {
    text-align: center;
}

.box_content-bg-fff .btn-outline-line-yellow {
    border: 4px solid #FDBC00;
    border-radius: 40px;
    background-color: #ffffff;
    color: #000;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 10px 20px !important;
}

.box_content-bg-fff .btn-outline-line-yellow:hover {
    color: #000;
}

.box_content-bg-fff .btn-back {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #F5F5F5;
    border-radius: 40px;
    font-size: 12px;
    line-height: 17px;
    font-weight: 700;
    padding: 12px 16px;
    text-decoration: none;
    color: #000;
}

.box_content-bg-fff .btn-back svg {
    height: 12px;
}

@media (min-width: 1024px) {
    .box_content-bg-fff .btn-back {
        font-size: 16px;
        line-height: 24px;
        padding: 16px 32px;
    }

    .box_content-bg-fff .btn-back svg {
        height: 16px;
    }

    .box_content-bg-fff .btn-outline-line-yellow {
        font-size: 16px !important;
        line-height: 24px !important;
        padding: 12px 44px !important;
    }
    .box_content-bg-fff .btn-only-mailing-detail {
        padding: 16px 24px;
        min-width: 282px;
        text-align: left;
        justify-content: start;
    }
}

.box_content-bg-fff a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.box_content-bg-fff .table > tbody > tr > td,
.box_content-bg-fff .table > tbody > tr > th,
.box_content-bg-fff .table > tfoot > tr > td,
.box_content-bg-fff .table > tfoot > tr > th,
.box_content-bg-fff .table > thead > tr > td,
.box_content-bg-fff .table > thead > tr > th {
    border: 1px solid #666666;
    padding: 15px 5px;
}
.box_content-bg-fff .table-none > tbody > tr > td,
.box_content-bg-fff .table-none > tbody > tr > th,
.box_content-bg-fff .table-none > tfoot > tr > td,
.box_content-bg-fff .table-none > tfoot > tr > th,
.box_content-bg-fff .table-none > thead > tr > td,
.box_content-bg-fff .table-none > thead > tr > th {
    border: none;
    padding: 15px 5px;
}

.box_content-bg-fff .table-striped > tbody > tr:nth-of-type(even) {
    background-color: #fff;
}

.box_content-bg-fff .table-header {
    background-color: #666666;
}

.box_content-bg-fff .table-header > tr {
    height: auto;
}

.box_content-bg-fff .table thead td:not(:last-child) {
    border-right: 1px solid #999;
}

.box_content-bg-fff .table thead td:last-child {
    border-right: 1px solid #666;
}

.box_content-bg-fff .table tbody {
    border-top: 1px solid #666;
}
.box_content-bg-fff .table-none tbody ,
.i_form_confirm_admin .table-none tbody {
    border: none;
}

.box_content-bg-fff h3.title {
    font-size: 18px;
    line-height: 26px;
    font-weight: 700;
}

.box_content-bg-fff .input-custom {
    width: 100%;
    background-color: #F5F5F5;
    border-radius: 16px;
    padding: 18px;
    font-size: 14px;
    line-height: 20px;
    border: 0;
}

@media (min-width: 1024px) {
    .box_content-bg-fff .input-custom {
        font-size: 16px;
        line-height: 24px;
    }
}

.box_content-bg-fff .select2-container{
    width: 100% !important;
}

.box-calendar {
    position: relative;
    width: 100%;
}

.box-calendar .ui-datepicker-trigger {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
}
.flex-1 {
    flex: 1;
}

.btn-yellow-common {
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    color: #000;
    background-color: #FDBC00;
    padding: 18px 24px;
    border-radius: 32px;
}
.select_custom-onlyyellow {
    font-weight: bold;
}

@media (min-width: 1024px) {
    .btn-yellow-common {
        font-size: 18px;
        line-height: 26px;
        min-width: 200px;
    }
}
.select_custom-onlyyellow {
    border: 4px solid #FDBC00;
    border-radius: 40px;
    padding: 16px 40px 16px 20px;
    font-size: 13px;
    line-height: 17px;
    appearance: none;
    background: url("/assets/images/ic-down-triagle.svg") no-repeat;
    background-size: 16px;
    background-position: 90%;
    cursor: pointer;
}
@media (min-width: 1024px) {
    .select_custom-onlyyellow {
        /*padding: 19px 50px 19px 20px;*/
        height: 56px;
        width: 200px;
        font-size: 16px;
    }
}

.select_custom-onlygary {
    border: 0;
    border-radius: 40px;
    padding: 16px 36px 16px 20px;
    font-size: 14px;
    line-height: 19px;
    appearance: none;
    background: url("/assets/images/ic-down-triagle.svg") no-repeat;
    background-size: 12px;
    background-position: 85%;
    cursor: pointer;
    background-color: #f5f5f5;
    width: 120px;
    max-width: 120px;
}

.survey_answer_item .checkbox-custom input[type="checkbox"] {
    width: 24px;
    height: 24px;
}

@media (min-width: 1024px) {
    .select_custom-onlygary {
        padding: 21.5px 36px 21.5px 20px;
        background-size: 16px;
        width: 144px;
        max-width: 144px;
        font-size: 16px;
        line-height: 21px;
    }
}
.btn-cancel{
    background-color: #FD6600;
    color: #ffffff;
}

.btn-black-common {
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    color: #fff;
    background-color: #000;
    padding: 18px 24px;
    border-radius: 32px;
}

@media (min-width: 1024px) {
    .btn-black-common {
        font-size: 18px;
        line-height: 26px;
        min-width: 200px;
    }
}

.gap-16 {
    gap: 16px;
}
@media (min-width: 1024px) {
    .content_base-form .event-edit-time-content , .content_base-form .detail-button-container {
        width: 640px;
        margin: 0 auto;
    }
    .content_base-form .form-group {
        margin-bottom: 24px;
    }
}
.content_base-form .input_title {
    font-size: 14px;
    line-height: 19px;
    font-weight: 700;
    margin-bottom: 8px;
}
@media (min-width: 1024px) {
    .content_base-form .input_title {
        font-size: 16px;
        line-height: 21px ;
    }
    .m-width-200 {
        min-width: 200px;
    }
}
.content_base-form .item-input {
    background-color: #F5F5F5;
    border-radius: 32px;
    padding: 16px 32px 16px 16px;
    border: 0;
    width: 100%;
}
@media (min-width: 1024px) {
    .content_base-form .item-input {
        padding: 21.5px 32px 21.5px 16px;
    }
}
.content_base-form .ui-datepicker-trigger {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
}
.content_base-form .area_item-box {
    height: 160px;
    border-radius: 16px;
    display: block;
    width: 100%;
    padding: 16px;
}
.content_base-form .txt {
    font-size: 14px;
    line-height: 19px;
    color: #000;
}
.mb-40 {
    margin-bottom: 40px;
}
.box_parents-item .box-item {
    border: 8px solid #F5F5F5;
    border-radius: 24px;
    padding: 28px 20px;
    margin-bottom: 24px;
}
@media (min-width: 1024px) {
    .box_parents-item .box-item {
        margin-bottom: 40px;
        padding: 42px 20px;
    }
}
.anchor_link-box {
    padding: 17.5px 16px;
    margin-left: -16px;
    margin-right: -16px;
    background-color: #F5F5F5;
    border-radius: 0 0 32px 32px;
    margin-top: 40px;
    cursor: pointer;
}
@media (min-width: 1024px) {
    .anchor_link-box{
        margin-left: -64px;
        margin-right: -64px;
        margin-top: 80px;
    }
    .w-xl-75 {
        width: 75% !important;
    }
}
.anchor_link-box-event {
    margin-left: -8px;
    margin-right: -8px;
}
@media (min-width: 1024px) {
    .anchor_link-box-event {
        margin-left: -64px;
        margin-right: -64px;
    }
}
.anchor_link-box a {
    font-size: 14px;
}
@media (min-width: 1024px) {

    .anchor_link-box a{
        font-size: 16px;
    }
}
.input_only-gray {
    border-radius: 16px;
    background-color: #f5f5f5;
    height: 48px;
}
.text-FD6600 {
    color: #FD6600 !important;
}
.text-11-13 {
    font-size: 11px;
    line-height: 15px;
}
@media (min-width: 1024px) {
    .text-11-13 {
        font-size: 13px;
        line-height: 16px;
    }
}
.text-11-14 {
    font-size: 11px;
    line-height: 15px;
}
@media (min-width: 1024px) {
    .text-11-14 {
        font-size: 14px;
        line-height: 17px;
    }
}
.text-11-15 {
    font-size: 11px;
    line-height: 15px;
}
@media (min-width: 1024px) {
    .text-11-15 {
        font-size: 15px;
        line-height: 19px;
    }
}
.text-11-16 {
    font-size: 11px;
    line-height: 15px;
}
@media (min-width: 1024px) {
    .text-11-16 {
        font-size: 16px;
        line-height: 20px;
    }
}
.text-10-14 {
    font-size: 10px;
    line-height: 14px;
}
@media (min-width: 1024px) {
    .text-10-14 {
        font-size: 14px;
        line-height: 16px;
    }
    .mb-xl--8{
        margin-bottom: -8px;
    }
}
.text-14-16 {
    font-size: 14px !important;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .text-14-16 {
        font-size: 16px !important;
        line-height: 21px;
    }
}
.text-14-17{
    font-size: 14px !important;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .text-14-17 {
        font-size: 17px !important;
        line-height: 21px;
    }
}
.text-14-15 {
    font-size: 14px !important;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .text-14-15 {
        font-size: 15px !important;
        line-height: 20px;
    }
}
.text-14-18 {
    font-size: 14px;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .text-14-18 {
        font-size: 18px !important;
        line-height: 24px;
    }
    .min-w-xl-160{
        min-width: 160px !important;
    }
}
.text-16-20 {
    font-size: 16px !important;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .text-16-20 {
        font-size: 20px !important;
        line-height: 21px;
    }
}
.text-12-13 {
    font-size: 12px !important;
    line-height: 16px;
}
@media (min-width: 1024px) {
    .text-12-13 {
        font-size: 13px !important;
        line-height: 17px;
    }
}
.text-12-14 {
    font-size: 12px;
    line-height: 16px;
}
@media (min-width: 1024px) {
    .text-12-14 {
        font-size: 14px;
        line-height: 19px;
    }
}
.text-12-16 {
    font-size: 12px;
    line-height: 16px;
}
@media (min-width: 1024px) {
    .text-12-16 {
        font-size: 16px;
        line-height: 21px;
    }
}
.text-13-17 {
    font-size: 13px;
    line-height: 17px;
}
.text-20-28 {
    font-size: 20px !important;
    line-height: 27px;
}
@media (min-width: 1024px) {
    .text-20-28 {
        font-size: 28px !important;
        line-height: 37px;
    }
}
.text-20-32 {
    font-size: 20px !important;
    line-height: 27px;
}
@media (min-width: 1024px) {
    .text-20-32 {
        font-size: 32px !important;
        line-height: 43px;
    }
}
.text-18-20 {
    font-size: 18px !important;
    line-height: 24px;
}
@media (min-width: 1024px) {
    .text-18-20 {
        font-size: 20px !important;
        line-height: 27px;
    }
}
.text-24 {
    font-size: 24px ;
    line-height: 32px;
}
.text-13-15{
    font-size: 13px;
}
@media (min-width: 1024px) {
    .text-13-15{
        font-size: 15px;
    }
}
.rounded-16 {
    border-radius: 16px !important;
}
.rounded-24 {
    border-radius: 24px !important;
}
.e_confirm_text{
    font-size: 14px;
}
@media (min-width: 1024px) {
    .rounded-32 {
        border-radius: 32px !important;
    }
    .e_confirm_text{
        font-size: 16px;
    }
}
.shadow-general {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}
.min-w-104 {
    min-width: 104px;
}
.min-w-80 {
    min-width: 80px;
}
.mr-minus {
    margin-right: -5px;
}
.cursor-pointer{
    cursor: pointer;
}
#i_application_container #i_application_form .form-group{
    margin-bottom: 18px;
}
.text-999999{
    color: #999999;
}
.box_content-bg-fff .table-striped > tbody > tr:nth-of-type(even) {
    background-color: #fff;
}
.box_content-bg-fff .table-striped > tbody > tr:nth-of-type(odd):hover > * {
    background-color: #ececec;
}
.btn-main9 {
    bottom: 16px;
}
@media (min-width: 1024px) {
    .btn-main9 {
        bottom: 24px;
    }
}
.height-password {
    height: 56px;
}
@media (min-width: 1024px) {
    .height-password {
        height: 64px;
    }
}
.carousel-inner>.item {
    justify-items: center;
}
.form_holder-parentnew .item-box input {
    background-color: #f5f5f5;
    font-size: 14px;
    line-height: 19px;
    padding: 18.5px 19px;
    border-radius: 16px;
    width: 100%;
    border: 0;
    margin-top: 8px;
    font-weight: 500;
}
.only_textarea-box {
    font-size: 14px;
    line-height: 19px;
    padding: 18.5px 19px;
    border-radius: 16px;
    width: 100%;
    border: 0;
    margin-top: 8px;
    font-weight: 500;
}
.p-only-calender {
    padding: 18.5px 19px !important;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box input {
        padding: 16px 40px 16px 24px;
        font-size: 16px;
        line-height: 21px;
        margin-top: 9px;
    }
    .p-only-calender {
        padding: 16px 40px 16px 24px !important;
    }
    .only_textarea-box {
        padding: 16px 40px 16px 24px;
        font-size: 16px;
        line-height: 21px;
        margin-top: 9px;
    }
    .form_holder-parentnew .item-box input[type="text"] {
        min-height: 64px;
    }
}
.form_holder-parentnew .item-box .only-textarea {
    background-color: #f5f5f5;
    font-size: 14px;
    line-height: 19px;
    min-height: 56px;
    padding: 6px 19px;
    align-content: center;
    border-radius: 16px;
    width: 100%;
    border: 0;
    margin-top: 10px;
    font-weight: 500;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box .only-textarea {
        padding: 22.5px 40px 22.5px 24px;
        font-size: 16px;
    }
}
.border-color-ccc {
    border-color: #CCCCCC !important;
}
.border-color-666 {
    border-color: #666 !important;
}
.container{
    padding-right: 8px;
    padding-left: 8px;
}
@media (min-width: 1024px) {
    .container{
        padding-right: 15px;
        padding-left: 15px;
    }
}
.end-16-8 {
    right: 16px;
}
@media (min-width: 1024px) {
    .end-16-8 {
        right: 8px;
    }
    .text-xl-center {
        text-align: center !important;
    }
}
.mb-40-80 {
    margin-bottom: 40px !important;
}
.mb-24-42 {
    margin-bottom: 24px !important;
}
.mb-12-0{
    margin-bottom: 12px !important;
}
.mb-28-44 {
    margin-bottom: 24px !important;
}
.min-w-235 {
    min-width: 235px;
}
.gap-14-23 {
    gap: 14px;
}
.pt-9-26 {
    padding-top: 9px ;
}
.pb-16-24 {
    padding-bottom: 16px;
}
.mb-17-26 {
    margin-bottom: 17px;
}
.mb-24-40 {
    margin-bottom: 24px !important;
}
.pb-24-40 {
    padding-bottom: 24px !important;
}
@media (min-width: 1024px) {
    .mb-40-80 {
        margin-bottom: 80px !important;
    }
    .mb-12-0{
        margin-bottom: 0 !important;
    }
    .mb-24-42 {
        margin-bottom: 42px !important;
    }
    .gap-14-23 {
        gap: 23px;
    }
    .mb-28-44 {
        margin-bottom: 44px !important;
    }
    .pt-9-26 {
        padding-top: 26px ;
    }
    .pb-16-24 {
        padding-bottom: 24px;
    }
    .mb-17-26 {
        margin-bottom: 26px;
    }
    .mb-24 {
        margin-bottom: 24px !important;
    }
    .pb-24 {
        padding-bottom: 24px !important;
    }
    .mb-xl-64 {
        margin-bottom: 64px !important;
    }
    .mb-24-40 {
        margin-bottom: 40px !important;
    }
    .pb-24-40 {
        padding-bottom: 40px !important;
    }
}
.mb-13-26 {
    margin-bottom: 13px !important;
}
@media (min-width: 1024px) {
    .mb-13-26 {
        margin-bottom: 26px !important;
    }
}
.mb-8-9 {
    margin-bottom: 8px !important;
}
@media (min-width: 1024px) {
    .mb-8-9 {
        margin-bottom: 9px !important;
    }
}
.w-100-276 {
    width: 100% ;
}
@media (min-width: 1024px) {
    .w-100-276 {
        width: 276px;
    }
}
.mb-26-42 {
    margin-bottom: 26px !important;
}
@media (min-width: 1024px) {
    .mb-26-42 {
        margin-bottom: 42px !important;
    }
}
.booking_mailing_list .widget-body .dataTables_wrapper .table tr td:first-child{
    text-align: center;
}
.booking_mailing_list .widget-body .dataTables_wrapper .table thead tr th:first-child{
    min-width: 170px;
}
.page-payment .type_COMBINI {
    align-items: start;
}
.page-payment .form_box_content .table_fee_form {
    background-color: #f5f5f5;
    margin-left: -16px;
    margin-right: -16px;
    padding: 25px 24px;
    margin-bottom: 0 !important;
}
@media (min-width: 1024px) {
    .page-payment .form_box_content .table_fee_form {
        margin: 0 -80px;
        padding: 25px 80px;
    }
}
.page-payment .item_fee_bill {
    justify-content: end;
    background-color: #f5f5f5;
    margin-left: -16px;
    margin-right: -16px;
    padding: 0px 24px;
    flex-direction: column;
}
@media (min-width: 1024px) {
    .page-payment .item_fee_bill  {
        margin: 0 -80px;
        padding: 0px 80px;
    }
}
.page-payment .item_fee_bill .table-fee tr:nth-child(odd) ,
.page-payment .item_fee_bill .table-fee tr:nth-child(even) ,
.i_form_confirm_admin .item_fee_bill .table-fee tr:nth-child(odd) ,
.i_form_confirm_admin .item_fee_bill .table-fee tr:nth-child(even) {
    background-color: unset;
}
.page-payment .custom_input_quantity ,
.i_form_confirm_admin .custom_input_quantity {
    background-color: #fff;
    min-width: 64px;
    width: 64px !important;
    height: 40px;
    text-align: left !important;
}
@media (min-width: 1024px) {
    .page-payment .custom_input_quantity ,
    .i_form_confirm_admin .custom_input_quantity {
        min-width: 96px;
        width: 96px !important;
        height: 48px;
    }
}
.item_fee_ship_address {
    background-color: #f5f5f5;
    padding: 0 24px;
    margin-left: -16px;
    margin-right: -16px;
    margin-bottom: 0 !important;
}
@media (min-width: 1024px) {
    .item_fee_ship_address   {
        margin: 0 -80px;
        padding: 0 80px;
    }
}

.term-of-service__content {
    background: #ffffff;
    margin-top: 64px;
    border-radius: 40px;
    padding: 40px 64px 0 64px;
    font-size: 16px;
    width: unset;
}

.term-of-service__content .title {
    padding: 0 24px 40px 24px;
    font-size: 40px;
}

.term-of-service__content .bold_text {
    font-size: 20px;
    font-weight: bold;
    margin: 40px 0 10px 0;
}
.term-of-service__content p {
    margin: 16px 0 10px !important;
}
.term-of-service__content .anchor_link-box {
    margin-top: 40px;
}
@media (max-width: 768px) {
    .term-of-service__content {
        padding: 16px 16px 0 16px !important;
        width: unset;
        margin: 50px 0;
    }
    .term-of-service__content .anchor_link-box {
        margin-top: 10px;
    }
    .term-of-service__content .bold_text {
        padding: 0 24px 24px 0;
        font-size: 16px;
        font-weight: bold;
    }
    .term-of-service__content .title {
        font-size: 26px;
        padding: 12px 24px 24px;
    }
    .term-of-service__content p {
        font-size: 16px;
    }
}
.min-width-100 {
    min-width: 100px;
}
@media (min-width: 1024px) {
    .page-payment .form_holder-parentnew .item-box .only-textarea {
        padding: 13px 24px;
    }
}
.page-payment .title-form-new ,
.reservation_application-box .i_form_confirm_admin .title-form-new {
    font-size: 24px;
    line-height: 56px;
}
@media (min-width: 1024px) {
    .page-payment .title-form-new ,
    .reservation_application-box .i_form_confirm_admin .title-form-new {
        font-size: 40px;
        margin-bottom: 12px;
    }
}
.page-payment .txt-form-new ,
.i_form_confirm_admin .txt-form-new {
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 23px;
}
@media (min-width: 1024px) {
    .page-payment .txt-form-new ,
    .i_form_confirm_admin .txt-form-new {
        font-size: 32px;
    }
}
.page-payment .txt-form-new:before ,
.page-payment .txt-form-new:after,
.i_form_confirm_admin .txt-form-new:before ,
.i_form_confirm_admin .txt-form-new:after{
    content: '';
    width: 40px;
    height: 2px;
    background-color: #000000;
    display: block;
}
@media (min-width: 1024px) {
    .page-payment .txt-form-new:before ,
    .page-payment .txt-form-new:after ,
    .i_form_confirm_admin .txt-form-new:before ,
    .i_form_confirm_admin .txt-form-new:after{
        width: 56px;
        height: 4px;
    }
}
.page-payment .from_box_title-new ,
.i_form_confirm_admin .from_box_title-new {
    margin-right: 24px;
}
@media (min-width: 1024px) {
    .page-payment .from_box_title-new ,
    .i_form_confirm_admin .from_box_title-new {
        margin-right: 52px;
    }
}
.i_form_confirm_admin .item_fee_ship_address {
    display: none;
}
.i_form_confirm_admin .table_fee_form ,
.i_form_confirm_admin .item_fee_bill  {
    margin-bottom: 18px;
}
.i_form_confirm_admin .text_rq-confirm {
    margin-bottom: 12px;
}
.i_form_confirm_admin .payment_method-confirm {
    margin-bottom: 12px;
}
.i_form_confirm_admin .transaction_type_description-credit-card {
    padding: 0;
}
.i_form_confirm_admin .item_fee_bill ,
.i_form_confirm_admin .form_box_content .table_fee_form {
    background-color: #f5f5f5;
    padding: 24px;
}
.i_form_confirm_admin .table-none > tbody > tr > td ,
.i_form_confirm_admin .table-none > tfoot > tr > td {
    border: none;
    padding: 12px;
}
.e_confirm_button_text{
    font-size: 14px;
    font-weight: bold;
}
.px-8 {
    padding: 0 8px;
}
@media (min-width: 1024px) {
    .e_confirm_button_text{
        font-size: 16px;
    }
}
.i_form_confirm_admin {
    border-radius: 24px;
    border: 8px solid #F5F5F5;
    padding: 0 16px;
    margin-bottom: 20px;
}
@media (min-width: 1024px) {
    .i_form_confirm_admin {
        border-radius: 40px;
        border: 12px solid #F5F5F5;
        padding: 0 80px;
        margin-bottom: 40px;
    }
}
#i_event_application_form_confirm .guide_text {
    font-size: 14px;
    line-height: 19px;
    margin-bottom: 18px;
}
@media (min-width: 1024px) {
    #i_event_application_form_confirm .guide_text {
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 17px;
    }
}
.pd-24-41-66-64 {
    padding: 41px 24px !important;
    margin-bottom: 0 !important;
}
@media (min-width: 1024px) {
    .pd-24-41-66-64 {
        padding: 66px 44px !important;
    }
}
.outline-none {
    outline: none;
}
.placeholder-none:focus::placeholder {
    color: transparent;
}
.breadcrumb-mobile{
    margin-bottom: 42px;
}
.min-h-56-64 {
    min-height: 56px !important;
}
@media (min-width: 1024px) {
    .min-h-56-64 {
        min-height: 64px !important;
    }
}
.minw-120-200 {
    min-width: 120px;
}
@media (min-width: 1024px) {
    .minw-120-200 {
        min-width: 200px;
    }
}
.minw-120-160 {
    min-width: 120px !important;
}
@media (min-width: 1024px) {
    .minw-120-160 {
        min-width: 160px !important;
    }
}
.mb-17-26 {
    margin-bottom: 17px;
}
@media (min-width: 1024px) {
    .mb-17-26 {
        margin-bottom: 26px;
    }
    .mb-30 {
        margin-bottom: 32px !important;
    }
}
.mb-11-22 {
    margin-bottom: 11px;
}
@media (min-width: 1024px) {
    .mb-11-22 {
        margin-bottom: 22px;
    }
}
.mb-19-27 {
    margin-bottom: 19px;
}
@media (min-width: 1024px) {
    .mb-19-27 {
        margin-bottom: 27px;
    }
}
.mb-26-41 {
    margin-bottom: 26px !important;
}
@media (min-width: 1024px) {
    .mb-26-41 {
        margin-bottom: 41px !important;
    }
}
.mb-23-32 {
    margin-bottom: 23px !important;
}
@media (min-width: 1024px) {
    .mb-23-32 {
        margin-bottom: 32px !important;
    }
}
.mb-20-35{
    margin-bottom: 20px;
}
@media (min-width: 1024px) {
    .mb-20-35{
        margin-bottom: 35px;
    }
}
.custom_step .col-6.col-lg-3:last-child .item-step {
    clip-path: unset;
}
.box_content-scroll {
    max-height: 550px;
    overflow-x: hidden;
    overflow-y: auto;
    margin-right: -10px;
}
@media (min-width: 1024px) {
    .box_content-scroll {
        max-height: 600px;
    }
}
@media (min-width: 1024px) {
    .text-xl-nowrap{
        white-space: nowrap;
    }
}
.text-note-img {
    margin-left: -25px;
    margin-top: 8px;
}
@media (min-width: 1024px) {
    .text-note-img {
        margin: 0;
        white-space: normal;
        width: 200%;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        flex-shrink: 0;
    }
}
@media (min-width: 1200px) {
    .text-note-img {
        width: auto;
    }
}

@media (max-width: 768px) {
    .select_custom-onlyyellow {
        font-size: 13px;
    }
    .add-form-button-container .btn-general.btn-yellow-2 {
        line-height: 48px;
    }
}
.table_box-new {
    overflow-x: auto;
    background: #fff;
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-top: 26px;
}
@media (min-width: 1024px) {
    .table_box-new {
        margin-top: 41px;
        padding: 3rem;
    }
}
.table_box-new table {
    width: 100%;
    border-collapse: collapse;
}

.table_box-new table colgroup col {
    width: 40%;
}

.table_box-new td {
    padding: 4px 6px;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: top;
    font-size: 12px;
    line-height: 17px;
    color: #666;
}
@media (min-width: 1024px) {
    .table_box-new td {
        padding: 14px 16px;
        font-size: 16px;
        line-height: 21px;
    }
}
.table_box-new tr:last-child td {
    border-bottom: none;
}

.table_box-new .link-of-table {
    text-decoration: none;
    font-weight: 500;
}
.table_box-new .link-of-table:hover {
    background-color: unset;
}

.table_box-new .link-of-table:hover {
    text-decoration: underline;
}
.text-break-all {
    word-break: break-all !important;
}
.fw-medium{
    font-weight: 500 !important;
}
.min-w-202-246{
    min-width: 202px;
}
@media (min-width: 1024px) {
    .min-w-202-246{
        min-width: 246px;
    }
    .min-xl-w-228{
        min-width: 228px;
    }
    .min-xl-w-200{
        min-width: 200px;
    }
    .w-xl-75 {
        width: 75% !important;
    }

}
#i_event_form_confirm .form_holder-parentnew .item-box input[type="text"] ,
#i_event_form_confirm .form_holder-parentnew .item-box select {
    border: 1px solid #ccc;
    outline: none;
    background-color: #fff;
}
#i_event_form_confirm .ui-datepicker-trigger {
    display: none !important;
}
.white-space-normal{
    white-space: normal;
}
@media (min-width: 1024px) {
    .survey_question_custom #select_q_copy{
        margin-bottom: 42px;
    }
}
.survey_question_custom1 .adding-checkbox {
    justify-content: center !important;
}
.table_payment .tips{
    display: none;
}
.has-success .checkbox, .has-success .checkbox-inline, .has-success .control-label, .has-success .help-block, .has-success .radio, .has-success .radio-inline, .has-success.checkbox label, .has-success.checkbox-inline label, .has-success.radio label, .has-success.radio-inline label {
    color: unset;
}
.mb-25-42{
    margin-bottom: 25px !important;
}
@media (min-width: 1024px) {
    .mb-25-42{
        margin-bottom: 42px !important;
    }
}
.setting_ticket_confirm .description_none_confirm{
    display: none;
}
.setting_ticket_confirm .e_sort_field{
    display: none;
}
.setting_ticket_confirm .mb-7-17{
    margin-bottom: 7px;
}
@media (min-width: 1024px) {
    .setting_ticket_confirm .mb-7-17{
        margin-bottom: 17px;
    }
}
.setting_ticket_confirm .event-fee-form .table tbody tr:last-child td{
    border-bottom: 1px solid #000000;
}
.setting_ticket_confirm .table>thead:first-child>tr:first-child>th{
    background: #666666;
    color: #ffff;
    font-weight: bold;
}
.setting_ticket_confirm .event-fee-form .table tr th, .event-fee-form .table tr td{
    border: 1px solid #000000;
    font-weight: bold;
}
.mb-17-42{
    margin-bottom: 17px;
}
@media (min-width: 1024px) {
    .mb-17-42{
        margin-bottom: 42px;
    }
}
.mb-25-30{
    margin-bottom: 25px;
}
@media (min-width: 1024px) {
    .mb-25-30{
        margin-bottom: 30px;
    }
}
.text-14-20{
    font-size: 14px !important;
}
@media (min-width: 1024px) {
    .text-14-20{
        font-size: 20px !important;
    }
}
.text-13-16{
    font-size: 13px !important;
}
@media (min-width: 1024px) {
    .text-13-16{
        font-size: 16px !important;
    }
}
.min-height-30-36{
    min-height: 30px;
}
@media (min-width: 1024px) {
    .min-height-30-36{
        min-height: 36px;
    }
}
.hover-color-unset:hover{
    color: unset;
}
.work-break-all {
    word-break: keep-all !important;
}
.min-height-500{
    min-height: 500px;
}
.min-w-auto{
    min-width: auto !important;
}
.word-break {
    word-break: break-all;
}
.mt-25-40{
    margin-top: 25px;
}
@media (min-width: 1024px) {
    .mt-25-40{
        margin-top: 40px;
    }
}
.min-w-104-200{
    min-width: 104px;
}
@media (min-width: 1024px) {
    .min-w-104-200{
        min-width: 200px;
    }
}
.lh-19-21{
    line-height: 19px !important;
}
@media (min-width: 1024px) {
    .lh-19-21{
        line-height: 21px !important;
    }
}
.rule_modal-1 .modal-header .close {
    top: 25px;
    right: 25px;
    font-size: 20px;
    box-shadow: unset;
}
.rule_modal-1 .modal-content {
    padding: 41px 24px ;
}
@media (min-width: 1024px) {
    .rule_modal-1 .modal-content {
        padding: 66px 64px ;
    }
}
.rule_modal p,.rule_modal span{
    font-size: 14px;
}
.rule_modal span.bold_text{
    font-size: 16px;
}
@media (min-width: 1024px) {
    .rule_modal p,.rule_modal span{
        font-size: 16px;
    }
    .rule_modal span.bold_text{
        font-size: 18px;
    }
}
.list-choice-shipping .label_error{
    display: block;
    width: 100%;
    text-align: center;
    margin-top: 10px;
}
@media (min-width: 1024px) {
    .list-choice-shipping .label_error{
        margin-top: 0;
    }
}