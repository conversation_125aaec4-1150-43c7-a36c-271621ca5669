a.e_ajax_link .search-text {
    color: white;
    text-decoration: none;
}

a.e_ajax_link:hover {
    text-decoration: none;
}

.search_result_title {
    padding-left: 30px;
    padding-top: 15px;
    font-size: 19px;
    font-weight: bold;
}

.result_box > .result_box_title {
    height: 37px;
    background-color: #ECECEC;
    text-align: center;
    line-height: 37px;
    font-size: 17px;
}

.result_box > .result_box_content {
    width: 100%;
    margin-bottom: 10px;
    padding-left: 95px;
    padding-right: 95px;
    max-height: 300px;
    overflow: auto;
}

.result_box_content .result_item {
    text-align: left;
    margin: 0 10%;
}

.result_box_content .no_result {
    margin-top: 20px;
    margin-bottom: 10px;
    text-align: center;
    color: red;
}

.result_box_content .result_item a {
    color: black;
}

.search_result_content > .back_button_holder {
    margin: 27px auto 0px auto;
    text-align: center;
}

.back_button_holder > .modal_back_button {
    margin-left: 0px;
    height: 44px;
    width: 148px;
}

.modal_back_button > span.glyphicon.modal_back_triangle {
    font-size: 10px;
    top: 16px;
}

.modal_back_button > .back_button_text {
    font-weight: bold;
    font-size: 18px;
    position: relative;
    top: 2px;
}