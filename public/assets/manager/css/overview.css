.overview-form .event-detail-content {
    padding: 0 54px;
}

.overview-container {
    width: 924px;
    margin: 0 auto;
    font-family: <PERSON><PERSON>;
}

.overview-main-title {
    border-bottom: 1px dotted #000000;
    font-size: 18px;
    padding-top: 40px;
    padding-bottom: 22px;
    line-height: 30px;
}

.first-overview-box {
    padding-top: 24px;
    padding-bottom: 32px;
}
.settingemail__body .first-overview-box {
    padding-bottom: 64px;
}

.first-overview-box hr {
    border-top: none;
    margin-top: 17px;
    margin-bottom: 17px;
    line-height: 25px;
}

.edit-button.black-button.small-button {
    position: absolute;
    top: 26px;
    right: 0px;
}

.edit-button span {
    font-size: 7px;
    color: #ffe400;
    position: absolute;
    top: 9px;
    right: 7px;
}

.overview-little-box {
}

.little-box-content-custom {
    padding-top: 0px !important;
}

.little-box-custom {
    border-bottom: none !important;
}

.overview-little-box-custom {
    margin-top: 36px;
}

.little-box-custom .star {
    padding-right: 5px;
    position: absolute;
    top: calc(50% - 25px);
    left: 0;
}

.update_link_container {
    position: absolute;
    right: 15px;
    top: 2px;
}

.update_link_option {
    float: left;
    padding-left: 15px;
}

.update_link_option input:hover,
.update_link_option label:hover {
    cursor: pointer;
}

.update_link_option label {
    font-weight: normal;
}

.ticket-box {
    width: 305px;
    height: auto;
    margin: 50px auto 0;
    /*border: 1px solid #bcbcbc;*/
    background-color: #f2f2f2;
}

.overview_detail_overview_option {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.video_url_overview {
    font-size: 14px !important;
}

.video_url_mb_15px {
    margin-bottom: 15px;
}

.notify_content.new_design {
    padding-bottom: unset;
    font-size: 16px;
    font-weight: bold;
    color: #FD6600;
}
.form_box.new_design {
    border-radius: 24px;
    margin-top: unset !important;
    border: 4px solid #FDBC00 !important;
    background-color: white;
}

.form_area.new_design {
    margin: unset;
}

.regist-content-box {
    border: 8px solid #ececec;
    margin-top: 36px;
    margin-bottom: 28px;
}

.regist-content-title {
    background-color: #ececec;
    padding-left: 30px;
    line-height: 68px;
    color: #4a4a4a;
    font-weight: bold;
    font-size: 20px;
    height: 68px;
}

.regist-content-body {
    padding: 20px 49px 45px
}

.regist-content-row {
    border-bottom: 1px solid #dbdcdd;
    height: 55px;
    margin-bottom: 0;
}

.regist-content-row .col-xs-6 {
    display: inline-flex;
    padding: 0;
}

.right-input {
    padding-top: 12px;
    padding-left: 5px;
}

.right-input input[type=radio] {
    margin: 9px 5px 0 20px;
}

.right-input .shadow_input {
    height: 30px;
    line-height: 30px;
}

.shadow_select {
    width: 89px;
    height: 30px;
    line-height: 30px;
    box-shadow: inset -1px -1px 2px 0px #CCCCCC;
    border: 1px solid #C4C4C4;
    padding-left: 7px;
    padding-right: 7px;
    margin-right: 5px;
}

.small-input {
    width: 160px;
}

.big-input {
    width: 256px;
}

.last-overview-box {
    font-size: 13px;
    padding-top: 36px;
    line-height: 24px;
    /*padding-bottom: 45px;*/
}

.last-overview-box a {
    text-decoration: underline;
}

.event-overview-button {
    height: 44px;
    width: 146px;
    line-height: 44px;
    text-shadow: 0px 0px 1px #000000;
}

.edit_overview_modal {
    max-width: 1000px;
    width: auto;
}

.edit_overview_modal .edit_button_holder {
    margin-bottom: 30px;
    text-align: center;
}

.edit_overview_modal .text_area_holder {
    padding: 0 64px;
}

.edit_overview_modal .notification_popup_container {
    margin: 50px 0 24px;
}

.edit_overview_modal .text_area_holder > textarea {
    display: block;
    margin: 0 auto;
    padding: 44px;
    border-radius: 16px;
    background: #F5F5F5;
    box-shadow: unset;
    border: unset;
    width: 100%;
    max-height: 350px;
}

.edit_overview_modal.modal-dialog-centered {
    top: 45%;
}

.btn_modal_edit_overview button.black-button.big-button {
    font-weight: bold;
    font-size: 16px;
    width: 120px;
    min-width: unset;
}

.edit_overview_modal .text_area_holder > textarea:focus-visible {
    border-color: #FDBC00;
}

.overview-little-box .method_description {
    padding-top: 28px;
    padding-bottom: 35px;
    font-size: 14px;
}

.overview-little-box .content_box {
    width: 794px;
    margin: auto;
}

.list_email_contain {
    background-color: #DAE5F1;

}

.list_mail_title {
    padding: 15px;
}

.list_email_content {
    padding-top: 25px;
}

.list_email_row .input_title {
    background-color: transparent;
    padding: 0;
}

.list_email_row .has-success .control-label,
.list_email_row .has-error .control-label {
    color: #0a0a0a;
}

.list_email_row input {
    width: 100%;
    padding-left: 5px;
    min-height: 30px;
}

#main-container .event-detail-container .event-detail-content .overview-little-box .wrapUrl {
    border-bottom: 1px dotted #000000;
    min-height: 90px;
    font-size: 17px;
    position: relative;
}

#main-container .event-detail-container .event-detail-content .overview-little-box .wrapUrl .wrapTextArrow {
    display: flex;
    align-items: center;
}

#main-container .event-detail-container .event-detail-content .overview-little-box .wrapUrl .wrapTextArrow div {
    padding-left: 7px;
}

.titleSmallUrl {
    margin: 10px 0px 10px 0px;
}

#main-container .event-detail-container .event-detail-content .overview-little-box .wrapUrl .wrapInput .inputUrl {
    width: 100%;
    margin: 10px 0 0 0;
    line-height: 20px;
    box-shadow: inset 2px 2px 1px 0px #cccccc;
    border: 1px solid #C4C4C4;
    padding-left: 10px;
    padding-right: 7px;
    padding-top: 5px;
    padding: 5px 7px;
    display: inline-block;
    font-size: 13px;
    vertical-align: top;
    height: 35px;
}

/*#main-container .event-detail-container .event-detail-content .overview-little-box .wrapUrl .text-warning-url {*/
/*    color: red;*/
/*    font-size: 13px;*/
/*}*/


/* event */

.event_container .overview-container .overview-little-box .wrapUrl {
    border-bottom: 1px dotted #000000;
    height: 120px;
    font-size: 17px;
    position: relative;
}

.event_container .overview-container .overview-little-box .wrapUrl .wrapTextArrow {
    display: flex;
    align-items: center;
}

.event_container .overview-container .overview-little-box .wrapUrl.wrapTextArrow div {
    padding-left: 7px;
}

.titleSmallUrl {
    margin: 12px 0px 15px 0px;
}

.event_container .overview-container .overview-little-box .wrapUrl .wrapInput .inputUrl {
    width: 100%;
    margin: 10px 0 0 0;
    line-height: 20px;
    box-shadow: inset 2px 2px 1px 0px #cccccc;
    border: 1px solid #C4C4C4;
    padding-left: 10px;
    padding-right: 7px;
    padding-top: 5px;
    padding: 5px 7px;
    display: inline-block;
    font-size: 13px;
    vertical-align: top;
    height: 35px;
}

.detail-box__overview-form--body .overview-little-box .wrapUrl .text-warning-url,
.event_container .overview-container .overview-little-box .wrapUrl .text-warning-url {
    color: #666666;
    font-size: 14px;
    margin: 0 !important;
    line-height: 19px;
}

.event_container .overview-container .overview-little-box .wrap-url-error {
    height: 150px;
}

.event_container .overview-container .overview-little-box .wrap-url-error .text-error-banking {
    color: #FD6600;
    font-size: 13px;
    padding-top: 20px;
}

.row-custom {
    display: flex;
    align-items: center;
    margin: 0px auto 0px auto;
    padding: 15px 0px 15px 0px;
    width: 90%;
}

.row-custom label {
    margin-bottom: 0px;
}

.error-cashier {
    /*background-color: rgb(255 255 0);*/
    width: fit-content;
}

.event_container.overview-form.settingemail__body--child {
    padding: 58px 80px 64px;
    border: 12px solid #F5F5F5;
    border-radius: 32px;
}

.settingemail__body--child-bottom {
    margin-top: 16px;
}

.settingemail__body {
    margin-top: 64px;
}

.settingemail__body--child .overview-main-title {
    padding: 0 0 30px 0;
    font-weight: bold;
}

.settingemail__body--child .overview-main-title {
    border-bottom: 1px solid #CCCCCC;
}

.settingemail__body--child .overview-container {
    width: auto;
}

.settingemail__body--child #i_content_mail_e_ticket {
    font-size: 18px;
    /*letter-spacing: -1px;*/
}

.settingemail__body--child .black-button.small-button.edit-button {
    padding: 0 24px !important;
    height: 56px !important;
    background-color: #FDBC00;
    width: auto;
    font-weight: bold;
    min-width: unset !important;
    font-size: 16px;
    border-radius: 40px;
}

.settingemail__body--child .black-button.small-button.edit-button svg {
    margin-bottom: 7px;
}

.event_detail_overview_not_option,
.settingemail__body--bottom--wrapper {
    background-color: #F5F5F5;
    border-radius: 24px;
    padding: 40px;
}

.settingemail__body--bottom {
    border: 4px solid #FDBC00;
    border-radius: 16px;
    padding: 26px 24px;
    background-color: white;
}

.detail__manual--box .wrapUrl div,
.settingemail__body--bottom--wrapper .titleSmallUrl,
.settingemail__body--bottom--wrapper .title__first--arrow-left {
    font-weight: bold;
    font-size: 16px;
    line-height: 21px;
}

.settingemail__body--bottom--wrapper .titleUrl {
    font-weight: bold;
    font-size: 18px;
}

.settingemail__body--bottom--wrapper_2 .overview-little-box,
.detail__manual--box .overview-little-box,
.settingemail__body--bottom--wrapper .overview-little-box {
    border: 1px solid #FDBC00;
    border-radius: 16px;
    padding: 22px 24px;
    margin-top: 24px;
    background-color: white;
}
.settingemail__body--bottom--wrapper_2 .little-box-title,
.settingemail__body--bottom--wrapper .little-box-title {
    border-bottom: unset;
    position: relative;
    font-weight: bold;
    height: unset;
    line-height: 21px;
    padding-left: unset;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list_email_content.email-content__bottom-box .row-one:first-child {
    margin-bottom: 26px;
}
.list_email_content.email-content__bottom-box .row-one .form-group {
    margin-bottom: unset !important;
}

.settingemail__body--child .overview-little-box .wrapUrl {
    border-bottom: unset !important;
    height: unset !important;
    font-size: unset !important;
    position: unset !important;
}

.settingemail__body--child .overview-little-box .wrapUrl .wrapTextArrow {
    display: flex !important;
    align-items: start !important;
    gap: 8px !important;
}
.settingemail__body--bottom--wrapper_2 .little-box-title span.arrow-left,
.settingemail__body--bottom--wrapper .little-box-title span.arrow-left {
    padding-right: unset;
    position: unset;
}

.settingemail__body--bottom--wrapper .wrapTextArrow .arrow-left {
    margin-top: 3px;
}

.detail-box__overview-form--body .arrow-left,
.settingemail__body--bottom--wrapper .arrow-left {
    border-left: 12px solid #FDBC00;
}

.overview__qr {
    width: auto;
    max-width: 305px;
    height: auto;
    background-color: #f2f2f2;
    border-radius: 24px;
}

.overview__qr table.right-box-content {
    border: unset !important;
    border-radius: 20px;
}

.settingemail__body--bottom .title__box--bottom {
    font-weight: bold;
    font-size: 24px;
    margin-bottom: 16px;
}

.settingemail__body--bottom {
    margin-top: 64px;
}

.event_container.overview-form.settingemail__body--child-bottom {
    padding: 60px 80px;
    margin-bottom: 40px;
}

.settingemail__body--child-bottom .overview-little-box-custom {
    margin: unset;
}

.detail-box__overview-form--body.detail__manual--box .title--top,
.settingemail__body--child-bottom .overview-little-box-custom .title--top {
    font-size: 18px !important;
    margin-bottom: 35px;
}
.settingemail__body--bottom .little-box-content-custom .title--top {
    font-size: 18px !important;
    /*letter-spacing: -3px;*/
}

.email-content__bottom-box .input_title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
    line-height: 21px;
}

.email-content__bottom-box .input_field {
    width: 100%;
    padding: 8px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background-color: #f8f9fa;
}

.settingemail__body--bottom.bottom-box .overview__bottom-box--button,
.detail__manual--box .overview__bottom-box--button {
    justify-content: center;
    display: flex;
    font-weight: bold;
}

.settingemail__body--bottom.bottom-box .overview__bottom-box--button {
    margin-bottom: 80px;
}

.detail__manual--box .embedded-code-button {
    margin-top: unset !important;
}

.overview__bottom-box--button .black-button.prev-btn {
    background-color: black;
    color: white;
}

.overview__bottom-box--button .black-button.big-button.next-btn {
    background-color: #FDBC00;
}

.overview__bottom-box--button .main-button-container {
    display: flex;
    gap: 16px;
}

.overview__bottom-box--button a {
    text-decoration: none;
}

.email-content__bottom-box .form-group {
    width: 50%;
}

.email-content__bottom-box .form-group input {
    height: 64px;
    border-radius: 16px;
    background: #F5F5F5;
    box-shadow: unset;
    border: unset;
    font-size: 14px;
}
@media (min-width: 1024px) {
    .email-content__bottom-box .form-group input{
        font-size: 16px;
    }
}

.settingemail__body--bottom.bottom-box {
    border: unset;
    padding: unset;
}

.settingemail__body .ticket-box.overview__qr table tbody tr th.ticket-group {
    font-size: 18px !important;
    /*padding: 5px 0 !important;*/
}

.settingemail__body .ticket-box.overview__qr table tbody tr.no_ticket td {
    font-size: 18px !important;
    padding: 0 16px !important;
    padding-top: 42px !important;
}

.settingemail__body .ticket-box.overview__qr {
    max-width: 360px !important;
}


.detail-box__overview-form--body .settingemail__body {
    padding: 0 64px 35px 64px;
}

.settingemail__body--bottom--wrapper_2 .update_link_container .update_link_option,
.settingemail__body--bottom--wrapper .update_link_container .update_link_option {
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.settingemail__body--bottom--wrapper_2 .update_link_container .update_link_option label,
.settingemail__body--bottom--wrapper .update_link_container .update_link_option label {
    margin-top: 10px;
    margin-left: 5px;
    font-weight: bold;
    font-size: 16px;
    white-space: nowrap;
}

.settingemail__body .settingemail__body--bottom.top-box {
    margin-top: 0 !important;
}
.settingemail__body--bottom--wrapper .update_link_container .update_link_option input[type=checkbox]:focus {
    outline: unset !important;
    outline-offset: unset !important;
}
.detail__manual--box .overview_email_template .little-box-title {
    border-bottom: unset !important;
    line-height: unset !important;
    height: unset;
    font-weight: bold;
}

.overview__bottom-box--button button svg {
    margin-bottom: 2px;
}

.detail_overview_option .edit-button {
    margin: 0 !important;
    top: 0 !important;
    white-space: nowrap !important;
}
.event_detail_overview_not_option.event_detail_overview_manual_ticket {
    margin-top: 64px;
}

.settingemail__body .overview-main-title .event-title, .status-sms, .subscribe-title {
    font-size: 18px !important;
}

@media (max-width: 768px) {
    .settingemail__body .overview-main-title .event-title, .status-sms, .subscribe-title {
        font-size: 14px !important;
    }
    .settingemail__body--bottom--wrapper .wrapTextArrow .arrow-left {
        margin-top: 13px;
    }
    .settingemail__body--bottom .little-box-content-custom .title--top {
        font-size: 14px !important;
        /*letter-spacing: -1px;*/
    }
    .settingemail__body--child.settingemail__body--child-bottom.bottom-box {
        padding: 16px 16px 0 !important;
    }
    .detail__manual--box .overview_email_template .little-box-title {
        height: unset;
    }
    .detail-box__overview-form--body .settingemail__body {
        padding: 0 8px 12px;
    }
    .detail__manual--box .wrapUrl div {
        font-size: 13px;
    }
    .detail-box__overview-form--body .overview-little-box .wrapUrl .text-warning-url,
    .event_container .overview-container .overview-little-box .wrapUrl .text-warning-url {
        font-size: 12px;
    }
    .event_container.overview-form.settingemail__body--child {
        border: 8px solid #F5F5F5;
        padding: 8px;
    }
    .settingemail__body--bottom--wrapper .update_link_container {
        align-items: start;
    }

    .settingemail__body--bottom--wrapper .update_link_container .update_link_option label {
        font-size: 14px;
    }

    .event_container.overview-form.settingemail__body--child-bottom {
        margin-bottom: 24px;
    }

    .event_container .overview-form .settingemail__body--child .overview-main-title {
        font-size: 16px !important;
    }

    .email-content__bottom-box .form-group {
        width: unset;
    }

    .overview-form.settingemail__body--child.settingemail__body--child-bottom.bottom-box
    .email-content__bottom-box .form-group .input_field.form-control {
        margin-bottom: 17px;
    }
    .list_email_content.email-content__bottom-box .row-one:first-child {
        margin-bottom: unset;
    }
    .email-content__bottom-box .form-group .input_field.form-control:last-child {
        /*margin-bottom: unset;*/
    }
    .settingemail__body--bottom--wrapper {
        padding: 12px !important;
    }

    .event_container.overview-form.settingemail__body--child {
        border: 8px solid #F5F5F5;
        padding: 14px 8px 8px;
    }

    .settingemail__body--bottom.bottom-box button.black-button {
        min-width: unset;
        width: 94px;
    }

    .settingemail__body--bottom.bottom-box .overview__bottom-box--button {
        margin-bottom: 40px;
    }

    .settingemail__body--bottom.top-box {
        border: 4px solid #FDBC00;
        padding: 12px !important;
        margin: unset !important;
    }

    .settingemail__body--bottom.bottom-box {
        margin-top: 24px !important;
        border: unset;
    }

    .detail__manual--box .overview-little-box,
    .settingemail__body--bottom--wrapper .overview-little-box {
        padding: 16px;
        margin-top: 12px;
    }

    .overview-main-title,
    .settingemail__body--child #i_content_mail_e_ticket {
        font-size: 14px;
    }

    .little-box-title {
        font-size: 13px;
    }

    .settingemail__body--bottom.top-box .overview-little-box .wrapTextArrow .titleSmallUrl,
    .detail__manual--box .overview-little-box .wrapTextArrow .titleSmallUrl,
    .settingemail__body--bottom--wrapper .title__first--arrow-left {
        font-size: 12px;
    }

    .title__first--arrow-left.mobile-fs-13 {
        font-size: 13px;
    }

    .settingemail__body--bottom--wrapper .title__third,
    .settingemail__body--bottom--wrapper .title__second {
        font-size: 12px !important;
    }

    .settingemail__body--bottom--wrapper .titleUrl {
        font-size: 16px;
    }

    .settingemail__body--bottom .title__box--bottom {
        font-size: 18px;
    }

    .detail-box__overview-form--body.detail__manual--box .title--top,
    .settingemail__body--child-bottom .overview-little-box-custom .title--top {
        line-height: 19px;
        font-size: 14px !important;
        margin-bottom: 25px !important;
    }

    .email-content__bottom-box .input_title {
        font-size: 12px;
    }

    .settingemail__body--child-bottom {
        margin-top: 8px;
    }

    .settingemail__body--child-bottom .main-button-container button.black-button {
        width: unset;
        padding: 0 20px !important;
    }

    .settingemail__body--child-bottom .last-overview-box {
        padding-bottom: unset;
    }

    .settingemail__body--child .black-button.small-button.edit-button {
        font-size: 14px;
        padding: 0 16px !important;
    }

    .settingemail__body--child .black-button.small-button.edit-button svg {
        height: 14px;
    }

    .settingemail__body .ticket-box.overview__qr table tbody tr.ticket-title th {
        font-size: 12px !important;
    }

    .settingemail__body .ticket-box.overview__qr table tbody tr img {
        width: 120px !important;
        height: 120px !important;
    }

    .list_email_content.email-content__bottom-box .control-label.input_title {
        font-size: 14px;
    }

    .settingemail__body--child.settingemail__body--child-bottom.bottom-box {
        padding: 20px 20px 0;
    }

    .settingemail__body .ticket-box.overview__qr table tbody tr th.ticket-group {
        font-size: 14px !important;
        /*padding: 5px 0 !important;*/
    }

    .settingemail__body .ticket-box.overview__qr table tbody tr.no_ticket td {
        font-size: 14px !important;
        padding: 0 16px !important;
        padding-top: 25px !important;
    }

    .email-content__bottom-box .form-group input {
        height: 56px;
    }
}

/* EVENT DETAIL OVERVIEW */

.detail__manual--box .overview_email_template {
    border-radius: 16px;
    background: #F5F5F5 !important;
    padding: 16px !important;
    margin: 16px !important;
    display: block;
}

.detail__manual--box .form_box {
    border: 4px solid #FDBC00 !important;
    border-radius: 16px;
}

.detail__manual--box .form_box_content {
    width: auto;
}

.detail__manual--box .first-overview-box {
    padding-bottom: 0 !important;
}
.detail__manual--box #i_content_mail_e_ticket {
    width: unset !important;
}

.detail__manual--box .input_row.row {
    font-weight: bold;
    font-size: 16px;
}

.detail__manual--box .event_container .form_area {
    font-size: 14px;
}

.detail__manual--box .overview-form .event-detail-content {
    padding: unset !important;
    margin-bottom: unset !important;
}

.detail__manual--box .black-button.small-button.focus_btn {
    padding: 10px 20px;
    background-color: #FDBC00 !important;
    border-radius: 24px;
    font-weight: bold;;
}
.detail__manual--box .black-button.big-button {
    padding: 19px 30px !important;
    font-size: 14px;
}
.detail__manual--box .text-right a.e_ajax_link {
    text-decoration: none !important;
}

.detail__manual--box .event_container .form_area .form_box {
    padding-bottom: unset;
}

.detail__manual--box .black-button.small-button span.glyphicon {
    font-size: 18px !important;
    top: calc(50% - 10px) !important;
    color: black !important;
}

.detail__manual--box .event-detail-content .overview-little-box .wrapUrl {
    min-height: unset !important;
}

.detail__manual--box .settingemail__body--child .overview-main-title {
    padding: unset;
}

.detail__manual--box .overview-little-box .wrapUrl .wrapTextArrow div {
    padding-left: unset !important;
}
.detail__manual--box .overview-main-title {
    font-size: 13px;
}

.detail__manual--box .settingemail__body--child #i_content_mail_e_ticket {
    font-size: 16px;
}
.detail__manual--box .ticket-box {
    max-width: 360px;
    width: unset;
    border-radius: 20px;
}

.detail__manual--box .right-box-content {
    border: none !important;
}

.detail__manual--box .settingemail__body--bottom {
    padding-top: 0 !important;
}
.event_container .form_area .application-button.plus_row_custom_field {
    border: 4px solid #FDBC00;
    border-radius: 40px;
    padding: 12px 24px;
    margin-bottom: 18px;
    margin-left: unset;
}
.detail__manual--box .overview_email_template .black-button.small-button.focus_btn {
    margin: unset;
}
#event_detail_overview_back_to_top {
    margin: 34px 0 0;
}

#i_content_mail_payment_success,
#i_content_mail_announce_lose,
#i_content_mail_register_success,
#i_content_mail_announce_win {
    border: 1px solid #FDBC00;
    border-radius: 16px;
    padding: 22px 24px;
    margin-top: 24px;
    background-color: white;
}

.to_bs5 .mr-8 {
    margin-right: 8px;
}

.to_bs5 .border-left-FDBC00 {
    border-left: 12px solid #FDBC00 !important;
}

.to_bs5 .fs-14 {
    font-size: 14px !important;
}

.header--modal {
    padding: 0 64px;
}

.header--modal input {
    background: #F5F5F5;
    height: 56px !important;
    border: none !important;
    border-radius: 12px;
     margin-bottom: 12px;
    padding: 0 44px !important;
    box-shadow: unset !important;
}

.header--modal label {
    font-size: 16px;
}


.manager_event_overview_manual_ticket {
    margin-top: 64px;
}

.manager_event_overview_manual_ticket .first_child_ignore_mt .form_area:first-child {
    margin-top: unset;
}

.manager_event_overview_manual_ticket .first_child_ignore_mt .form_area .form_box:first-child {
    margin-top: unset;
}

.manager_event_overview_manual_ticket .first_child_ignore_mt .form_area .form_box {
    background-color: unset;
}

.manager_event_overview_manual_ticket .first_child_ignore_mt .form_area .form_box .form_box_content {
    margin: 19px auto 0px auto;
}

.manager_event_overview_manual_ticket .form_box_content button {
    background-color: #fff;
    min-width: 146px !important;
    width: 146px !important;
    height: 56px;
    max-height: 56px;
}

.manager_event_overview_manual_ticket .overview__bottom-box--button button {
    font-weight: bold;
    font-size: 18px !important;
}

.manager_event_overview_manual_ticket .overview__bottom-box--button button.back-button {
    color: white;
}

.manager_event_overview_manual_ticket .overview__bottom-box--button button.back-button svg{
    margin: 0 8px 2px 0;
}

.manager_event_overview_manual_ticket .overview__bottom-box--button .black-button.next_btn.big-button,
.manager_event_overview_manual_ticket .overview__bottom-box--button .black-button.e_ajax_confirm.big-button {
    background-color: #FDBC00;
}

.manager_event_overview_manual_ticket .event-detail-content button.focus_btn {
    max-height: unset;
    height: unset;
    width: unset;
    min-width: unset !important;
    padding: 10px 20px !important;
    font-size: 14px;
}

.manager_event_overview_manual_ticket .overview__bottom-box--button {
    margin-bottom: 80px !important;
}

.overview__bottom-box--button.event_detail_overview {
    margin-bottom: 0 !important;
}

.settingemail__body--child .overview-main-title.new_design {
    font-size: 18px;
    padding-bottom: 30px !important;
}

.new_design .box_ok {
    margin-top: 64px !important;
}

@media (max-width: 768px) {
    .manager_event_overview_manual_ticket .overview__bottom-box--button {
        margin-bottom: 40px !important;
    }

    .manager_event_overview_manual_ticket .overview__bottom-box--button button {
        font-size: 14px !important;
    }
    .manager_event_overview_manual_ticket {
        margin-top: 30px;
    }
    .header--modal {
        padding: 0 16px;
    }
    .header--modal label {
        font-size: 14px;
    }
    .wrapUrl.header--modal {
        padding: 12px 0 0;
    }
    .detail__manual--box .overview_email_template {
        padding: 12px !important;
        margin: 8px !important;
    }
    .detail__manual--box .form_box {
        border: 4px solid #FDBC00 !important;
    }

    .detail__manual--box .overview-form .event-detail-content {
        padding: 0 16px;
    }

    .detail__manual--box .settingemail__body {
        padding: 0 8px 8px;
    }

    .settingemail__body {
        margin-top: 8px;
    }

    .detail__manual--box .black-button.big-button {
        min-width: unset;
        width: unset !important;

    }

    .detail__manual--box .black-button.big-button {
        padding: 17px 30px !important;
        font-size: 14px;
    }

    .detail__manual--box .event_container .form_area .form_box {
        min-width: unset;
        margin-bottom: unset;
    }

    .detail__manual--box .input_row.row {
        font-size: 14px;
    }

    .detail__manual--box .event_container .form_area {
        font-size: 12px;
    }

    .detail__manual--box .event_container.overview-form.settingemail__body--child {
        padding: 12px;
    }

    .edit_overview_modal .text_area_holder {
        padding: 0 16px;
    }
    .edit_overview_modal .text_area_holder > textarea {
        padding: 16px;
    }
    .btn_modal_edit_overview button.black-button.big-button {
        font-size: 14px;
        width: 100px;
        min-width: unset;
    }
    .ticket-box {
        margin: 22px auto 0;
    }
    .video_url_overview {
        font-size: 12px !important;
    }
    .event_detail_overview_not_option.event_detail_overview_manual_ticket {
        margin-top: 12px;
    }
    .notify_content.new_design {
        font-size: 14px;
    }

    .new_design .box_ok {
        margin-top: 12px !important;
    }

    .settingemail__body--child .overview-main-title.new_design {
        font-size: 14px;
    }
}