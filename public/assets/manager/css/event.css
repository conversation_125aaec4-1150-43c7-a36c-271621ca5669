.modal-fee {
    display: none;
    position: fixed;
    z-index: 999;
    left: 0; top: 0;
    width: 100%; height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
}

.modal-fee-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 100%;
}
@media (min-width: 1024px) {
    .modal-fee-content {
        margin: 5% auto;
        width: 800px;
        padding: 66px 64px;
    }
}

.modal-fee-close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

/*.main-container.body-container{*/
/*    padding: 0 8px;*/
/*}*/
.form-create-event{
    background: #FFFFFF;
    padding: 40px 16px 0 16px;
    border-radius: 32px;
    margin-bottom: 40px;
}
.form-create-event .type-ticket-container .help-block {
    background-color: rgb(255 255 0);
    width: max-content;
    font-weight: bold;
}
.form-create-event-new {
    padding: 40px 8px 0 8px;
}
@media (min-width: 1024px) {
    .form-create-event {
        margin: 64px 0;
        padding: 66px 64px 0 64px;
    }
}

.form-create-event .guide_holder_content {
    text-align: center;
    font-weight: normal;
    margin-bottom: 48px;
    font-size: 14px;
}
@media (min-width: 1024px) {
    .form-create-event .guide_holder_content {
        font-size: 18px;
        line-height: 24px;
        font-weight: normal;
        margin-bottom: 68px;
    }
}
.step-parents {
    margin-bottom: 24px;
}
@media (min-width: 1024px) {
    .step-parents {
        margin-bottom: 40px;
    }
}
.item-step{
    clip-path: polygon(0% 0%, 90% 0%, 100% 50%, 90% 100%, 0% 100%);
    padding: 22px 12px;
    font-size: 14px;
    font-weight: 700;
    gap: 4px;
    background-color: #F5F5F5;
    height: 100%;
}
@media (min-width: 1024px) {
    .item-step {
        padding: 20px 24px;
        margin-bottom: 0;
        gap: 8px;
    }
}
.item-step.focus_step {
    background-color: #FDBC00;
}
.item-step .number-step {
    height: 24px;
    width: 24px;
    color: #fff;
    font-size: 14px;
    font-weight: 700;
    background-color: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    flex-shrink: 0;
}
@media (min-width: 1024px) {
    .item-step .number-step {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }
}
.event_plan-parents {
    padding: 16px;
    border-radius: 16px;
    background-color: #F5F5F5;
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
}
@media (min-width: 1024px) {
    .event_plan-parents {
        padding: 32px 60px;
        gap: 24px;
        margin-bottom: 40px;
        flex-direction: unset;
    }
}
.event_plan-parents .entrust_popup {
    font-size: 14px;
    line-height: 19px;
    font-weight: 700;
    padding: 15px 16px;
    border: 3px solid #000000;
    border-radius: 39px;
    background-color: #FDBC00;
    margin-bottom: 16px;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
}
@media (min-width: 1024px) {
    .event_plan-parents .entrust_popup {
        width: 45%;
        margin-bottom: 0;
        font-size: 20px;
        text-align: unset;
        padding: 23.5px 16px;
        line-height: 27px;
    }
}
.event_plan-parents p {
    font-size: 13px;
    margin-bottom: 0;
    line-height: 18px;
    font-weight: 700;
}
@media (min-width: 1024px) {
    .event_plan-parents p {
        font-size: 18px;
        line-height: 24px;
        width: 55%;
    }
}
.copy-events .e_ajax_link {
    font-size: 14px;
    line-height: 19px;
    padding: 18.5px 33px;
    border-radius: 39px;
    width: max-content;
    margin-bottom: 16px;
}
@media (min-width: 1024px) {
    .copy-events .e_ajax_link {
        padding: 20px 36px;
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 42px;
    }
    .form_holder-parentnew {
        width: 640px;
    }
    .copy-events .btn-copy-event-button {
        background-color: #fff !important;
        color: #000 !important;
        border: 4px solid #FDBC00;
        height: 56px;
        position: relative;
        padding: 20px 42px;
        font-size: 16px;
    }
    .copy-events .btn-copy-event-button:after {
        content: '';
        background: url(/assets/images/down-ic.svg) no-repeat;
        background-size: 100% 100%;
        display: block;
        width: 16px;
        height: 16px;
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
    }
}
.form_holder-parentnew .input_title-new {
    font-size: 14px;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .input_title-new {
        font-size: 16px;
        line-height: 21px;
    }
    .form_holder-parentnew .item-box .form-group {
        margin-bottom: 24px;
    }
}
.form_holder-parentnew .item-box span {
    font-size: 12px;
    color: #FD6600;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box span {
        font-size: 14px;
    }
}
.form_holder-parentnew .item-box select {
    font-size: 14px;
    line-height: 19px;
    padding: 18.5px 32px 18.5px 19px;
    appearance: none;
    background: url("/assets/images/down-ic.svg") no-repeat;
    background-size: 12px;
    background-position: 94%;
    background-color: #f5f5f5;
    border-radius: 16px;
    margin-top: 10px;
    width: 100%;
    border: 0;
}
.form_holder-parentnew .item-box select.arrow-ic-pc {
    background-position: 87%;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box select {
        font-size: 16px;
        line-height: 21px;
        padding: 16px 42px 16px 24px;
        background-position: 96%;
        background-size: 16px;
        min-height: 64px;
    }
    .form_holder-parentnew .item-box label {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 0;
    }
}
.form_holder-parentnew .item-box .input-comfirm {
    background-color: #f5f5f5;
    font-size: 14px;
    line-height: 19px;
    padding: 18.5px 19px;
    border-radius: 16px;
    width: 100%;
    border: 0;
    margin-top: 10px;
}
@media (min-width: 1024px) {
    @media (min-width: 1024px) {
        .form_holder-parentnew .item-box .input-comfirm {
            padding: 22.5px 40px 22.5px 24px;
        }
    }
}

.form_holder-parentnew .item-box .confirm-time-box input {
    max-width: 100px;
}
@media (min-width: 414px) {
    .form_holder-parentnew .item-box .confirm-time-box input {
        max-width: 120px;
    }
}
.form_holder-parentnew .popup_name {
    display: none;
}
.form_holder-parentnew .modal__content.input_name {
    position: absolute;
    background-color: #fff;
    top: 0;
    left: 10px;
    right: 16px;
    box-shadow: 0 3px 6px rgba(0, 0 , 0, 0.16);
    padding: 12px;
    font-size: 12px;
    border-radius: 16px;
    z-index: 9;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .modal__content.input_name {
        width: 400px;
        padding: 16px;
        left: 152px;
        top: -20px;
    }
}
.form_holder-parentnew .item-box .far_icon {
    margin-top: 10px;
}
.form_holder-parentnew .item-box .far_icon img {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 16px;
    width: 18px;
    height: 20px;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box .far_icon img {
        right: 24px;
    }
}
.form_holder-parentnew .item-box .box_select-two {
    margin-top: 10px;
}
.form_holder-parentnew .item-box .box_select-two select {
    margin-top: 0;
}
@media (min-width: 414px) {
    .form_holder-parentnew .item-box .box_select-two select {
        min-width: 120px;
    }
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box .box_select-two select {
        min-width: 200px;
    }
    .form_holder-parentnew .item-box .box_select-two-new select {
        min-width: 120px;
    }
}
.form_holder-parentnew .item-box .medium_size{
    width: 120px ;
}
.index-top .form_holder-parentnew .item-box .medium_size{
    width: 160px ;
}
.form_holder-parentnew .txt {
    margin-bottom: 41px;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .txt {
        margin-bottom: 31px;
    }
}
.form_holder-parentnew .txt p{
    font-size: 14px;
    line-height: 19px;
    margin-bottom:0;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .txt p {
        font-size: 15px;
        line-height: 20px;
    }
}
.form_holder-parentnew .width-vip-row {
    margin-bottom: 25px;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .width-vip-row {
        margin-bottom: 35px;
    }
}
.form_holder-parentnew .item-box .type-ticket-container {
    padding-bottom: 10px;
    border-bottom: 1px solid #cccc;
}
@media (min-width: 1024px) {
    .form_holder-parentnew .item-box .type-ticket-container {
        margin-bottom: 42px;
        padding-bottom: 32px;
    }
    .btn_submit-news {
        font-size: 18px;
        max-width: 200px;
    }
}
.btn_submit-news {
    text-align: center;
    background-color: #FDBC00;
    padding: 18.5px 16px;
    width: 100%;
    box-sizing: border-box;
    border-radius: 40px;
}

/*index top*/
.index-top{
    background: #ffffff;
    border-radius: 32px;
}
.list_step .sub_header{
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 8px;
}
.list_step .show_tip{
    margin-bottom: 20px;
}
.index-top .list_step{
    display: flex;
    align-items: end;
    flex-direction: column;
    background: #f5f5f5;
    padding: 20px 16px 24px 16px;
    border-radius: 32px 32px 0 0;
    margin-bottom: 16px;
}
.header-step{
    flex-wrap: wrap;
}

.tooltip_wrapper {
    position: relative;
    display: inline-block;
}

.tooltip_wrapper {
    position: relative;
    display: inline-block;
}
/*.modal__step:after {*/
/*    content: '';*/
/*    border-top: transparent;*/
/*    border-left: 8px solid transparent;*/
/*    border-right: 8px solid transparent;*/
/*    border-bottom: 12px solid #ffffff;*/
/*    display: block;*/
/*    position: absolute;*/
/*    top: -11px;*/
/*    left: 50%;*/
/*    transform: translateX(-50%);*/
/*}*/
.modal__step {
    display: none;
    position: absolute;
    width: 100%;
    border-radius: 16px;
    top: 110%;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    padding: 12px;
    border: 1px solid #ccc;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 99;
}
.modal__close__step {
    position: absolute;
    top: 12px;
    right: 12px;
    color: #585858;
    text-decoration: none !important;
}
.sub_header_item {
    border-radius: 16px;
    width: 100%;
    background-color: #FFFFFF;
}
.sub_header .sub_header_item:last-child {
    width: 115%;
}
.has_sub_step{
    padding: 15px 12px 15px 12px;
    display: flex;
    justify-content: space-between;
}
.has_sub_step_title{
    color: #000000;
    font-weight: bold;
    font-size: 13px;
}

.tooltip_wrapper:hover .modal__step {
    display: block;
}
.event-detail-container .event-detail-title{
    padding: 0 16px;
}
.check-box-tool-tip-pc{
    display: none;
}
.index-top .statistic-box{
    background-color: #F5F5F5;
    border-radius: 16px;
    padding: 16px;
    font-size: 12px;
}
#i_event_form_confirm .button-container {
    display: flex;
    justify-content: center;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}
#i_event_form_confirm .button-container button{
    padding: 18px;
    border-radius: 40px;
    font-size: 14px;
    font-weight: bold;
    min-width: 108px;
}
#i_event_form_confirm .confirm-back{
    position: relative;
    top: 2px;
}
.bg-main{
    background: #FDBC00;
}

@media (min-width: 1024px) {
    #i_event_form_create .button-container {
        width: 640px;
    }
    #i_event_form_confirm .button-container button{
        padding: 20px 0;
        font-size: 18px;
        min-width: 200px;
    }
    #i_event_form_create{
        font-size: 16px;
    }
    .index-top{
        margin-top: 65px;
    }
    .index-top .list_step{
        padding: 64px;
        margin-bottom: 64px;
    }
    .list_step .sub_header{
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
        width: max-content;
    }
    .has_sub_step_title {
        margin-right: 12px;
        font-size: 18px;
    }
    .check-box-tool-tip-pc{
        margin: 18px 4px 0 -30px;
        white-space: nowrap;
        display: block;
    }
    .has_sub_step{
        justify-content: start;
    }
    .list_step .sub_header .sub_header_item{
        width: max-content;
        min-width: 150px;
    }
}
.color-red{
    color: red;
}
.application_form-parents .step-parents {
    margin-bottom: 0;
}
.application_form-parents h3 {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 9px;
}
@media (min-width: 1024px) {
    .application_form-parents h3 {
        font-size: 24px;
        margin-bottom: 18px;
    }
}
.application_form-parents .event_container .form_box {
    background: #F5F5F5;
    border-radius: 24px;
    border: 8px solid #F5F5F5;
    min-width: unset;
    padding-bottom: 0;
}
@media (min-width: 1024px) {
    .application_form-parents .event_container .form_box {
        border: 12px solid #F5F5F5;
    }
}
.application_form-parents .form_box .event-detail-title {
    font-size: 16px;
    line-height: 21px;
    background-color: #f5f5f5;
    padding: 13.5px 4px;
}
@media (min-width: 1024px) {
    .application_form-parents .form_box .event-detail-title {
        padding: 23.5px 4px;
    }
}
.application_form-box {
    width: 100%;
}
.application_form-parents .input_box-new label {
    font-size: 14px;
    line-height: 21px;
    margin-bottom: 0;
}
@media (min-width: 1024px) {
    .application_form-parents .input_box-new label{
        font-size: 16px;
    }
}
.application_form-parents .input_box-new {
    padding: 16px 0;
    border-bottom: 1px solid #ccc;
    height: auto;
}
@media (min-width: 1024px) {
    .application_form-parents .input_box-new {
        padding: 24px 0;
    }
}
.form_box_content > .input_row.input_box-new:first-child {
    border-top: 1px solid #ccc;
}

.application_form-parents .input_box-new .checkbox_col {
    display: flex;
}
.application_form-parents .form_box .form_box_content {
    margin: 0;
    width: 100%;
    padding: 0 16px;
}
@media (min-width: 1024px) {
    .application_form-parents .form_box .form_box_content {
        padding: 0 80px;
    }

}
.application_form-parents .form_box .only_padding-sort {
    padding: 0 0 0 40px;
}
.application_form-parents .input_box-new .text_required_emmail {
    font-size: 14px;
    font-weight: 500;
    line-height: 19px;
    margin-bottom: 0;
    color: #FD6600;
}
.application_form-parents .event_container .form_area .input_box-new .application-button{
    margin: 0;
    box-sizing: border-box;
    width: 100%;
    display: block;
    border: 4px solid #FDBC00;
    background: #fff;
    font-size: 14px;
    line-height: 19px;
    color: #000;
    font-weight: 700;
    padding: 14.5px 16px;
    border-radius: 40px;
    box-shadow: unset;
}
@media (min-width: 1024px) {
    .application_form-parents .event_container .form_area .input_box-new .application-button {
        max-width: 376px;
    }
}
.application_form-parents .input_box-new .glyphicon-triangle-right:before {
    display: none;
}
.application_form-parents .input_box-new .black-button.small-button span.glyphicon {
    position: relative;
    top: 0;
    right: 0;
}
.application_form-parents .txt-bot {
    font-size: 14px;
    line-height: 19px;
    color: #FD6600;
    font-weight: 500;
    margin-bottom: 16px;
}
@media (min-width: 1024px) {
    .application_form-parents .txt-bot {
        font-size: 16px;
        line-height: 21px;
    }
}
.application_form-parents button.black-button {
    font-size: 14px;
    line-height: 19px;
    color: #000;
    height: 56px;
    max-height: 56px;
    border-radius: 40px;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
    background: #000;
    box-shadow: none;
    border: 0;
    padding: 0 16px !important;
    min-width: unset !important;
}
@media (min-width: 1024px) {
    .application_form-parents button.black-button {
        height: 64px;
        max-height: 64px;
        padding: 0 22px !important;
        min-width: 200px !important;
        font-size: 18px;
    }
}
.application_form-parents button.black-button.btn_confimation-reservation {
    width: 202px;
}
.application_form-parents button.yellow-btn {
    background-color: #FDBC00;
    color: #000;
}
.application_form-parents button.btn-black {
    background: #000;
    color: #fff;
}
.application_form-parents .button-container button.black-button .glyphicon-triangle-left:before {
    display: none;
}
.black-button.big-button span.glyphicon {
    position: relative;
    top: 0;
    right: 0;
}
.application_form-parents .event_container .form_area .application-main-button-container {
    margin: 0;
    padding-bottom: 24px;
}
@media (min-width: 1024px) {
    .application_form-parents .event_container .form_area .application-main-button-container {
        padding-bottom: 60px;
    }
}
.border-radius-bot-24 {
    border-radius: 0 0 24px 24px;
}
.application_form-parents #i_free_form .fee-btn ,
.application_form-parents #i_subscriber_card_form .fee-btn  {
    width: 100%;
    margin-left: 0;
}
.form-create-event .event_container {
    width: 100%;
}
.application_modal-new {
    width: 100% !important;
    margin: 0;
    padding: 0 10px;
}
.application_modal-new label {
    font-size: 14px;
    line-height: 19px;
}
@media (min-width: 1024px) {
    .application_modal-new label {
        font-size: 16px;
        line-height: 21px;
    }
}
.application_modal-new .event-add-field-modal .shadow_input::placeholder{
    color: #999999;
}
.application_modal-new .event-add-field-modal .shadow_input {
    width: 100%;
    margin: 0;
    background: #F5F5F5;
    box-shadow: unset;
    border: 0;
    color: #000000;
    font-size: 14px;
    line-height: 19px;
    padding: 18.5px 16px;
    border-radius: 40px;
    vertical-align: middle;
    height: 56px;
    margin-bottom: 16px;
    align-content: center;
    min-height: 56px;
}
@media (min-width: 1024px) {
    .application_modal-new .event-add-field-modal .shadow_input {
        min-height: 64px;
    }
}

.application_modal-new .modal-body {
    padding: 40px 24px ;
}
@media (min-width: 1024px) {
    .application_modal-new .modal-body {
        padding: 64px ;
    }
}
.application_modal-new .title-modal {
    font-size: 20px;
    line-height: 27px;
    color: #000;
    margin-bottom: 25px;
}
@media (min-width: 1024px) {
    .application_modal-new .title-modal {
        font-size: 28px;
        line-height: 37px;
        margin-bottom: 27px;
    }
}
.application_modal-new .glyphicon-remove:before {
    content: '';
    background: url("/assets/images/close-ic-black.svg") no-repeat;
    background-size: 100% 100%;
    width: 16px;
    height: 16px;
    display: block;
}
.application_modal-new .txt {
    font-size: 14px;
    line-height: 19px;
    margin-bottom: 8px;
}
@media (min-width: 1024px) {
    .application_modal-new .txt {
        font-size: 16px;
        line-height: 21px;
        margin-bottom: 8px;
    }
}
.application_modal-new .add-form-row {
    padding: 0;
    border-bottom: 0;
}
.application_modal-new .add_more-item {
    padding: 16px 0;
    border: 1px solid #CCCCCC;
    border-width: 1px 0 1px 0;
    margin: 16px 0 24px;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    display: grid;
    gap: 4px;
}
@media (min-width: 1024px) {
    .application_modal-new .add_more-item {
        padding: 24px 0;
    }
}
.application_modal-new .add_more-item button {
    border: 4px solid #FDBC00;
    border-radius: 40px;
    padding: 13.5px 16px;
}
.application_modal-new .black-button {
    margin: 0;
    background: #FDBC00;
    font-size: 14px;
    line-height: 19px;
    padding: 13.5px 24px;
    border-radius: 40px;
    height: 56px;
    box-sizing: border-box;
}
.application_modal-new .input_row-new {
    padding: 16px 0;
    border-top: 1px solid #ccc;
}
.application_modal-new .event-add-field-modal-new .modal-body input {
    padding: 11.5px 16px;
    min-width: 80px;
    background-color: #F5F5F5;
    border-radius: 8px;
    border: 0;
    font-weight: 500;
    margin: 0 5px;
}
.application_modal-new .event-edit-item-modal {
    width: 100%;
}
.application_modal-new .event-edit-item-modal-new-3 textarea {
    min-height: 334px;
    border-radius: 16px;
    border: 0;
    background-color: #f5f5f5;
}
.list-choice-event-type-new {
    background: #fff;
    border-radius: 24px;
    border: 8px solid #F5F5F5;
    padding: 24px 15px;
}
@media (min-width: 1024px) {
    .list-choice-event-type-new {
        border: 12px solid #F5F5F5;
        padding: 38px 15px 36px;
    }
}
.list-choice-event-type-new .box-item input {
    display: none;
}

.application_form-parents #i_free_form {
    background-color: #fff;
}
.application_form-parents .subscriber_card_form_text {
    font-size: 14px;
    font-weight: 700;
}
.text-FD6600 {
    color: #FD6600 !important;
}
.text-666 {
    color: #666 !important;
}
.border-color-drank {
    border: 3px solid #000 !important;
}
.bg-unset {
    background-color: unset !important;
}
.rounded-4 {
    border-radius: 8px !important;
}
.modal__content--new {
    top: 0;
    right: 0;
    left: 0;
}
.modal__content--new .modal__content {
    max-width: 420px;
    background: #fff;
    z-index: 1;
}
.content_banking_card:hover .modal__content--new {
    display: flex;
}
.setwidth-box img {
    width: 100%;
}
.modal_scroll-height {
    max-height: 500px;
    margin-right: -10px;
    padding-right: 10px;
}
.modal-body.modal-padding-body {
    padding: 24px;
    margin-right: -5px;
}
@media (min-width: 1024px) {
    .modal-body.modal-padding-body {
        padding: 30px 54px;
    }
}
.entrust_title{
    margin-top: 1px;
    font-size: 20px;
    margin-bottom: 27px;
    padding: 0 76px;
}
.entrust_message_header{
    margin-bottom: 18px;
}
.entrust_message_content{
    margin-bottom: 18px;
}
.entrust_message_footer{
    margin-bottom: 27px;
}
.email_success_message{
    font-size: 14px;
    margin-bottom: 10px;
}

@media (min-width: 1024px) {
    .entrust_title{
        font-size: 28px;
        margin-bottom: 44px;
        padding: 0;
        text-align: center;
    }
    .entrust_message{
        font-size: 16px;
    }
    .entrust_message_content{
        margin-bottom: 23px;
    }
    .entrust_message_footer{
        margin-bottom: 70px;
    }
    .email_success_message{
        font-size: 16px;
    }
}
.success-img-box img{
    margin-top: 0;
}
.event-fee-modal-new .modal-body {
    padding: 41px 21px;
}
@media (min-width: 1204px) {
    .event-fee-modal-new .modal-body {
        padding:  64px;
    }
}
.event-fee-modal-new .event-fee-item-title {
    font-size: 20px;
    line-height: 27px;
    padding-top: 10px;
    margin-bottom: 25px;
}
.event-fee-modal-new .event-fee-item-content .box-item {
    border: 8px solid #F5F5F5;
    border-radius: 24px;
    padding: 16px;
    margin-bottom: 24px;
}
@media (min-width: 1024px) {
    .event-fee-modal-new .event-fee-item-content .box-item {
        margin-bottom: 40px;
        padding: 28px;
    }
}
.event-fee-modal-new .event-fee-item-content p {
    font-size: 12px;
    line-height: 16px;
    margin-bottom: 28px;
}
@media (min-width: 1024px) {
    .event-fee-modal-new .event-fee-item-content p {
        font-size: 15px;
        line-height: 20px;
        margin-bottom: 20px;
    }
}
.event-fee-modal-new .event-fee-item-content .box-item .checkbox label {
    font-size: 13px;
    line-height: 17px;
}
@media (min-width: 1024px) {
    .event-fee-modal-new .event-fee-item-content .box-item .checkbox label {
        font-size: 16px;
        line-height: 21px;
    }
}
.event-fee-modal-new .event-fee-item-content .box-item-2 .field_nav {
    padding: 12px;
    background-color: #F5F5F5;
    font-size: 12px;
    line-height: 16px;
}
@media (min-width: 1024px) {
    .event-fee-modal-new .event-fee-item-content .box-item-2 .field_nav {
        font-size: 14px;
        line-height: 19px;
    }
}
.event-fee-modal-new .event-fee-item-content .box-item-2 .field_nav.nav_active {
    background-color: #FDBC00;
}
.event-fee-modal-new .event-fee-item-content .box-item-2 .row_custom_field {
    border-bottom: 0;
    padding: 0;
    margin-bottom: 25px;
}
.select_general-box select {
    appearance: none;
    border-radius: 16px;
    background-color: #F5F5F5 !important;
    padding: 0 30px 0 16px !important;
    min-height: 56px;
    max-height: 56px;
    background: url("/assets/images/down-ic.svg") no-repeat;
    background-size: 12px;
    background-position: 85% !important;
    background-color: #f5f5f5;
    border: 0;
    font-size: 14px;
}
@media (min-width: 1024px) {
    .select_general-box select {
        background-position: 88% !important;
        padding: 0 34px 0 24px !important;
        font-size: 16px;
        line-height: 21px;
        min-height: 64px;
        max-height: 64px;
    }
}
.max-w-120 {
    max-width: 120px;
}
@media (min-width: 1024px) {
    .max-w-xl-200 {
        max-width: 200px;
    }
}
.event-fee-modal-new .table-event-fee-data tr > th {
    white-space: nowrap;
    background-color: #666666;
}
@media (min-width: 1024px) {
    .event-fee-modal-new .table-event-fee-data tr > th {
        font-size: 13px;
    }
}
.event-fee-modal-new .table-event-fee-data tr > th:first-child ,
.event-fee-modal-new .table-event-fee-data tr > th:nth-child(2) {
    min-width: 44px;
}
.event-fee-modal-new .table-event-fee-data tr > th:nth-child(3) {
    min-width: 118px;
}
.event-fee-modal-new .table-event-fee-data .popup_step {
    display: none;
    position: relative;
}
.event-fee-modal-new .table-fee.table-event-fee-data.e_table tr > td {
    padding: 7px 12px;
    line-height: 1;
}
.event-fee-modal-new .table-fee tr td {
    border-right: 1px solid #666666;
    border-bottom: 1px solid #666666;
}
.event-fee-modal-new .table-fee tr th {
    border-right: 1px solid #999999;
    border-bottom: 1px solid #999999;
}
.event-fee-modal-new .table-fee tr th:first-child {
    border-left: 1px solid #999999;
}
.event-fee-modal-new .table-fee tr:nth-child(odd) ,
.event-fee-modal-new .table-fee tr:nth-child(even) {
    background-color: unset;
}
.event-fee-modal-new  .table-event-fee-data tr .temp_row span {
    font-size: 12px;
    line-height: 16px;
    font-weight: 700;
}
.event-fee-modal-new .table-event-fee-data tr > th {
    padding: 9px 12px !important;
}
.table_scroll-box {
    margin-right: -21px;
}
@media (min-width: 1024px) {
    .table_scroll-box {
        margin-right: 0;
    }
}
.event-fee-modal-new .table-event-fee-data tr > td input[type="text"] {
    font-size: 12px;
    line-height: 16px;
    font-weight: 700;
    padding: 12px;
    background-color: #F5F5F5;
    border-radius: 8px;
}
.event-fee-modal-new .table-event-fee-data tr > td input[type="text"]:disabled {
    background-color: #fff;
}
.event-fee-modal-new .table-fee tr td:first-child {
    border-left: 1px solid #666666;
}
.btn-add-item {
    border: 4px solid #FDBC00 !important;
    border-radius: 40px;
}
.lbl-box-none .lbl {
    display: none;
}
.add_form_item_checkbox_modal{
    padding-top: 40px;
}
.add_form_item_checkbox_modal .add_form_item_checkbox_modal_title{
    padding: 0 80px;
    font-size: 20px;
}
@media (min-width: 1024px) {
    .add_form_item_checkbox_modal{
        padding: 66px 0 0 0;
    }
    .add_form_item_checkbox_modal .add_form_item_checkbox_modal_title{
        padding: 0;
        font-size: 28px;
    }
}
.bottom-event-action-description{
    color: #FD6600;
    padding: 0 16px;
    text-align: center;
    margin-bottom: 19px;
}
@media (min-width: 1024px) {
    .bottom-event-action-description{
        font-size: 16px;
    }
}
.h_area-custom {
    height: 72px !important;
}
@media (min-width: 1024px) {
    .h_area-custom {
        height: 64px !important;
        display: block;
        align-content: center;
    }
}

/*start event edit*/
.edit-form .submit-edit{
    margin-bottom: 40px;
}
.edit-form .form_holder-parentnew{
    padding: 0 16px;
}
@media (min-width: 1024px) {
    .edit-form .submit-edit{
        margin-bottom: 80px;
    }
    .edit-form .form_holder-parentnew{
        padding: 0;
    }
}
/*end event edit*/
.tool_tip-parents a {
    position: relative;
    display: block;
    z-index: 9;
}
.tool_tip-parents .modal__content {
    top: 0;
    left: 0;
    padding: 16px;
    border-radius: 16px;
    right: 16px;
    z-index: 9;
}
.event_step_wrap{
    margin-bottom: 40px;
}
.time-checkin .event-edit-time-container{
    padding: 0 16px;
}
.time-checkin .event-edit-time-container .event-edit-time-title {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 18px;
}
@media (min-width: 1024px) {
    .time-checkin .event-edit-time-container .event-edit-time-title {
        font-size: 20px;
        line-height: 27px;
        margin-bottom: 27px;
    }
}
.time-checkin .event-edit-time-container .event-edit-time-title:after {
    content: '';
    background: #CCCCCC;
    display: block;
    width: 100%;
    height: 1px;
}
.time-checkin .list_work-box {
    padding: 0;
    list-style: none;
    margin-bottom: 43px;
}
@media (min-width: 1024px) {
    .time-checkin .list_work-box {
        margin-bottom: 31px;
    }
}
.time-checkin .list_work-box li p:before {
    content:'●';
    display: block;
}
.application-form-embedded .application_form-parents{
    padding-top: 0 !important;
}
#guide-gmo.popup ,
.popup_deadline {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    align-items: center;
    justify-content: center;
    background: rgba(77, 77, 77, .7);
    transition: all .4s;
    padding: 0 16px;
    display: none;
}
#guide-gmo .modal__close ,
#guide-deadline .modal__close__deadline{
    position: absolute;
    top: 10px;
    right: 10px;
}
#guide-deadline .modal__content {
    border-radius: 12px;
    position: relative;
    max-width: 100%;
    background: #fff;
    padding: 20px;
    z-index: 1;
    border: 1px solid #000;
}
.application_form-parents .form_box .application-title{
    padding: 13.5px 4px;
}
@media (min-width: 1024px) {
    .application_form-parents .form_box .application-title{
        padding: 23.5px 28px;
    }
}
.input_color-box {
    width: 72px !important;
    height: 28px;
}
label.lbl {
    display: none;
}
.container_form_time_subscriber{
    padding: 0 16px;
}
.popup_transaction_type, .popup_canceled_time, .popup_status_email, .popup_edited_time, .popup_send_ticket_time, .popup_checkin_time, .popup_scanner_number,
.popup_is_answer_survey, .popup_time_video, .popup_subscriber_image, .popup_deleted, .popup_vip_time, .popup_slot_number, .popup_is_winner, .popup_transaction_last_time {
    visibility: hidden;
    width: 300px;
    background-color: white;
    color: #000;
    text-align: center;
    position: absolute;
    z-index: 1;
    padding: 12px;
    border: 1px solid #989797;
    border-radius: 12px;
    display: flex;
    gap: 8px;
    align-items: start;
}
.popup_step p {
    margin-bottom: 0;
}
.popup_step .modal__close__application {
    position: relative;
    top: 0;
    right: 0;
    z-index: 999;

}
@media (max-width: 1023px) {
    .event-detail-container .event-detail-title-2 {
        padding: 0;
    }
}