.user-body-container {
    margin-bottom: 86px;
}
.box_general-new {
    margin: 0 auto;
    border: 8px solid #ececec;
    padding: 32px 16px;
}

@media (min-width: 1024px) {
    .box_general-new {
        width: 800px;
        border: 12px solid #ececec;
        padding: 30px 28px 36px;
    }
}

.manager-form .modal-body label {
    font-family: Meiryo;
}

.user-label {
    padding-left: 17px;
    line-height: 27px;
    padding-right: 0px;
    font-size: 13px;
}

.label-title {
    background-color: #eae8dc;
    color: #5d5d5d;
    padding-left: 15px;
}

.button-container.user-button-container {
    margin-top: 31px;
    margin-bottom: 39px;
}

.user-body-content .first-button.button-right {
    margin: 15px 45px 30px;
}

.user-body-content .button-container-right {
    margin: 0px 32px 20px;
    text-align: right;
}

.user-body-container .user-button-container {
    margin-top: 31px;
    margin-bottom: 39px;
}

/*Add Form Modal*/
.manager-modal .modal-body {
    padding: 0;
    padding-top: 10px;
}

.add-form-title {
    font-size: 20px;
    line-height: 27px;
    font-weight: bold;
    margin-bottom: 18px;
    text-align: center;
}
@media (min-width: 1024px) {
    .add-form-title {
        font-size: 28px;
        line-height: 37px;
    }
}
.add-form-content {
    padding-right: 30px;
    padding-left: 30px;
}

.add-form-content > .form-group {
    margin-bottom: 17px;
}

.row-title {
    height: 27px;
    line-height: 27px;
    margin-top: 11px;
    font-weight: 500;
    padding-left: 5px;
}

.add-user-input {
    border: 1px solid #bfbfbf;
}

.add-user-span {
    height: 32px;
    width: 256px;
    line-height: 32px;
    margin-top: 8.5px;
    font-weight: 500;
}

.decription-box {
    padding-top: 28px;
}

.decription-box > p {
    font-size: 13px;
    padding-left: 6px;
    height: 27px;
    line-height: 27px;
}

/*Modal-confirm*/
.confirm-button {
    height: 44px;
    width: 149px;
    line-height: 44px;
    margin: 15px 13.5px 83px;
}

/********** edit account modal *********/
.manager-modal .edit_account_form_holder , .user_list-new2{
    margin-left: auto;
    margin-right: auto;
    padding: 40px 24px;
}
@media (min-width: 1024px) {
    .manager-modal .edit_account_form_holder , .user_list-new2{
        padding: 66px 64px;
    }
    .w-xl-800 {
        width: 800px;
    }
    .p-xl-0 {
        padding: 0 !important;
    }
}
.edit_account_form_holder > .form-group {
    margin: 0;
    margin-bottom: 15px;
}

.edit_account_form_holder > .form-group > [class*="col-"]{
    float: none;
}

.edit_account_form_holder .input_dark_title {
    height: auto;
    line-height: normal;
    padding: 5px 15px;
    background-color: #eae8dc;
    margin: 0px;
    font-weight: normal;
}

.edit_account_form_holder .user_list_input {
    padding-left: 15px;
    padding-right: 15px;
}

.edit_account_form_holder .user_list_input_small {
    width: 105px;
}

.edit_account_form_holder .edit_account_button_holder {
    padding-top: 30px;
}

.main-user-edit-button {
    margin-top: 20px;
}

.add-user-input-container > .input-notice {
    padding-top: 14px;
}

.bank_padding {
    padding: 15px 0;
}

.separator-dashed-line {
    border-bottom: 2px dashed #c9c9c1;
    margin: 15px 0;
}

.section-title-holder {
    margin: 0px;
}

.has-back-to-top {
    padding-bottom: 64px;
}

.user-body-container {
    margin-bottom: 40px;
}

.section-title {
    padding-left: 0;
    font-weight: bold;
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 10px;
}
@media (min-width: 1024px) {
    .section-title {
        font-size: 20px;
        line-height: 27px;
        margin-bottom: 13px;
    }
}
span.right-triangle {
    color: #FEE200;
}

.list-export-container {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    color: black;
    font-weight: bold;
    font-size: 20px;
    align-items: center;
}

.list-export-container a {
    color: black;
}
.pdf_link {
    margin-right: 50px
}

@media (max-width: 768px) {
    .list-export-container {
        font-size: 16px;
        justify-content: space-between;
    }
    .pdf_link {
        margin-right: 30px
    }
    .has-back-to-top {
        padding-bottom: 40px;
    }
}

.delete-export{
    border: 0px;
    background-color: white;
}
.user_list-new .label-title {
    background: unset;
    margin-bottom: 9px;
    padding: 0;
    font-weight: 700;
    color: #000;
}
.user_list-new .input_box-new {
    padding: 16px;
    min-height: 56px;
    align-content: center;
    border-radius: 16px;
    border: 1px solid #E0E0E0;
}
@media (min-width: 1024px) {
    .user_list-new .input_box-new {
        min-height: 64px;
    }
}
.user_list-new .border_only-user {
    border-top: 8px solid #F5F5F5;
    margin-left: -16px;
    margin-right: -16px;
    margin-bottom: 19px;
    padding-left: 16px;
    padding-right: 16px;
    padding-top: 26px;
}
@media (min-width: 1024px) {
    .user_list-new .border_only-user {
        border-top: 12px solid #F5F5F5;
        margin-left: -28px;
        margin-right: -28px;
        margin-bottom: 27px;
        padding-left: 28px;
        padding-right: 28px;
        padding-top: 33px;
    }
}
.user_list-new .title-box {
    font-size: 18px;
    line-height: 24px;
    margin-bottom: 9px;
}
.user_list-new .form-group {
    margin-bottom: 17px;
}
@media (min-width: 1024px) {
    .user_list-new .title-box {
        font-size: 20px;
        line-height: 27px;
        margin-bottom: 18px;
    }
    .user_list-new .form-group {
        margin-bottom: 26px;
    }
    .m-width-176 {
        min-width: 176px;
    }
    .max-witdh-200 {
        max-width: 200px;
    }
    .max-witdh-320 {
        max-width: 320px;
    }
}
.bg-F5F5F5 {
    background-color: #F5F5F5;
}
.text-666 {
    color: #666;
}
.user_list-new .txt-content {
    font-size: 11px;
    line-height: 15px;
    color: #666666;
}
@media (min-width: 1024px) {
    .user_list-new .txt-content {
        font-size: 14px;
        line-height: 19px;
    }
}
.user_list-new .only_w-modalinput {
    width: 200px;
}

@media (min-width: 1024px) {
    .user_list-new .only_w-modalinput {
        width: 320px;
    }
    .user_list-new .only_wxl-modalinput {
        width: 200px !important;
    }
    .user_list-new .select_custom-onlygary {
        width: 200px;
        max-width: 200px;
    }
}

