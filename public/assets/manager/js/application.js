var select_button_html = ""; // store html code of select button in friend ticket
var n_ft_form = 1; // number of friend ticket forms
var fee_data; // hold the quantity of each fee item data
var current_zipcode_form = null;
var fee_items = new Array();
var fee_item_info = new Array();
var fee_item_target = null;
var fee_data_id = null;
var list_fee_items = new Array();
var item_number = 1;
var display_explain = 0;
function my_lang(value){
    return translate[value] ? translate[value] : value
}

/*$("#choose_1").prop('checked','checked')
$("#require1").prop('checked','checked')
$("#show_on_friend_form1").prop('checked','checked')
$("#choose_13").prop('checked','checked')
$("#require20").prop('checked','checked')
$("#show_on_friend_form20").prop('checked','checked')
$("#show_on_friend_form20").prop('checked','checked')*/

$("#required1").remove('disable')
// $("#choose_1").remove('disable')
// $("#show_on_friend_form1").remove('disable')
$(document).ready(function () {
    var text_error = translate['決済金額が200円以下ですので、他の決済方法を選択してください。'] ? translate['決済金額が200円以下ですので、他の決済方法を選択してください。'] : '決済金額が200円以下ですので、他の決済方法を選択してください。';
	window.mobileAndTabletcheck = function() {
		  var check = false;
		  (function(a){if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);
		  return check;
	};

    $(document).on("click", "#application-form .application-confirm .e_back", back_to_application_form);

    function back_to_application_form() {
        var form = $("#application-form");
        form.find(".application-confirm").remove();
        display_application_form(form);
    }

    $(document).on("click", ".e_transaction", function () {
        var obj = $(this);
        var url = obj.attr("data-url");
        var data = {
            "transaction_type": $("input[name = \"transaction_type\"]").val()
        };
        call_ajax_link(url, data, obj);
    });
    $(document).on("click", ".e_custom_field_application input[name = 'field_name[]']", function () {
        var obj = $(this);
        var url = $(".e_custom_field_application").attr("data-url");
        var data = {
            "limit": $(".e_changer_number_record").val(),
            "field_choice": []
        };
        $(".e_custom_field_application").find("input").each(function () {
            var obj_checked = $(this).prop("checked");
            if (obj_checked) {
                data.field_choice.push($(this).val());
            }
        });
        if (data.field_choice.length === 0) {
            $(".e_custom_field_application input#field_no_ticket").prop("checked", true);
            data.field_choice.push("no_ticket");
        }
        call_ajax_link(url, data, obj);
    });

    $(document).on("click", ".change_event_fee", function(){
        var name_class = $(this).attr('id')
        console.log(name_class)
        if ($(this).is(':checked')) {
            $("." + name_class).addClass('hidden');
            $(this).attr('checked');
        }else {
            $("." + name_class).removeClass('hidden');
            $(this).removeAttr('checked');
        }
    });

    /*$(document).on("change", ".e_change_fee", function () {
        change_fee($(this));
    });*/

    //Fix event hide when close one modal in page muti modal
    (function (Modal) {
        var show = Modal.prototype.show;

        Modal.prototype.show = function () {
            setTimeout(()=> {
                this.modalOpen = !this.$body.hasClass("modal-open");
                show.apply(this, arguments);
            },200)
        };

        Modal.prototype.hideModal = function () {
            var that = this;
            this.$element.hide();
            this.backdrop(function () {
                if (that.modalOpen) {
                    that.$body.removeClass("modal-open");
                }
                that.resetAdjustments();
                that.resetScrollbar();
                that.$element.trigger("hidden.bs.modal");
            });
        };
    })($.fn.modal.Constructor);

    $(document).on("click", "#i_change_view_application_form", function () {
        $("#application_form_edit").toggle();
        $("#application_form_detail").toggle();
    });

    $(document).on("click", "#i_back_application_form", function () {
        $("#i_btn_application_form").show();
        $("#i_application_transaction").html("");
        $("#i_application_container").toggle();
        $("#i_application_form").show();
        $("#i_application_confirm").html("");
        $("#i_application_transaction").toggle();
        var form = $('#application-form.e_ajax_submit');
        var url = form.attr('url_temp_default');
        form.attr('action', url);
        $("label.help-block").hide();
    });

    $(document).on("change", "#i_read_rules", function () {
        if ($(this).is(":checked")) {
            $(this).parents(".e_modal_content").modal("hide");
            $("#i-rules").attr("checked", "true");
        }
    });

    $(document).on("click", ".e_btn_zip_code", function () {
        custom_ajaxzip3();
        AjaxZip3.zip2addr("zip_code", "", "shipping_address1", "shipping_address2", "shipping_address3", "");
    });

    $(document).on("click", ".street_zip_code", function () {
        resolve_zipcode($(this));
    });
    $(document).on("click", "#e_reset_free_form", function (e) {
        var selector = creat_input_selector("");
        $(this).parents("form.e_ajax_submit").find(selector).each(function () {
            if (($(this).attr("type") == "radio") || $(this).attr("name") && $(this).attr("name") == "max_choice") {
                $(this).prop("checked", false);
            } else if (!$(this).attr("type") || $(this).attr("type") != "hidden") {
                $(this).val("");
            }
        });
        $(".modal").animate({scrollTop: 0}, 500);
    });
    $(document).on("change", ".e_street_zipcode", function () {
        resolve_zipcode($(this));
    });

    function resolve_zipcode(source) {
        current_zipcode_form = source.closest(".ship_address_form");
        AjaxZip3.onSuccess = function (city_id, street_address1, street_address2, street_address3) {
            if (city_id) {
                current_zipcode_form.find(".sa_address1").val(street_address1);
                current_zipcode_form.find(".sa_address2").val(street_address2);
                current_zipcode_form.find(".sa_address3").val(street_address3);
            }
        };
        var zipcode_name = current_zipcode_form.find(".sa_zipcode").attr("name");
        var address1_name = current_zipcode_form.find(".sa_address1").attr("name");
        var address2_name = current_zipcode_form.find(".sa_address2").attr("name");
        var address3_name = current_zipcode_form.find(".sa_address3").attr("name");
        AjaxZip3.zip2addr(zipcode_name, "", address1_name, address2_name, address3_name, "");
    }

    $(document).on("change", ".e_zip_code", function () {
        custom_ajaxzip3();
        AjaxZip3.zip2addr("zip_code", "", "shipping_address1", "shipping_address2", "shipping_address3", "");
    });

    var checksum_code = getUrlParameter("checksum_code");
    var application_id = getUrlParameter("id");
    if (application_id && checksum_code) {
        var url = $("#application_btn").attr("application-url") + "/" + application_id + "/" + checksum_code;
        call_ajax_link(url, {}, $("#application_btn"));
    }

    $(document).on("change", "#i_form_event .e_input_row .e_change_field_required[data-field-name^=friend_ticket]", onChangeFriendTicketCheckbox);

    function onChangeFriendTicketCheckbox() {
        this.checked && $(this)
            .closest(".e_input_row")
            .siblings()
            .find(".e_change_field_required[data-field-name^=friend_ticket]")
            .each(function () {
                this.checked = false;
            });
    }

    //AnhLD - pharse 8 -- on click toogle email setting btn event_detail/overview
    $(document).on("click", ".toggle_email_template_config", function(e){
        $(this).closest(".form_box").find(".overview_email_template").toggle();
        var span = $(this).find("span.glyphicon");
        if(span.hasClass("glyphicon-minus")){
            span.removeClass("glyphicon-minus");
            span.addClass("glyphicon-plus");
        }else{
            span.removeClass("glyphicon-plus");
            span.addClass("glyphicon-minus");
        }
    });


    // AnhLD - pharse 8 -- on change input in application list => instant update
    $(document).on("change", ".input_instant_update_application", function(e){
        var form = $(this).closest("form");
        form.submit();
    });


    // AnhLD - pharse 8 -- on click import excel winner status
    $(document).on("click", "#import_winner_status_btn", function(e){
        $("#import_winner_status_file_input").click();
    });

    $(document).on("change", "#import_winner_status_file_input", function(e){
        $(this).closest("form").submit();
    });

    // AnhLD - pharse 7
    $(document).on("change", ".select_type_ticket", function(e){
        var select_quantity_name = $(this).val();
        $(this).closest(".ft-select-item").find(".item-data-select").val(0);
        $(this).closest(".ft-select-item").find("select[name='" + select_quantity_name + "']").val(1);
        update_delegate_ticket_quantity();
    });

    // AnhLD - upgrade
    $(document).on("click", "input[name=max_choice]", function(e){
        if($(this).is(":checked")){
            $(this).closest(".list_choice_checkbox").find("input[name=max_choice]").prop("checked", false);
            $(this).prop("checked", true);
        }
    });

    // get category
    $("label[id*='item-name-']").each(function () {
        let fee_item_id = new Array();
        // slice "item-name-"
        fee_item_id['fee_item_id'] = $(this).attr('id').slice(10);
        fee_item_id['fee_item_name'] =  $(this).html().trim();
        fee_item_id['fee_item_type'] = $(this).hasClass("fee-item-type-0") ? '0' : '1';
		fee_item_id['count_number'] = item_number;
		item_number++;
        fee_item_info.push(fee_item_id);
    });

    let $isToggle = localStorage.getItem('toggleFields') ?? true;
    if ($isToggle != null && $isToggle == 'true') {
        $(".e_custom_field_application").removeClass("hidden");
        $(".fields-toggle span").removeClass("glyphicon-plus").addClass("glyphicon-minus");
    } else if ($isToggle != null && $isToggle == 'false') {
        $(".e_custom_field_application").addClass("hidden");
        $(".fields-toggle span").removeClass("glyphicon-minus").addClass("glyphicon-plus");
    }
});

function getUrlParameter(sParam) {
    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
        sURLVariables = sPageURL.split("&"),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split("=");

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
};

function change_fee(obj) {
    var url = obj.parents(".e_row").attr("data-url");
    var quantity = [];
    var data_id = [];
    var item_id = [];
    $("#i-event-fee-form").find(".e_quantity").each(function () {
        quantity.push($(this).val());
        item_id.push($(this).attr("item-id"));
        data_id.push($(this).attr("data-id"));
    });
    var data = {
        "item_id": item_id,
        "data_id": data_id,
        "quantity": quantity,
        "city_id": $("#i_city_id").val()
    };
    call_ajax_link(url, data, obj);
}

function custom_ajaxzip3() {
    $("#i_ship_info").find("label.help-block").each(function () {
        $(this).hide();
    });
    $("#i_ship_address_info").find("label.help-block").each(function () {
        $(this).hide();
    });
    AjaxZip3.onSuccess = function (city_id, shipping_address1, shipping_address2, shipping_address3) {
        if (city_id) {
            $("input[name=\"city_id\"]").val(city_id);
            $("input[name=\"shipping_address1\"]").val(shipping_address1);
            $("input[name=\"shipping_address2\"]").val(shipping_address2);
            $("input[name=\"shipping_address3\"]").val(shipping_address3);
            change_fee($(".e_change_fee"));
        }
    };

}

function setting_total_fee(data) {
    var text_error = translate['決済金額が200円以下ですので、他の決済方法を選択してください。'] ? translate['決済金額が200円以下ですので、他の決済方法を選択してください。'] : '決済金額が200円以下ですので、他の決済方法を選択してください。';
    $(".total_sub_fee").text(format_number(data.total_sub_fee));
    $(".total_tax").text(format_number(data.total_tax));
    $(".total_ship").text(format_number(data.total_ship));
    $(".total_bill").text(format_number(data.total_bill));
    $(".total_ticket").text(data.total_ticket);
    var transaction_type = $("input[name = \"transaction_type\"]:checked").val();
    var total_payment_amount = $('.total_payment_amount');
    $(".text-error-combini").remove();

    // min total bill combini accept
    const min_total_bill_combini_accept = 200;
    if(!total_payment_amount[0]) {
        document.getElementById('total_bill').value = data.total_bill;
        if(parseFloat(data.total_bill) < min_total_bill_combini_accept && transaction_type == 'COMBINI') {
            $(".error-money-combini").append('<span class="bg-yellow text-error-combini">' + text_error + '</span>');
        }
    }
    data.fee_ticket_issuance = Math.floor(data.total_ticket * 100);
    data.consumption_tax = Math.floor( data.fee_ticket_issuance * 0.1);
    data.total_system_fee = Math.floor(data.fee_ticket_issuance + data.consumption_tax);
    data.total_payment_amount = data.total_system_fee + data.total_bill;

    if(total_payment_amount[0]) {
        document.getElementById('total_bill').value = data.total_payment_amount;
        if(parseFloat(data.total_payment_amount) < min_total_bill_combini_accept && transaction_type == 'COMBINI') {
            $(".error-money-combini").append('<span class="bg-yellow text-error-combini">' + text_error + '</span>');
        }
    }

    $(".fee_ticket_issuance").text(format_number(data.fee_ticket_issuance));
    $(".consumption_tax").text(format_number(data.consumption_tax));
    $(".total_system_fee").text(format_number(data.total_system_fee));
    $(".total_payment_amount").text(format_number(data.total_payment_amount));

    //Set cost shipping
    var list_fee_item = data.list_fee_item;
    $(".e_cost_shipping_item").each(function () {
        var item_id = $(this).attr("data-id");
        if (list_fee_item.hasOwnProperty(item_id)) {
            $(this).val(list_fee_item[item_id].cost_shipping);
        }
    });
    $('.payment_method .help-block').remove();
    $('.payment_method').removeClass('has-error');
}

$(document).on('change', '.transaction_type', function(e){
    var text_error = translate['決済金額が200円以下ですので、他の決済方法を選択してください。'] ? translate['決済金額が200円以下ですので、他の決済方法を選択してください。'] : '決済金額が200円以下ですので、他の決済方法を選択してください。';

    var transaction_type = e.target.id;
    var total_sub_fee = document.getElementsByClassName('total_sub_fee')[0].innerText;
    var total_tax = document.getElementsByClassName('total_tax')[0].innerText;
    var total_ship = document.getElementsByClassName('total_ship')[0].innerText;
    var total_bill = document.getElementsByClassName('total_bill')[0].innerText;
    // min total bill combini accept
    const min_total_bill_combini_accept = 200;
    var total_payment_amount = $('.total_payment_amount');
    if(total_payment_amount[0]) {
        var total_ticket = document.getElementsByClassName('total_ticket')[0].innerText;

        if(total_sub_fee.length > 3){
            total_sub_fee = total_sub_fee.replaceAll(',', '');
        }
        if(total_tax.length > 3){
            total_tax = total_tax.replaceAll(',', '');
        }
        if(total_bill.length > 3){
            total_bill = total_bill.replaceAll(',', '');
        }
        if(total_ticket.length > 3){
            total_ticket = total_ticket.replaceAll(',', '');
        }

        var fee_ticket_issuance = Math.floor(total_ticket * 100);
        var consumption_tax = Math.floor(( fee_ticket_issuance) * 0.1);
        var total_system_fee = Math.floor(fee_ticket_issuance + consumption_tax);
        var total_payment_amount = Math.floor(total_system_fee + Number(total_bill));

        $(".text-error-combini").remove();
        document.getElementById('total_bill').value = total_payment_amount;
        if(Math.floor(total_payment_amount) < min_total_bill_combini_accept && transaction_type == 'i_COMBINI') {
            $(".error-money-combini").append('<span class="bg-yellow text-error-combini">'+ text_error+'</span>');
        }

        $(".fee_ticket_issuance").text(format_number(fee_ticket_issuance));
        $(".consumption_tax").text(format_number(consumption_tax));
        $(".total_system_fee").text(format_number(total_system_fee));
        $(".total_payment_amount").text(format_number(total_payment_amount));
        $('.payment_method .help-block').remove();
        $('.payment_method').removeClass('has-error');
    } else {
        $(".text-error-combini").remove();
        total_bill = total_bill.replaceAll(',', '');
        document.getElementById('total_bill').value = total_bill;
        if(Math.floor(total_bill) < min_total_bill_combini_accept && transaction_type == 'i_COMBINI') {
            $(".error-money-combini").append('<span class="bg-yellow text-error-combini">'+ text_error+'</span>')
        }
    }
})

function format_number(value) {
    value = parseInt(value);
    return value.format(0, 3, ",", ".");
}

function change_transaction_value(data, form, button) {
    if (data.state == 1) { /* success */
        if (data.hasOwnProperty("transaction_type_text")) {
            $(".e_transaction_type_text").text(data.transaction_type_text + my_lang('決済'));
        }
        if (data.hasOwnProperty("transaction_type")) {
            $("input[name = \"transaction_type\"]").val(data.transaction_type);
        }
    } else {
    }
}

/**
 * Handle data response from edit application when submit
 * @param {Object} data data response from server
 * @param {jQuery} form From submit
 * @param {jQuery} button Button click
 */
function save_form_edit_application_response(data, form, button) {
    var jgrow = "alert-danger";
    if (data.state == 1) { /* success */
        jgrow = "alert-success";
        if (data.html) {
            $(".e_modal_content").modal("hide");
            var $modal = $("<div class='modal fade e_modal_content'>");
            $modal.html(data.html);
            $modal.modal({
                backdrop: "static"
            });
        }
        update_table_content_application(data);
    } else if (data.state == 0) { /* Invalid data */

    } else if (data.state == 2) { /* Server error */

    } else {

    }
    if (data.error) {
        show_error(data.error, form);
    }

    if (data.hasOwnProperty("redirect") && data.redirect) {
        setTimeout(function () {
            window.location = data.redirect;
        }, 3000);

    }

    if (data.hasOwnProperty("reload") && data.reload) {
        setTimeout(function () {
            window.location.reload();
        }, 3000);
    }

    $.jGrowl("<i class='icon16 i-checkmark-3'></i> " + data.msg, {
        group: jgrow,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        }
    });
}

function save_form_note_response(data, form, button) {
    var jgrow = "alert-danger";
    if (data.state == 1) { /* success */
        jgrow = "alert-success";
        form.parents(".e_modal_content").modal("hide");
    }

    if (data.error) {
        show_error(data.error, form);
    }

    $.jGrowl("<i class='icon16 i-checkmark-3'></i> " + data.msg, {
        group: jgrow,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        }
    });
}

function update_table_content_application(data) {
    if (data.record != undefined) {
        //Update total application
        if (data.total_subscriber != undefined) {
            $("#i_total").html(data.total_subscriber);
        }
        //Update total application canceled
        if (data.total_subscriber_canceled != undefined) {
            $("#i_total_canceled").html(data.total_subscriber_canceled);
        }
        //Update total application male
        if (data.total_subscriber_male != undefined) {
            $("#i_total_male").html(data.total_subscriber_male);
        }
        //Update total application female
        if (data.total_subscriber_female != undefined) {
            $("#i_total_female").html(data.total_subscriber_female);
        }
        //Update total ticker release
        if (data.total_ticket_release != undefined) {
            $("#i_total_ticket_release").html(data.total_ticket_release);
        }
        //Update total checkin
        if (data.total_checkin != undefined) {
            $("#i_total_checkin").html(data.total_checkin);
        }
        //Update total payment count
        if (data.payment_count != undefined) {
            $("#i_total_payment").html(data.payment_count);
        }

        var for_key = data.key_name;
        $("th[field_name]").each(function () {
            var attr = $(this).attr("field_name").split(".");
            if (attr[attr.length - 1] == data.key_name) {
                for_key = $(this).attr("field_name");
            }
        });
        var tempObj = $("tr[data-id='" + data.record[data.key_name] + "']");
        if (tempObj && tempObj.length) {
            tempObj.find("td").effect("highlight", {}, 5000);
            for (var key in data.record) {
                tempObj.children("td[for_key]").each(function () {
                    var t_key = $(this).attr("for_key");
                    // if the key is transaction_last_time and
                    // returned data of transaction_last_time is null
                    // don't set html code for that cell
                    if (key == "transaction_last_time" && data.record[key] == null) {
                        // don't set html code,
                        // move to the next key
                        return;
                    }
                    if (t_key == key) {
                        $(this).html(data.record[key]);
                    } else {
                        if (t_key.split(".").length > 1) {
                            if (t_key.split(".")[1] == key) {
                                $(this).html(data.record[key]);
                            }
                        }
                    }
                });
            }
        }
    }
}


function update_table_content_application_custom(data) {
    if (data.record != undefined) {
        var for_key = data.key_name;
        $("th[field_name]").each(function () {
            var attr = $(this).attr("field_name").split(".");
            if (attr[attr.length - 1] == data.key_name) {
                for_key = $(this).attr("field_name");
            }
        });
        for (let index = 0; index < data.record.length; index++) {
                var tempObj = $("tr[data-id='" + data.record[index][data.key_name] + "']");
                if (tempObj && tempObj.length) {
                tempObj.find("td").effect("highlight", {}, 5000);
                for (var key in data.record[index]) {
                    tempObj.children("td[for_key]").each(function () {
                        var t_key = $(this).attr("for_key");
                        // if the key is transaction_last_time and
                        // returned data of transaction_last_time is null
                        // don't set html code for that cell
                        if (key == "transaction_last_time" && data.record[index][key] == null) {
                            // don't set html code,
                            // move to the next key
                            return;
                        }
                        if (t_key == key) {
                            $(this).html(data.record[index][key]);
                        } else {
                            if (t_key.split(".").length > 1) {
                                if (t_key.split(".")[1] == key) {
                                    $(this).html(data.record[index][key]);
                                }
                            }
                        }
                    });
                }
            }
        }
    }
}

function application_response(data, form, button) {
    data.msg = translate[data.msg] ? translate[data.msg] : data.msg;
    if(data.error && data.error.transaction_type === 'uncheck_radio_transaction_type'){
        data.error.transaction_type = my_lang('uncheck_radio_transaction_type')
        uncheck_radio_transaction_type()
    }
    if (button) {
        button.removeAttr("disabled");
    }
    clear_error(form);
    if (!form) {
        form = $(".e_ajax_submit");
    }
    var jgrow = "alert-danger";
    /* success */
    if (data.state == 1) {
        form.parents(".e_modal_content").modal("hide");
        jgrow = "alert-success";
        if (data.html) {
            var $modal = $("<div id='myModal' class='modal fade e_modal_content'>");
            $modal.html(data.html);
            $modal.modal({
                backdrop: "static"
            });
        }
    }
    if (data.state == 2) {
        form.parents(".e_modal_content").modal("hide");
        jgrow = "alert-danger";
        if (data.html) {
            var $modal = $("<div id='myModalError' class='modal fade e_modal_content'>");
            $modal.html(data.html);
            $modal.modal({
                backdrop: "static"
            });
        }
    }

    if (data.state == 'redirect') {
        window.location.replace(data.redirect[1]);
    }

    if (data.state == 'redirect_thank') {
        window.location.replace(data.redirect);
    }

    if(data.error_payment) {
        $('.error_payment label').remove();
        $('.error_payment').append('<label>' + data.error_payment + '</label>');
    }

    if (data.hasOwnProperty("pay_email_delivery") && data.pay_email_delivery) {
        window.opener.location.reload();
    }

    //Use on_application_form_error(data, form) instead of the code below (line 373 -> 421)

    if (data.hasOwnProperty("redirect") && data.redirect) {
        setTimeout(function () {
            window.location = data.redirect;
        }, 3000);

    }

    if (data.hasOwnProperty("reload") && data.reload && (data.html == undefined || !data.html)) {
        setTimeout(function () {
            window.location.reload();
        }, 3000);
    }
    form.find(".i_item_fee .fee-item-error").hide();
    form.find("#total_ticket_error").hide();
    form.find(".error_total_quantity").hide();
    form.find("#total_quantity_show_error .fee-item-error").hide();
    /* error */
    if (!data.state) {
        if (data.hasOwnProperty("error")) {
            show_error_application(data.error, form);
        }
        if (data.hasOwnProperty("error_fee")) {
            show_error_fee(data.error_fee, form);
        }
        if (data.hasOwnProperty("error_street")) {
            show_error_street(data.error_street, form);
        }

        if (data.hasOwnProperty("friend_validate")) {
            $.each(data.friend_validate, function () {
                var friend_id = this["friend_id"];
                var name = "friends[" + friend_id + "]";
                var obj = $("input[name^=\"" + name + "\"]:first");
                var friend_form = obj.parents(".friend_ticket_row");
                show_error_friend(this, friend_form);
            });
        }
    }
    /* jGrowl */
    $.jGrowl(data.msg, {
        group: jgrow,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        }
    });

    update_table_content_application(data);

}

function show_error_friend(data, form) {
    var errorList = new Array();
    var errors = data.error;
    for (var property in errors) {
        if (errors.hasOwnProperty(property)) {
            var friend_name = get_friend_input_name(property, data.friend_id);
            errorList[friend_name] = errors[property];
        }
    }
    var error_fee = data.error_fee;
    for (var property_fee in error_fee) {
        if (error_fee.hasOwnProperty("total_quantity")) {
            var string_error = "<span class=\"text-red error_total_quantity\">" + error_fee.total_quantity + "</span>";
            form.find(".ft-select-holder").append(string_error);
        }
    }

    show_error_on_friend_form(errorList, form);
}

function get_friend_input_name(delegate_name, friend_id) {
    if(!delegate_name) return "";
    if ($("#form-to-copy").length != 0) {
        // if the form-to-copy is present
        // return the name in the form of "friends[id][delegate_name]"
        return "friends[" + friend_id + "][" + delegate_name + "]";
    } else {
        return get_normal_friend_name(delegate_name, friend_id);
    }

}

function get_normal_friend_name(delegator_name, friend_id) {
    var new_name = "friends[" + friend_id + "][";
    var open_index = delegator_name.indexOf("[");
    if (open_index != -1) {
        var close_index = delegator_name.indexOf("]");
        if (close_index == -1) return;
        if (close_index == open_index + 1) {
            // input name is "name[]"
            // convert to friends[i][name][]
            new_name += delegator_name.substring(0, open_index) + "][]";
        } else {
            // input name has the form "name[address1]"
            // convert to friends[i][name][address1]
            new_name += delegator_name.substring(0, open_index) + "]" +
                delegator_name.substring(open_index, close_index) + "]";
        }
    } else {
        new_name += delegator_name + "]";
    }
    return new_name;
}

function show_error_fee(errorList, form) {
    if (errorList.hasOwnProperty("max_choice")) {
        for (var i in errorList.max_choice) {
            var string_append = " <span class=\"no-gap-col fee-item-error d-block\">" + errorList.max_choice[i] + "</span>";
            var selector = ".item_fee_" + i;
            form.find(selector).append(string_append);
        }
    }
    if (errorList.hasOwnProperty("quantity")) {
        for (var i in errorList.quantity) {
            var item = errorList.quantity[i];
            for (var j in item) {
                var string_append_quantity = " <span class=\"no-gap-col fee-item-error d-block\">" + item[j]["quantity"] + "</span>";
                var selector = "#quantity_" + j;
                form.find(selector).parents("td").append(string_append_quantity);
            }
        }
    }
    if (errorList.hasOwnProperty("total_quantity")) {
        var str_total_quantity = " <span class=\"no-gap-col fee-item-error d-block\" id=\"total_ticket_error\">" + errorList.total_quantity + "</span>";
        var selector_total_quantity = $(".table-fee-item:last").parents(".e_row");
        selector_total_quantity.after(str_total_quantity);
    }
    if (errorList.hasOwnProperty("choice_ticket")) {
        var str_choice_ticket = " <span class=\"no-gap-col fee-item-error d-block\" id=\"total_ticket_error\">" + errorList.choice_ticket + "</span>";
        var selector_choice_ticket = $(".table-fee-item:last").parents(".e_row");
        selector_choice_ticket.after(str_choice_ticket);
    }
}

function show_error_street(errors, form) {
    var errorList = [];
    for (var property in errors) {
        if (errors.hasOwnProperty(property)) {
            errorList["street_address[" + property + "]"] = errors[property];
        }
    }

    show_error_application(errorList, form);
}

function change_field_response(data, obj) {
    if (data.state != undefined && data.state == 0) {
        $.jGrowl("<i class='icon16 i-checkmark-3'></i> " + data.msg, {
            group: "alert-danger",
            position: "top-right",
            sticky: false,
            closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
            animateOpen: {
                width: "show",
                height: "show"
            }
        });
    } else {
        $(".subscribers-top-paging").html(data.paging.paging);
        $(".subscribers_post").val(data.paging.post);
        $(".subscribers_limit").val(data.paging.limit);
        $(".subscribers-top-paging").find(".e_data_paginate").addClass("subscribers");
        $(".event-detail-content").find(".e_data_table").html(data.html);
        //obj.find("select").select2();
    }
    adjust_table_header(data);
}

function application_transaction_response(data, obj) {
    var jgrow = "alert-danger";
    if (data.state) {
        var form = $('#application-form.e_ajax_submit');
        var url = form.attr('url_default');
        form.attr('action', url + '?action=save_transaction');
        $("#i_btn_application_form").hide();
        $("#i_application_transaction").html(data.html);
        $("#i_application_container").toggle();
        $("#i_application_transaction").toggle();
    } else {
        /* jGrowl */
        $.jGrowl(data.msg, {
            group: jgrow,
            position: "top-right",
            sticky: false,
            closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
            animateOpen: {
                width: "show",
                height: "show"
            }
        });
    }

    if ($('select').length) {
        $('.select-box select').each(function() {
            $(this).select2({
                minimumResultsForSearch: -1,
                dropdownParent: $(this).closest('.select-box'),
                width: 'resolve'
            });
        });
    }
}

function application_default_ajax_link(data, obj) {
    var jgrow = "alert-danger";
    if (data.state == 1) {
        jgrow = "alert-success";
        if (data.html) {
            var $modal = $("<div class='modal fade e_modal_content'>");
            $modal.html(data.html);
            $modal.modal({
                backdrop: "static"
            });
        }
    }
    $.jGrowl(data.msg, {
        group: jgrow,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        },
        afterOpen: function () {
            if (data.redirect) {
                window.location = data.redirect;
            }
            if (data.hasOwnProperty("reload") && data.reload) {
                setTimeout(function () {
                    window.location.reload();
                }, 3000);
            }
        }
    });
}

function form_setting_application_submit_response(data, form, button) {
    $('.ERROR_CASHIER').html('') //remove error cashier
    button.removeAttr("disabled");
    var jgrow = "alert-danger";
    if (data.state == 1) {
        jgrow = "alert-success";
    } else if (data.state == 0) {
    }
    if (data.error) {
        show_error(data.error, form);
    }
    if (data.error_transaction_type) {
        show_error_transaction_type(data.error_transaction_type, form);
    } else {
        show_error_transaction_type({}, form);
    }

    $.jGrowl(data.msg, {
        group: jgrow,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        },
        afterOpen: function () {
            if (data.redirect && data.state == 1) {
                window.location = data.redirect;
            }
        }
    });

    $(".modal").animate({scrollTop: 0}, 500);
}

function form_setting_check_email_response(data, form, button) {
    button.removeAttr("disabled");
    var time_jgrow_life = 3000;
    var jgrow = "alert-danger";
    if (data.state == 1) {
        jgrow = "alert-success";
    } else if (data.state == 0) {
        time_jgrow_life = 86400000;
    }
    if (data.error) {
        show_error(data.error, form);
    }

    if (data.error_transaction_type) {
        show_error_transaction_type(data.error_transaction_type, form);
    } else {
        show_error_transaction_type({}, form);
    }

    $.jGrowl(data.msg, {
        group: jgrow,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        },
        life: time_jgrow_life,
        afterOpen: function () {
            if (data.redirect && data.state == 1) {
                window.location = data.redirect;
            }
        }
    });

    $(".modal").animate({scrollTop: 0}, 500);
}

/**
 *
 * @param {object} errorList list of error, object {key:value}<br/>
 *                       key is name of input, value is error message
 * @returns {undefined}
 */
function show_error_transaction_type(errorList, form) {
    var selector = ".transaction-type-input button";
    form.find(selector).each(function () {
        var name = $(this).attr("name");
        if (name) {
            name = name.replace("[]", "");
            if (errorList.hasOwnProperty(name)) {
                var parent = $(this).closest(".transaction-type-input");
                change_error_transaction_type($(this), false);
                parent.find("label.transaction-type-error").html(errorList[name]);
            } else {
                change_error_transaction_type($(this), true);
            }
        }
    });
}

function change_error_transaction_type(obj, is_valid) {
    var parent = obj.closest(".transaction-type-input");
    if (is_valid) {
        parent.removeClass("transaction-type-error");
        parent.addClass("transaction-type-success");
        parent.find("label.transaction-type-error").hide();
    } else {
        parent.removeClass("transaction-type-success");
        parent.addClass("transaction-type-error");
        var error = parent.find("label.transaction-type-error"); //length;//.children(".error");
        if (error.length) {
            error.show();
        } else {
            parent.append("<label class='transaction-type-error col-sm-8 col-xs-12 col-sm-offset-3 m-0 mt-3'></label>").show();
        }
    }
}

/// js for friend ticket
$(document).on("click", ".friend-ticket-btn", function (e) {
    toggle_friend_form();
});

/// js for friend ticket
$(document).on("click", ".friend-ticket-detail-btn", function (e) {
    toggle_friend_detail_form();
});

function toggle_friend_form() {
    var target = $(".friend-ticket-btn");
    var glyphicon_span = target.children("span");
    if (glyphicon_span.hasClass("glyphicon-plus")) {
        // change the status of the button plus -> minus
        glyphicon_span.removeClass("glyphicon-plus");
        glyphicon_span.addClass("glyphicon-minus");
        // show form
        $("#friend-ticket-container").show();
        if (n_ft_form == 1 && $("#form-to-copy").length == 0) {
            // when the friend ticket button is click the first time and
            // there is no form-to-copy element, we add a new friend form.
            // Only friend ticket with email and name has the form-to-copy and
            // it is rendered by server
            add_new_friend_form();
        }
        // update has friend input
        $("input[name=\"has_friends\"]").val("1");
    } else if (glyphicon_span.hasClass("glyphicon-minus")) {
        // change glyphicon minus -> plus
        glyphicon_span.addClass("glyphicon-plus");
        glyphicon_span.removeClass("glyphicon-minus");
        // hide form
        $("#friend-ticket-container").hide();
        $("input[name=\"has_friends\"]").val("0");
    }
}

function toggle_friend_detail_form() {
    var target = $(".friend-ticket-detail-btn");
    var glyphicon_span = target.children("span");
    if (glyphicon_span.hasClass("glyphicon-plus")) {
        // change the status of the button plus -> minus
        glyphicon_span.removeClass("glyphicon-plus");
        glyphicon_span.addClass("glyphicon-minus");
        // show form
        $(".friend-ticket-detail-container").show();

    } else if (glyphicon_span.hasClass("glyphicon-minus")) {
        // change glyphicon minus -> plus
        glyphicon_span.addClass("glyphicon-plus");
        glyphicon_span.removeClass("glyphicon-minus");
        // hide form
        $(".friend-ticket-detail-container").hide();
    }
}

/**
 * application_fee_response
 */
function application_fee_response(data, form, button) {
    setting_total_fee(data.data);
    fee_items = data.data.list_fee_item_data;
    list_fee_items = data.data.list_fee_item;
    display_fee_items();
}

function display_fee_items() {
    fee_data = new Object();
    var html = "";
    var html_select_btns = "";
    var i;
    var total_data_items = 0;
    sort_fee_items_by_data_id();
    var grouped_data = group_data_by_fee_items();
    for (i = fee_items.length - 1; i >= 0; i--) {
        if (fee_items[i].quantity > 0) {
            // the quantity of the item is > 0
            total_data_items += fee_items[i].quantity;
            var item_html = render_fee_data(fee_items[i]);
            html += item_html;
            fee_data["data_" + fee_items[i].fee_data_id] = fee_items[i].quantity;
        }
    }

    // AnhLD - pharse 7
    var item_id = "item_" + fee_item_target;
    // var category;
    // fee_item_info.forEach(function(fee_item) {
    //     if(fee_item['fee_item_id'] == fee_item_target){
    //         category = fee_item;
    //     }
    // });
    html_select_btns = render_select_type_ticket(grouped_data, item_id);

    html_select_btns += render_selects_block(grouped_data, true);
    var delegate_items = render_selects_block(grouped_data, false);
    html = render_fee_overview(total_data_items) + html;
    $(".fee-item-summary").html(html);
    select_button_html = html_select_btns;
    $(".fee-item-" + fee_item_target + ".ft-select-item.fee-data-id-" + fee_data_id).html(html_select_btns);
    var list_fee_item =   $(".fee-item-" + fee_item_target + ".ft-select-item");
    for (let index = 0; index < list_fee_item.length; index++) {
        if($(list_fee_item[index]).context.classList['4'] != ("fee-data-id-" + fee_data_id)) {
            $(".fee-item-" + fee_item_target + ".ft-select-item .row.no-gap-col.ft-select-holder").remove();
            $(".fee-item-" + fee_item_target + ".ft-select-item .col-xs-7").after(render_selects_block(grouped_data, true));
        }
    }
    fee_items.forEach(function(element) {
        if(element.fee_item_id != fee_item_target){
            $(".fee-item-" + element.fee_item_id + " .row.no-gap-col.ft-select-holder").remove();
            $(".fee-item-" + element.fee_item_id).append(render_selects_block(grouped_data, true));
        }
    });
    $(".ft-delegate-item").html(delegate_items);
    update_delegate_ticket_quantity();


    // AnhLD - pharse 7
    $(".select_type_ticket").change();
}

/**
 * sort fee_items by data id DESC
 */
function sort_fee_items_by_data_id() {
    var i, j, key;
    var len = fee_items.length;
    var tmp;
    // bubble sort
    for (i = 0; i < len - 1; i++) {
        var max = fee_items[i].fee_data_id;
        key = i;
        for (j = i + 1; j < len; j++) {
            if (fee_items[j].fee_data_id > max) {
                max = fee_items[j].fee_data_id;
                key = j;
            }
        }

        if (key != i) {
            // swap
            tmp = fee_items[i];
            fee_items[i] = fee_items[key];
            fee_items[key] = tmp;
        }

    }
}

/**
 * group each data by fee_item id
 */
function group_data_by_fee_items() {
    var grouped_data = []; // hold grouped data
    var i;
    for (i = fee_items.length - 1; i >= 0; i--) {
        if (fee_items[i].quantity <= 0) continue;
        var key = "item_" + fee_items[i].fee_item_id;
        if (grouped_data.hasOwnProperty(key)) {
            // if the item is added into the grouped data
            // add its data
            grouped_data[key].push(fee_items[i]);
        } else {
            // if the item is not added into the grouped data
            // add it to the group
            grouped_data[key] = [];
            grouped_data[key].push(fee_items[i]);
        }
    }

    return grouped_data;
}

function render_fee_data(fee_item) {
    var html = "<div class=\"d-flex gap-2 no-gap-col\">" +
        "<div><p class=\"fee-item-name mb-0 text-14-16\">" + fee_item.fee_item_name + " " + fee_item.name_display + "</p></div>" +
        "<div>" + fee_item.quantity + "</div>" +
        "</div>";
    return html;
}

/**
 * render html code for the block of select buttons
 * @param grouped_data
 */
function render_selects_block(grouped_data, has_option) {
    var html = "";
    for (var fee_data in grouped_data) {
        html += render_select_holder(grouped_data[fee_data], has_option);
    }

    return html;
}

function render_fee_overview(total_item) {
    var value = translate['ご購入数'];
    if (!value) {
        value = 'ご購入数';
    }
    return "<span class=\"glyphicon glyphicon-stop text-14-16 mb-3\"></span>" +
        value + "：　" + total_item;
}

function render_select_holder(fee_data, has_options) {
    // open tag
    var html = "<div class=\"row no-gap-col ft-select-holder\"><div><b>" +
        fee_data[0].fee_item_name + "</b></div>";
    // render content
    for (var data in fee_data) {
        html += render_select_button(fee_data[data], has_options);
    }

    // render close tag
    html += "</div>";

    return html;
}

function render_select_button(fee_item, has_options) {
    var html = "<div class=\"row ft-data-row\">" +
        "<div class=\"col-xs-5\">" +
        "<label class=\"control-label\">" + fee_item.name_display + ": </label>" +
        "</div>";
    if (has_options) {
        html += render_options(fee_item) +
            "</div>";
    } else {
        html += render_delegate_items(fee_item) + "</div>";
    }

    return html;
}

function render_options(fee_item) {
    var html = "<div class=\"col-xs-4\">" +
        "<select name=\"item_data[" + fee_item.fee_item_id + "][" + fee_item.fee_data_id +
        "][]\" data-data-id=\"" + fee_item.fee_data_id +
        "\" class=\"form-control item-data-select item_data_" + fee_item.fee_data_id + "\">";

    // AnhLD - pharse 7
    // old: i < fee_item.quantity
    // new: i <=fee_item.quantity

    for (var i = 0; i <= fee_item.quantity; i++) {
        html += "<option value =" + i + " >" + i + "枚</option>";
    }

    html += "</select></div>";
    return html;
}

function render_delegate_items(fee_item) {
    var html = "<div class=\"col-xs-4\">" +
        "<span id=\"delegate_data_" + fee_item.fee_data_id + "\">" +
        fee_item.quantity + " 枚" +
        "</span>" +
        "<input type=\"hidden\" name=\"delegate_qty[" + fee_item.fee_item_id + "][" +
        fee_item.fee_data_id + "]\" value=\"" +
        fee_item.quantity + "\" id=\"delegate_qty_" + fee_item.fee_data_id + "\">" +
        "</div>";
    return html;
}

$(document).on("click", "#add-new-friend", function (e) {
    add_new_friend_form();
    update_all_selects();
});

function add_new_friend_form(item_id) {
    if ($("#i-event-fee-form").length == 0) {
        add_friend_form_with_no_fee();
        return;
    }

    var friend_form;
    var category;
    if ($("#form-to-copy").length == 0) {
        // copy friend form without email and name
        var form_rows = $("#i_application_form").children();
        fee_item_info.forEach(function(fee_item) {
            if(fee_item['fee_item_id'] == item_id){
                category = fee_item;
            }
        });
        var html = get_friend_ticket_row_html(category);
        $("#add-new-friend").parent().before(html);
        friend_form = $(".friend_ticket_holder").prev();
        var current_form = friend_form.children(".friend-form");
        copy_application_form(form_rows, current_form);
        n_ft_form++;
    } else {
        // copy friend form with email and name
        n_ft_form++;
        friend_form = $("#form-to-copy").clone();
        change_input_name_friend_form(friend_form);
        $("#add-new-friend").parent().before(friend_form);
    }

    if (select_button_html != "") {
        friend_form.children(".ft-select-item").html(select_button_html);
    }
}

function add_friend_form_with_no_fee() {
    if (n_ft_form > 1) return;
    var form_rows = $("#i_application_form").children();
    var friend_form = $(".friend-form");
    copy_application_form(form_rows, friend_form);
    n_ft_form++;
}

function copy_application_form(application_form_rows, friend_form) {
    var friend_type = $('input[name="friend_type"]').val();
    application_form_rows.each(function () {
        if ($(this).hasClass("input_row")) {
            var input_row = $(this).clone();
			//comment for id140 fix slide 4
            /*if($('#item-name-' + fee_item_target).hasClass("fee-item-type-0")) {
                copy_form_without_anything(input_row, friend_form);
            } else {*/
                if (friend_type == "friend_ticket_without_email" &&
                    input_row.find('input[name="email"]').length) {
                    return;
                }

                // AnhLD - pharse 7
                if(input_row.find('input[name="first_name"]').length == 0 && input_row.attr("show_on_friend_form") == "0"){
                    return;
                }

                var input_firstname = $(this).find('input[name="first_name"]').val();
                // input_row.find('input[name="first_name"]').val(input_firstname + '（同伴者' + n_ft_form + '）');
                input_row.find('input[name="first_name"]').val((input_firstname != "" ? input_firstname + ' - ' : '同伴者 - ') + n_ft_form);
                input_row.find("input, textarea").each(function () {
                    change_input_name($(this));

                    //AnhLD - alway friend has input first name, if not show on friend form = 1 => hide input first name
                    // fix validate friend form error
                    if(input_row.find('input[name="first_name"]').length > 0 && input_row.attr("show_on_friend_form") == "0"){
                        input_row.hide();
                        input_row.removeClass("e_row_form"); // not display in confirm form
                        // input_row.find('input[name="first_name"]').val("");
                    }
                });
                friend_form.append(input_row);
            }
       // }
    });
}

function copy_form_without_anything(input_row, friend_form){
    if(input_row.find('input[name="first_name"]').length > 0){
        var input_firstname = input_row.find('input[name="first_name"]').val();
        // input_row.find('input[name="first_name"]').val(input_firstname + '（item' + '）');
        input_row.find('input[name="first_name"]').val((input_firstname != "" ? input_firstname + ' - ' : '同伴者 - ') + n_ft_form);
        input_row.find("input, textarea").each(function () {
        change_input_name($(this));
        input_row.hide();
            //AnhLD - alway friend has input first name, if not show on friend form = 1 => hide input first name
            // fix validate friend form error
            if(input_row.find('input[name="first_name"]').length > 0 && input_row.attr("show_on_friend_form") == "0"){
                input_row.hide();
                input_row.removeClass("e_row_form"); // not display in confirm form
                // input_row.find('input[name="first_name"]').val("");
            }
        });
        friend_form.append(input_row);
    } else {
        return;
    }
}

function get_friend_ticket_row_html(category) {
    var text1 = translate['同伴者の情報に変更してください。'] ? translate['同伴者の情報に変更してください。'] : '同伴者の情報に変更してください。';
    var text2 = translate['メールアドレスも設定されている場合はチケットの送信先をご記入ください。'] ? translate['メールアドレスも設定されている場合はチケットの送信先をご記入ください。'] : 'メールアドレスも設定されている場合はチケットの送信先をご記入ください。';
    let class_item = '';
    category['count_number'] == 1 ? class_item = 'block' : class_item = 'none';
	let explain_str = '';
	if (display_explain == 0) {
		explain_str = "<br>"+ text1+"<br>"+ text2;
		display_explain = 1;
	}
    var html = "<div class=\"friend_ticket_row\" style=\"display: ${class_item}\">" +
                "<span class=\"glyphicon glyphicon-stop\"></span>${category['fee_item_name']}" + explain_str +
                "<div class=\"pull-right delete-friend-button\">" +
                "<button type=\"button\" class=\"black-button small-button\">キャンセル</button>" +
                "</div>" +
                "<div class=\"friend-form\">" +
                "</div>" +
                "<div class=\"row ft-select-item form-horizontal fee-item-${category['fee_item_id']} fee-data-id-${fee_data_id}\" >" +
                "</div>"
                "</div>";
    return html
        .replace('${class_item}', class_item)
        .replace("${category['fee_item_name']}", category['fee_item_name'])
        .replace("${category['fee_item_id']}", category['fee_item_id'])
        .replace('${fee_data_id}', fee_data_id);
}

function update_all_selects() {
    $(".ft-select-item").first().find(".item-data-select").each(function () {
        update_select_options($(this));
    });
}

/**
 * change the names of inputs of the friend form with email and names
 * @param form_container
 */
function change_input_name_friend_form(form_container) {
    form_container.find("input").each(function () {
        var name = $(this).attr("name");
        if (name && name.indexOf("friends") != -1) {
            name = name.replace(/[0-9]+/, n_ft_form);
            $(this).attr("name", name);
        }
    });
}

/**
 * change input name of an input of friend form without email and name
 * @param input
 */
function change_input_name(input) {
    if(input[0].type == "file"){
        change_input_file_id(input);
    }
    var friend_name = get_friend_input_name(input.attr("name"), n_ft_form);
    input.attr("name", friend_name);
}

/**
* AnhLD - update input file id
* 1 input file name have an id, and have one fake input and one <a> btn to pick file
* both of them have attr file_input_id => need to be updated when append friend form
*/
function change_input_file_id(input){
    var old_id = input.attr("id");
    var new_id = old_id + "_" + Math.round(Math.random() * 10000);
    var container = input.parent("div");
    container.find("[file_input_id="+old_id+"]").each(function(){
        $(this).attr("file_input_id", new_id);
    });
    input.attr("id", new_id);
}


// when user select the quantity of item data
$(document).on("change", ".item-data-select", function () {
    // get the value of select button
    var value = $(this).find(":selected").val(); // quantity selected
    var data_id = $(this).data("data-id"); // id of item data

    var name = $(this).attr("name");
    var data_qty = calculate_item_qty(name);
    update_select_options($(this));
    // update the number of remain ticket at the add button
    update_delegate_ticket_quantity();

});

function calculate_item_qty(select_name) {
    var qty = 0;
    $("#friend-ticket-container").find("select[name=\"" + select_name + "\"]").each(function () {
        qty += parseInt($(this).find(":selected").val());
    });

    return qty;
}

function update_select_options(select_button) {
    var data_id = select_button.data("data-id");
    var data_qty = calculate_item_qty(select_button.attr("name"));
    var remain_qty = fee_data["data_" + data_id] - calculate_item_qty(select_button.attr("name"));
    var filter = ".item_data_" + data_id;
    $(filter).each(function () {
        var currentSelectObj = $(this);
        var select_val = parseInt(currentSelectObj.val());
        if (select_val === data_qty) {
            reset_current_select(currentSelectObj);
        } else {
            //We put away 1 ticket for the delegate
            var availableTicketForFriend = remain_qty + select_val - 1;

            // AnhLD - pharse 7 - no need disabled

            // currentSelectObj.find("option").each(function () {
            //     var optionObj = $(this);
            //     if (optionObj.val() > availableTicketForFriend) {
            //         optionObj.attr("disabled", "disabled");
            //     } else {
            //         optionObj.removeAttr("disabled");
            //     }
            // });
        }
    });
}

function update_delegate_ticket_quantity() {
    var selected_quantity = calculate_selected_quantity();
    var total_remain = 0;
    for (var property in fee_data) {
        if (fee_data.hasOwnProperty(property)) {
            var data_id = property.substring(5);
            var delegate_name = "delegate_data_" + data_id;
            var remain;
            if (selected_quantity[delegate_name] == undefined) {
                remain = fee_data[property];
            } else {
                remain = fee_data[property] - selected_quantity[delegate_name];
            }
            var span_selector = "#" + delegate_name;
            var input_selector = "#delegate_qty_" + data_id;
            $(span_selector).html(remain + "枚");
            $(input_selector).val(remain);
            total_remain += remain;
        }
    }

    if (total_remain <= 1) {
        // disable add-new-friend button
        $("#add-new-friend").prop("disabled", true);
    } else {
        $("#add-new-friend").prop("disabled", false);
    }
}

function calculate_selected_quantity() {
    var total = 0;
    var selected_quantity = [];
    $(".ft-select-item").first().find(".item-data-select").each(function () {
        var selected_qty = calculate_item_qty($(this).attr("name"));
        total += selected_qty;
        var data_id = $(this).data("data-id");
        var name_in_delegate = "delegate_data_" + data_id;
        selected_quantity[name_in_delegate] = selected_qty;
    });

    return selected_quantity;
}

function reset_current_select(current_select) {
    current_select.find("option").each(function () {
        $(this).removeAttr("disabled");
    });
}

function download_action_response(data, obj) {
    var group = "alert-success";
    if (data.state != 1) {
        group = "alert-danger";
    }
    if (data.state == 1) {
        if (data.hasOwnProperty("url")) {
            var url = data.url;
            window.location = url;
        }
    }
    $.jGrowl("<i class='icon16 i-checkmark-3'></i> " + data.msg, {
        group: group,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        }
    });
}

/**
 * show error on friend form
 * @param errorList
 * @param form
 */
function show_error_on_friend_form(errorList, form) {
    var selector = creat_input_selector("");
    form.find(selector).each(function () {
        // don't process hidden input
        if ($(this).attr("type") == "hidden") return;
        var name = $(this).attr("name");
        if (name) {
            name = name.replace("[]", "");
            if (errorList.hasOwnProperty(name)) {
                var parent = $(this).closest(".form-group");
                change_error_state($(this), false);
                parent.find("label.help-block").html(errorList[name]);
            } else {
                change_error_state($(this), true);
            }
        }
    });
}

$(document).on("click", ".delete-friend-button", function () {
    if ($(".delete-friend-button").length == 1) {
        // if there is only one friend left
        // hide friend form instead of deleting it
        toggle_friend_form();
        return;
    }
    $(this).closest(".friend_ticket_row").remove();
    update_delegate_ticket_quantity();
    update_all_selects();
});

$(document).on("change", ".e_quantity", function () {
	//Check fee num
	var obj = $(this);
	var check_fee_url = $('#check_fee_num_url').attr('data-url');
	var item_data = {};
	$('.e_quantity').each(function() {
		if ($(this).val() <= 1) display_explain = 0;
		if ($(this).val() < 1) return;
		var item_id = $(this).attr('item-id');
		if (typeof item_data[item_id] === 'undefined') item_data[item_id] = 1;
		else item_data[item_id] ++;
	});
    console.log(item_data);
	call_ajax_link(check_fee_url, item_data, obj);

    fee_item_target = obj.attr('item-id');
    fee_data_id = obj.data('id');
    fee_item_type = $("label[id*='item-name-" + fee_item_target +"']").hasClass("fee-item-type-0") ? '0' : '1';
    // AnhLD - pharse 7 - auto show total quantity - 1 friend form
    auto_show_friend_forms();
    var pageURL = $(location).attr("href");
    if (pageURL.indexOf('application_form/add/4440') == -1) {
        if(Object.keys(item_data).indexOf(fee_item_target) >= 0 ) {
            $('#i_ship_info').removeClass("soft_hide");
            $('#i_ship_address_info').removeClass("soft_hide");
        } else if(Object.keys(item_data).indexOf(fee_item_target) < 0 ) {
            $('#i_ship_info').addClass("soft_hide");
            $('#i_ship_address_info').addClass("soft_hide");
        }
    }
// console.log('change .e_quantity');
    if ($(this).hasClass("e_change_fee")) {
		change_fee(obj);
        return;
    }
    var free_item = new Object();
    free_item.quantity = parseInt($(this).val());
    free_item.fee_item_id = parseInt($(this).attr("item-id"));
    free_item.fee_data_id = $(this).data("id");
    free_item.fee_item_type = $("label[id*='item-name-" + free_item.fee_item_id + "']").hasClass("fee-item-type-0") ? '0' : '1';
	if (window.mobileAndTabletcheck()) {
		free_item.name_display = $(this).parents('tbody').prev().children().children().first().html().trim();
	} else {
		free_item.name_display = $(this).closest("tr").children().first().html().trim();
	}
    var item_name_selector = "#item-name-" + free_item.fee_item_id;
    free_item.fee_item_name = $(item_name_selector).html().trim();

    var free_item_index = has_free_item(fee_items, free_item);
    if (free_item_index != -1) {
        // the fee_item is already added to fee_items
        if (free_item.quantity == 0) {
            // quantity is 0
            // this item should be removed from the list
            fee_items.splice(free_item_index, 1);
        } else {
            fee_items[free_item_index] = free_item;
        }
    } else {
        // the fee_item is not added to fee_items
        if (free_item.quantity) {
            // if the quantity is not 0
            fee_items.push(free_item);
        }
    }

    display_fee_items();
});

function reset_quantity_callback(data, obj) {
	//reset quantity input
	var fee_data_id = obj.attr('data-id');
	obj.val('0');
	if (obj.hasClass("e_change_fee")) change_fee(obj);
	auto_show_friend_forms();
	application_response(data, obj);

    //handle
    const dataId = obj.attr("data-id");
    const fee_items_temp =  [];
    fee_items.forEach((val, index) => {
        if (Number(val.fee_data_id) != Number(dataId))
            fee_items_temp.push(val);
    })

    fee_items = fee_items_temp;
    display_fee_items();
}


/**
 * check if free_item already added into fee_items array
 * @param fee_items
 * @param free_item
 * @returns {number}
 */
function has_free_item(fee_items, free_item) {
    var i;

    for (i = 0; i < fee_items.length; i++) {
        if (free_item.fee_item_id == fee_items[i].fee_item_id &&
            free_item.fee_data_id == fee_items[i].fee_data_id) {
            return i;
        }
    }

    return -1;
}

function form_checking_friend_ticket(data, form, button) {
    $.jGrowl(data.msg, {
        group: "alert-danger",
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        }
    });

    if (data.error) {
        var selector = "#choose_" + data.error.field_id;
        $(selector).closest(".e_input_row").append("<div class=\"col-xs-9 duplicate-friend-ticket\" style=\"color: red\">同伴者一括申込フォームはどちらか一つを選択してください。</div>");
    }
}

$(".application-main-button-container button[type=\"submit\"]").click(function () {
    $(".duplicate-friend-ticket").remove();
});

function on_application_form_response(data, form, button) {
    clear_error(form);
    button && button.removeAttr("disabled");
    form = form || $(".e_ajax_submit");

    var state = parseInt(data.state) || 0;
    switch (state) {
        case 1:
            on_application_form_validated(data, form);
            break;
        default:
            on_application_form_error(data, form);
            break;
    }
}

function on_application_form_validated(data, form) {
    clear_error(form);
    hide_application_form(form);
    display_application_confirm(data, form);
    window.scrollTo(0, 0);
}

function clear_error(form) {
    var selector = creat_input_selector("");
    form.find(selector).each(function () {
        change_error_state($(this), true);
    });
    var otherErrorSelector = ".error_total_quantity";
    form.find(otherErrorSelector).remove();
}

function display_application_confirm(data, form) {
    data.html && form.find("#i_application_confirm").append(data.html);
    var form_content = $("#i_application_form");
    var confirm_content = application_form_content_to_confirm_content(form_content);
    form.find(".application-confirm .application-confirm-content").html(confirm_content);
}

function application_form_content_to_confirm_content(form_content) {
    var confirm_content = $("<div class = 'form_box_content'>");
    application_transfer_rows(form_content, confirm_content, ".e_row_form:not(.friend-form .e_row_form)");
    var event_fee_form = build_event_fee_form_confirm();
    confirm_content.append(event_fee_form);
    var standalone_friend_area_confirm = build_standalone_friend_area_confirm(form_content);
    confirm_content.append(standalone_friend_area_confirm);
    confirm_content.find("label").removeAttr("for");
    confirm_content.find("input,select,textarea").removeAttr("name").prop("disabled", true);
    return confirm_content;
}

function application_transfer_rows(form_content, confirm_content, row_selector) {
    row_selector = row_selector || ".e_row_form";
    form_content.find(row_selector).not(function(index, el){
        // AnhLD - not copy text view to confirm form
        if($(el).has("div.text_view").length > 0){
            return true;
        }
        return false;
    }).each(function () {
        var row = application_form_row_to_confirm_row($(this));
        confirm_content.append(row);
    });
}

function build_event_fee_form_confirm() {
    var origin_event_fee_form = $("#i-event-fee-form");
    var event_fee_form = origin_event_fee_form.clone();
    if (event_fee_form.length <= 0) return "";
    change_event_fee_form_general_area(origin_event_fee_form, event_fee_form);
    transfer_friend_ticket_rows(origin_event_fee_form, event_fee_form);
    return event_fee_form;
}

function build_standalone_friend_area_confirm(form_content) {
    var standalone_friend_area = form_content.find(".e_friend_area--standalone");
    if (standalone_friend_area.length <= 0) return "";
    var standalone_friend_area_confirm = standalone_friend_area.clone();
    standalone_friend_area_confirm.find(".friend_ticket_holder").remove();
    transfer_friend_ticket_rows(standalone_friend_area, standalone_friend_area_confirm);
    return standalone_friend_area_confirm;
}

function change_event_fee_form_general_area(origin_event_fee_form, event_fee_form) {
    event_fee_form.attr("id", generate_unique_id("i-event-fee-form-confirm"));
    event_fee_form.find(".fee-item-error, .friend_ticket_holder").remove();

    var origin_rows = origin_event_fee_form.find(".item_sub.e_row");
    var confirm_rows = event_fee_form.find(".item_sub.e_row");

    confirm_rows.each(function (number_order) {
        var quantity_control = $(this).find(".e_quantity");
        var quantity;
        if (quantity_control.is("select")) {
            quantity = application_read_select_value($(origin_rows[number_order]).find(".e_quantity")) || 0;
        } else {
            quantity = parseInt(quantity_control.val()) || 0;
        }
        quantity_control.after(quantity);
        quantity_control.remove();
    });

    var quantity_input = event_fee_form.find(".e_quantity");
    var quantity_input_wrapper = quantity_input.parent();
    var quantity = quantity_input.val();
    quantity_input_wrapper.html(quantity);
}

function transfer_friend_ticket_rows(origin_area, confirm_area, is_standalone_friend_area) {
    var origin_friend_ticket_rows = origin_area.find(".friend_ticket_row");
    confirm_area.find(".friend_ticket_row").each(function (number_order) {
        var friend_form_wrapper = $(this);
        friend_form_wrapper.find(".delete-friend-button").remove();
        if (is_standalone_friend_area) {
            transfer_rows_of_standalone_friend_area(friend_form_wrapper);
        } else {
            transfer_rows_of_event_fee_form(friend_form_wrapper, origin_friend_ticket_rows, number_order);
        }
    });
}

function transfer_rows_of_standalone_friend_area(friend_form_wrapper) {
    var friend_form = friend_form_wrapper.find(".e_friend_ticket_container");
    var friend_form_confirm = $("<div class='col-xs-12 no-gap-col'>");
    application_transfer_rows(friend_form, friend_form_confirm);
    friend_form.before(friend_form_confirm);
    friend_form.remove();
}

function transfer_rows_of_event_fee_form(friend_form_wrapper, origin_friend_ticket_rows, number_order) {
    var ft_select_item = friend_form_wrapper.find(".ft-select-item");
    var item_data_select = ft_select_item.find(".ft-data-row .item-data-select");
    var item_data_select_name = item_data_select.attr("name");
    var origin_item_data_select = $(origin_friend_ticket_rows[number_order]).find(".ft-data-row .item-data-select");
    var item_data = application_read_select_value(origin_item_data_select);
    item_data_select.after(item_data);
    item_data_select.remove();

    // AnhLD - pharse 7 show selected type ticket
    var origin_select_type_ticket = $(origin_friend_ticket_rows[number_order]).find(".select_type_ticket");
    var selected_option_type_ticket = origin_select_type_ticket.val();
    ft_select_item.find(".select_type_ticket").val(selected_option_type_ticket);

    var friend_form = friend_form_wrapper.find(".friend-form");
    var friend_form_confirm = $("<div class='friend-form friend-form-confirm'>");
    application_transfer_rows(friend_form, friend_form_confirm);
    friend_form.before(friend_form_confirm);
    friend_form.remove();
}

function generate_unique_id(prefix, subfix) {
    return prefix + (new Date().getTime()).toString(36) + subfix;
}

function application_form_row_to_confirm_row(form_row) {
    var data = application_form_row_to_row_data(form_row);
    return application_row_data_to_confirm_row(data);
}

function application_form_row_to_row_data(form_row) {
    var children_form = form_row.find(".ship_address_form");
    if (children_form.length) {
        var parent_label = get_label(form_row, ".no_noted_input");
        var child_rows = children_form.find(".row");
        var children = child_rows.map(function (index, selector) {
            return application_form_row_to_row_data($(selector));
        }).toArray();
        return {label: parent_label, children: children};
    } else {
        var label = get_label(form_row, ".input_title");
        var input = form_row.find("input:not([type=hidden], .fake_input), input[type=file], textarea");
        if (input.length) {
            return application_input_to_row_data(input, label);
        } else {
            var select = form_row.find(".form-group select:not([type=hidden])");
            var value = application_read_select_value(select);
            return {label: label, value: value};
        }
    }
}

function get_label(area, selector) {
    var obj = area.find(selector);
    if (obj.length === 0) obj = area.find("label");
    return (obj.html() || "").trim();
}

function application_input_to_row_data(input, label) {
    var value = "";
    var input_type = input.attr("type");
    // console.log(input_type, input);
    var convert_labels = {
        "氏": "氏　名",
        "フリガナ氏": "フリガナ",
        "都道府県": "都道府県、区市町村"
    };
    if (convert_labels.hasOwnProperty(label)) {
        label = convert_labels[label];
        input.each(function () {
            value += value ? "　" : "";
            value += $(this).val();
        });
    } else if (input_type === "radio" || input_type === "checkbox") {
        input.each(function () {
            if (this.checked) {
                value += value ? "、" : "";
                value += $(this).next().html();
            }
        });
    } else if (input_type === "password") {
        value = new Array(input.val().length + 1).join("•");
    } else if(input_type === "file"){
        if (input[0].files && input[0].files[0]) {
            var reader = new FileReader();
            var id = "img_"+(new Date()).getTime();
            value = "<img style='max-width: 30mm; max-height: 40mm;' id='"+id+"' src=''/>";
            reader.onload = function(e) {
                $("#"+id).attr('src', e.target.result);
            }
            reader.readAsDataURL(input[0].files[0]);
        }
    } else {
        value = input.val();
    }
    return {label: label, value: value};
}

function application_read_select_value(select) {
    if (!select.is("select")) return 0;
    var select_value = select.val();

    // console.log("ANHLDANHLD", select_value);

    return select_value ? select.find("option[value=" + select_value + "]").html() : "";
}

function application_row_data_to_confirm_row(data, is_no_border_bottom) {
    if (data.hasOwnProperty("children")) {
        var children_length = data.children.length;
        var child_rows = data.children.map(function (child, index) {
            return application_row_data_to_confirm_row(child, index < children_length - 1);
        });
        return "<div class='clearfix m-t-20 text-bold'>" + data.label + "</div>" + child_rows.join("");
    } else {
        var label_html = "<div class='no-gap-col' style='word-wrap: break-word;white-space: pre-wrap;'>" + data.label + "</div>";
        var value_html = "<div class='text-bold' style='white-space: pre-wrap'>" + data.value + "</div>";
        var css_class = "d-flex flex-wrap text-14-16 input_row no-gap-row gap-3" + ( is_no_border_bottom ? "no-border-bottom" : "");
        return "<div class='" + css_class + "'>" + label_html + value_html + "</div>";
    }
}

function hide_application_form(form) {
    form.find(".form_box_content,.application-form-submit,.application-form-modal-trigger").hide();

}

function display_application_form(form) {
    form.find(".form_box_content,.application-form-submit,.application-form-modal-trigger").show();
}

function on_application_form_error(data, form) {
    if (data.hasOwnProperty("redirect") && data.redirect) {
        setTimeout(function () {
            window.location = data.redirect;
        }, 3000);
    }

    if (data.hasOwnProperty("reload") && data.reload && (data.html == undefined || !data.html)) {
        setTimeout(function () {
            window.location.reload();
        }, 3000);
    }
    form.find(".i_item_fee .fee-item-error").hide();
    form.find("#total_ticket_error").hide();
    form.find(".error_total_quantity").hide();
    form.find("#total_quantity_show_error .fee-item-error").hide();
    /* error */
    if (!data.state) {
        show_application_form_errors(data, form);
    }

    notify(data.msg, "alert-danger");
}

function show_application_form_errors(data, form) {
    if (data.hasOwnProperty("error")) {
        show_error(data.error, form);
    }
    if (data.hasOwnProperty("error_fee")) {
        show_error_fee(data.error_fee, form);
    }
    if (data.hasOwnProperty("error_street")) {
        show_error_street(data.error_street, form);
    }

    if (data.hasOwnProperty("friend_validate")) {
        $.each(data.friend_validate, function () {
            var friend_id = this["friend_id"];
            var name = "friends[" + friend_id + "]";
            var obj = $("input[name^=\"" + name + "\"]:first");
            var friend_form = obj.parents(".friend_ticket_row");
            show_error_friend(this, friend_form);
        });
    }
}

function notify(message, group) {
    group = group || "alert-info";
    message && $.jGrowl(message, {
        group: group,
        position: "top-right",
        sticky: false,
        closeTemplate: "<i class=\"fa fa-times\" aria-hidden=\"true\"></i>",
        animateOpen: {
            width: "show",
            height: "show"
        }
    });
}

function adjust_table_header(data){

    // console.log(data.custom_field_choice);
    if(data.custom_field_choice.length){
        $(".e_custom_field_application").find("input[type=checkbox]").each(function(k, v){
            var value = $(v).val();
            if(data.custom_field_choice.indexOf(value) == -1){
                $(v).prop('checked', false);
            }
        });
    }

    var intervalId = setInterval(function(){
        var table = $(".table-application-list table");
        if(table.length == 0) return;

        // console.log(table);
        var list_th = table.find("th");

        var list_width = [];

        var total_width = 0;
        list_th.each(function(k, v){
            if ($(v).attr('field_name') != "checkin_time") {
                list_width.push($(v).outerWidth());
                $(v).css("width", $(v).outerWidth());
                total_width += $(v).outerWidth();
            } else {
                list_width.push('145');
                $(v).css("width", 145);
                $(v).css("min-width", 145);
                total_width += 145;
            }
        });

        newThead = $("<table class='table table-striped table-bordered table-hover dataTable no-footer DTTT_selectable background-write' style='font-size: 0;' cellpadding='0' cellspacing='0' border='0'><thead style='cell'></thead></table");
        newThead.find("thead").html(table.find("thead").html());
        //newTable = $("<div style='position: fixed; top:0; display:none;overflow: hidden;'><div class='scroll_container' style='width: 940px;overflow-x: auto;'><div class='th_container' style='width: "+(total_width+10)+"px;'></div></div></div>");
        newTable = $("<div style='position: fixed; top:0; display:none;overflow: hidden;'><div class='scroll_container' style='width:100%;overflow-x: auto;'><div class='th_container' style='width: "+(total_width+10)+"px;'></div></div></div>");
		newTable.find(".th_container").append(newThead);
        // newTable.find("th").css("display", "inline-block");
        $(".table-application-list").prepend(newTable);

        var scrollbarWidth = getScrollbarWidth();
        $(".table-application-list").css("margin-right", -scrollbarWidth + "px");

        // newTable.css("height", newTable.outerHeight() - scrollbarWidth);

        // console.log(newTable.outerHeight());
        // $(".table-application-list .manager_table_scroll").find("thead").css("visibility", "collapse");

        var scrollItem = $(".table-application-list .manager_table_scroll")[0];
        var lockScrollHeader = true;
        var timer;
        var oldScrollValue= 0;
        $(scrollItem).on("scroll", function(e){
            lockScrollHeader = false;
            clearTimeout(timer);
            // console.log(lockScrollHeader);
            newTable.find(".scroll_container").scrollLeft($(this).scrollLeft());
            oldScrollValue = $(this).scrollLeft();
            timer = setTimeout(function(){
                lockScrollHeader = true;
            }, 500);
        });

        newTable.find(".scroll_container").on("scroll", function(e){
            // if(lockScrollHeader) {
            //     $(this).scrollLeft(oldScrollValue);
            // }

            $(scrollItem).scrollLeft($(this).scrollLeft());
        });

        clearInterval(intervalId);
    }, 1000);

}

function getScrollbarWidth() {
    var outer = document.createElement("div");
    outer.style.visibility = "hidden";
    outer.style.width = "100px";
    outer.style.msOverflowStyle = "scrollbar"; // needed for WinJS apps

    document.body.appendChild(outer);

    var widthNoScroll = outer.offsetWidth;
    // force scrollbars
    outer.style.overflow = "scroll";

    // add innerdiv
    var inner = document.createElement("div");
    inner.style.width = "100%";
    outer.appendChild(inner);

    var widthWithScroll = inner.offsetWidth;

    // remove divs
    outer.parentNode.removeChild(outer);

    return widthNoScroll - widthWithScroll;
}

function get_manager_data_response(data, obj){
    $(".subscribers-top-paging").html(data.paging.paging);
    $(".subscribers_post").val(data.paging.post);
    $(".subscribers_limit").val(data.paging.limit);
    $(".subscribers-top-paging").find(".e_data_paginate").addClass("subscribers");
    default_data_table(data, obj);
    adjust_table_header(data);
}


// AnhLD - pharse 7 - auto show total quantity - 1 friend form
function auto_show_friend_forms(){
    var fee_item_obj = $(".fee-item-" + fee_item_target);
    var total_ticket_quantity = get_total_ticket_quantity();
    var current_number_friend_form = n_ft_form - 1;
	var number_friend_form_to_add = total_ticket_quantity - current_number_friend_form - 1; // -1 for origin form
    if(number_friend_form_to_add > 0){
        $("input[name=\"has_friends\"]").val("1");
    }

    if(number_friend_form_to_add >= 0){
        $("#friend-ticket-container").show();
        for(var i = 0; i < number_friend_form_to_add; i++){
            add_new_friend_form(fee_item_target);
        }
        update_all_selects();
    }else if(number_friend_form_to_add < 0){
        remove_friend_form(Math.abs(number_friend_form_to_add));
    }
}

// AnhLD - pharse 7 - get total ticket quantity
function get_total_ticket_quantity(){
    var total_ticket_quantity = 0;
    $(".e_quantity").each(function () {
        if($(this).val() == "") $(this).val(0);
        total_ticket_quantity += parseInt($(this).val());
    });
    return total_ticket_quantity;
}

// AnhLD - pharse 7 - remove last {number_form} friend form
function remove_friend_form(number_form){
    var list_friend_form = get_list_friend_form();
    // console.log(list_friend_form.length);

    for(var i = 0; i < number_form; i++){
        var friend_form = list_friend_form.pop();
        if(friend_form) {
            friend_form.remove();
            n_ft_form--;
        }
    }

    if(n_ft_form == 1){
        $("input[name=\"has_friends\"]").val("0");
    }
}

// AnhLD - pharse 7 - get list friend form
function get_list_friend_form(){
    var list_friend_form = [];
    $(".friend_ticket_row").each(function(k, v){
        if($(v)[0].classList.length == 1 && $(v).children(".fee-data-id-" + fee_data_id + ".ft-select-item").length != 0){
            list_friend_form.push($(v));
        }
    });
    return list_friend_form;
}

// AnhLD - pharse 7 - render select type ticket (in all category) - to replace select quantity per category
function render_select_type_ticket(grouped_data_fee_item, item_id){
    var text_require = translate['選択してください'] ? translate['選択してください'] : '選択してください';
    var ret = $("<div>");
    var select = $("<select class='select_type_ticket select_custom-onlygary rounded-16 minw-120-200'></select>");
    var option_value = "";
    var option_display = "";
    var option_html = "";
    if(grouped_data_fee_item[item_id] && typeof grouped_data_fee_item[item_id] !== 'undefined') {
        grouped_data_fee_item[item_id].forEach(function(value, index){
            if(value.feee_item_type == "1") {
                option_value = "item_data["+value.fee_item_id+"]["+value.fee_data_id+"][]";
                option_display = value.name_display;
                option_html = "<option value='" + option_value + "'>" + option_display + "</option>";
                select.append(option_html);
            } else {
                if(value.fee_data_id == fee_data_id){
                    option_value = "item_data["+value.fee_item_id+"]["+value.fee_data_id+"][]";
                    option_display = value.name_display;
                    option_html = "<option selected value='" + option_value + "'>" + option_display + "</option>";
                    select.append(option_html);
                } else {
                    option_value = "item_data["+value.fee_item_id+"]["+value.fee_data_id+"][]";
                    option_display = value.name_display;
                    option_html = "<option value='" + option_value + "'>" + option_display + "</option>";
                    select.append(option_html);
                }
            }
        });
    }
    ret.append(select);
    return "<div class='col-xs-5 text-center'>"+ text_require+"</div><div class='text-center mt-3'>" + ret.html() + "</div>";
}

function changePagingLimit(e) {
    var limit = $(e).data("limit");
    var $li = $(e).closest('li');
    if ($li.hasClass("active")) {
        return;
    }
    $li.siblings().removeClass('active');
    $li.addClass('active');
    $(".e_changer_number_record").val(limit);
    creat_ajax_table($(".e_manager_table_container"));
}

function toggleFields(e) {
    var $fields = $(".e_custom_field_application");
    var $span = $(e).find("span");
    var $isToggle = localStorage.getItem('toggleFields') ?? true;
    if ($fields.hasClass("hidden")) {
        $fields.removeClass("hidden");
        $span.removeClass("glyphicon-plus").addClass("glyphicon-minus");
        $isToggle = true;
    } else {
        $fields.addClass("hidden");
        $span.removeClass("glyphicon-minus").addClass("glyphicon-plus");
        $isToggle = false;
    }
    localStorage.setItem('toggleFields', $isToggle);
}
$(document).ready(function() {
    let isVisible = localStorage.getItem('listFieldVisible');

    if (isVisible || isVisible === null) {
        $('#list_field_application_form').show();
        $('.toggle-fields svg:first-child').show();
        $('.toggle-fields svg:last-child').hide();
    } else {
        $('#list_field_application_form').hide();
        $('.toggle-fields svg:first-child').hide();
        $('.toggle-fields svg:last-child').show();
    }

    $('.toggle-fields').click(function() {
        const $listField = $('#list_field_application_form');
        const $minusIcon = $(this).find('svg:first-child');
        const $plusIcon = $(this).find('svg:last-child');

        if ($listField.is(':visible')) {
            $listField.hide();
            $minusIcon.hide();
            $plusIcon.show();
            localStorage.setItem('listFieldVisible', 'false');
        } else {
            $listField.show();
            $minusIcon.show();
            $plusIcon.hide();
            localStorage.setItem('listFieldVisible', 'true');
        }
    });
});

$(document).on('change', '.transaction_type', function () {
    var id = document.getElementById('error_date_time').value;
    //payment over time
    const OVER_TIME = 1;
    var error = '';
    $.ajax({
        url: '/manager/application_form/check_time_application/' + id,
        type: "GET",
        success: function (response) {
            if(response == OVER_TIME) {
                error = '<label class="help-block col-sm-8 col-xs-12 col-sm-offset-3">'+my_lang('只今、本申し込み期間中ではございません。')+'</label>';
                $("#subscriber_btn").prop("disabled", true);
            }
            $(".error_date_time .help-block").remove();
            $(".error_date_time").append(error)
        },
    })
})

$(document).ready(function () {
    var input_max_choice = $('.max_choice');
    input_max_choice.each(function (item) {
        var name = $(this).attr('name');
        var limit = $(this).val();
        $('input[name="'+ name +'[]"]').on('change', function(evt) {
            if($('input[name="'+ name +'[]"]:checked').length > limit & limit > 0) {
                var form = $('.' + name);
                form.find('.help-block').remove();
                form.append('<label class="help-block col-sm-8 col-xs-12 col-sm-offset-3">' + my_lang('ご回答数は') + limit + my_lang('択です。') + '</label>');
                $(this).prop('checked', false);
            } else {
                var form = $('.' + name);
                form.find('.help-block').remove();
            }
        });
    });

    $(document).on("change", ".e_pay_item", function () {
        change_fee();
    });

    function change_fee() {
        var total_sub_fee = 0;
        var total_tax = 0;
        $("#i-event-fee-form").find(".e_pay_item").each(function () {
            if($(this).is(":checked")){
                total_sub_fee += $(this).data("price");
            }
        });
        total_tax = total_sub_fee * 0.1;
        total_bill = total_sub_fee + total_tax;

        $(".total_sub_fee").text(format_number(total_sub_fee));
        $(".total_tax").text(format_number(total_tax));
        $(".total_bill").text(format_number(total_bill));
        $(".total_bill").siblings("input[name=total_bill]").val(total_bill);
    }
})
function uncheck_radio_transaction_type() {
    $('.payment_method input[name="transaction_type"]').prop('checked', false);
}
