function login_response(data, form, button) {
    button.removeAttr('disabled');
    if (data.state == 1) { /* Thành công */
        if(data.link_create){
            $.ajax({
                type: 'GET',
                url: data.link_create,
                success: function (data) {
                    console.log(data);
                },
                error: function (xhr, status, error) {
                    var err = eval("(" + xhr.responseText + ")");
                    alert(err.Message);
                }
            });
        }
        if (data.redirect) {
            window.location = data.redirect;
        }
    } else { /* Thất bại */
        if (data.msg) {
            form.find("label.help-block").html(data.msg).show();
        }
    }
}

function login_response_remake(data, form, button) {
    if (data.state == 1) { /* Thành công */
        if (data.redirect) {
            window.location = data.redirect;
        }
    } else { /* Thất bại */
        if (data.msg) {
            form.find("label.help-block").html(data.msg).show();
        }
    }
}



$(document).ready(function () {
    $(document).on("click", "#forget-password-back", function () {
        $("#forget-account-confirm").hide();
        $("#forget-account-form").show();
    });

    function getUrlParameter(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    };

    var forget_password = getUrlParameter('forget_password');
    if (forget_password) {
        var url = $('#i_forget_password').attr('href') + "?forget_password=true";
        call_ajax_link(url, {}, $('#i_forget_password'));
    }
    var create_account = getUrlParameter('action');
    if (create_account == 'reset_password') {
        var url = $('#i_forget_password').data('forget-password-url');
        call_ajax_link(url, {}, $('#i_forget_password'));
    }
});

function forget_account_check(data, form, button) {
    button.removeAttr('disabled');
    if (data.state) {
        $("#forget-account-form").hide();
        $("#forget-account-success").show();
    } else {
        if (data.error) {
            var parent = $('input[type=email].add-user-input').closest(".form-group");
            change_error($('input[type=email].add-user-input'), false);
            parent.find("label.error").html(data.error);
        } else {
            change_error($('input[type=email].add-user-input'), true);
        }
    }
}

function forget_account_save(data, obj) {
    console.log(data);
    if (data.state) {
        $("#forget-account-form").hide();
        $("#forget-account-confirm").hide();
        $("#forget-account-success").show();
    } else {
        alert(data.msg);
    }
}
function change_error(obj, is_valid) {
    var parent = obj.closest("div");
    if (is_valid) {
        parent.find("label.error").hide();
    } else {
        var error = parent.find("label.error"); //length;//.children(".error");
        if (error.length) {
            error.show();
        } else {
            parent.append("<label class='error no-bold'></label>").show();
        }
    }
}
function login_default_ajax_link(data, obj) {
    var jgrow = "alert-danger";
    if (data.state == 1) {
        jgrow = "alert-success";
        if (data.html) {
            var $modal = $("<div class='modal fade e_modal_content'>");
            $modal.html(data.html);
            $modal.modal({
                backdrop: 'static',
            });
        }
    }
    $.jGrowl(data.msg, {
        group: jgrow,
        position: 'top-right',
        sticky: false,
        closeTemplate: '<i class="fa fa-times" aria-hidden="true"></i>',
        animateOpen: {
            width: 'show',
            height: 'show'
        },
        afterOpen: function () {
            if (data.hasOwnProperty("redirect") && data.redirect) {
                setTimeout(function () {
                    window.location = data.redirect;
                }, 1000);
            }
        }
    });
}