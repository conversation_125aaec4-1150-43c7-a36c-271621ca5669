/**
 * Created by <PERSON><PERSON> on 15/07/2016.
 */
$(document).ready(function () {
    $(document).on("click", '.left-box input[type=checkbox]', function () {
        var checkbox_name = $(this).attr('name');
        if ($(this).prop("checked")) {
            $(this).addClass('checked');
            $(this).removeClass('not_checked');
            $('.right-box-content .event_' + checkbox_name).show();
            $('.right-box-content .event_' + checkbox_name).siblings('.ticket_group_name').show();
            if (checkbox_name.match(/sms[0-9]+/)) {
                var value = $(this).parents('div.form-group').find('input[type=text]').val();
                $('.right-box-content .event_' + checkbox_name).html(value);
            }
        } else {
            $(this).removeClass('checked');
            $(this).addClass('not_checked');
            $('.right-box-content .event_' + checkbox_name).hide();
            $('.right-box-content .event_' + checkbox_name).siblings('.ticket_group_name').hide();
            $('#check_all').prop("checked", false);
        }
    });

    $(document).on("click", '#check_all', function () {
        if ($(this).prop("checked")) {
            $('.left-box input[type=checkbox]').prop("checked", true);
            $('.right-box-content .ticket-row').show();
        } else {

            $(".checkbox-input-ticket").find('input[type=checkbox].not_checked, input[type=checkbox].not-default:not(input[type=checkbox].checked)').each(function () {
                var checkbox_name = $(this).attr('name');
                $(this).prop("checked", false);
                $('.right-box-content .event_' + checkbox_name).hide();
                $('.right-box-content .event_' + checkbox_name).siblings('.ticket_group_name').hide();
            });
        }
    });
    $(document).on("change", ".left-box input[type=text]", function () {
        var add_msg = $(this).val();
        var checkbox_name = $(this).parents('div.checkbox_ticket_parent').find('input[type=checkbox]').attr('name');
        if (checkbox_name.match(/sms[0-9]+/)) {
            $('.right-box-content .event_' + checkbox_name).html(add_msg);
        }
    })
    $('#event_stop').find('input').attr('disabled', 'disabled');

    // $(window).scroll(function (e) {
    //     var viewportOffset = $('.content-box')[0].getBoundingClientRect();
    //     if (viewportOffset.top < 0) {
    //         // if the content-box starts to be invisible
    //         // set the margin of center box and right box to
    //         // make it invisible
    //         var margin_top = -1 * viewportOffset.top;
    //         var gray = $('.gray-left-box-content').height();
    //         var green = $('.green-left-box-content').height();
    //         var blue = $('.blue-left-box-content').height();
    //         var height_left = gray + green + blue + 15 * 3 + 15 * 3 + 26 * 3;
    //         var height_right = $('.right-box-content').height();
    //         var margin_max = height_left - height_right;
    //         if (margin_top <= (margin_max - 20)) {
    //             $('.center-box').animate({'margin-top': margin_top + 20}, 30);
    //             $('.right-box').animate({'margin-top': margin_top + 20}, 30);
    //         } else {
    //             $('.center-box').animate({'margin-top': margin_max}, 5);
    //             $('.right-box').animate({'margin-top': margin_max}, 5);
    //         }
    //     } else {
    //         // otherwise, set margin to 0
    //         $('.center-box').animate({'margin-top': 0}, 30);
    //         $('.right-box').animate({'margin-top': 0}, 30);
    //     }
    // });

    function handleScroll() {
        if (window.innerWidth > 768) {
            var viewportOffset = $('.content-box')[0].getBoundingClientRect();
            if (viewportOffset.top < 0) {
                var margin_top = -1 * viewportOffset.top;
                var gray = 0;
                $('.gray-left-box-content').each(function () {
                    gray += $(this).outerHeight(true);
                });

                var green = 0;
                $('.green-left-box-content').each(function () {
                    green += $(this).outerHeight(true);
                });

                var blue = 0;
                $('.blue-left-box-content').each(function () {
                    blue += $(this).outerHeight(true);
                });
                var height_left = gray + green + blue + 15 * 3 + 15 * 3 + 26 * 3;
                // console.log("height left:" + height_left)
                var height_right = $('.right-box-content').height();
                // console.log("height right:" + height_right)
                var margin_max = height_left - height_right;
                if (margin_top <= (margin_max - 20)) {
                    $('.center-box, .right-box').stop().animate({'margin-top': margin_top + 20}, 30);
                } else {
                    $('.center-box, .right-box').stop().animate({'margin-top': margin_max}, 5);
                }
            } else {
                $('.center-box, .right-box').stop().animate({'margin-top': 0}, 30);
            }
        } else {
            $('.center-box, .right-box').stop().animate({'margin-top': 0}, 30);
        }
    }

    $(window).on("scroll", handleScroll);

    $(window).on("resize", function () {
        handleScroll();
    });


    $(document).on('click', ".submit_ticket_form", function () {
        $('.event_container > .e_ajax_submit').submit();
    });
});
