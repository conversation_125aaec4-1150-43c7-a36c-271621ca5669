!function(){"ace"in window||(window.ace={}),"helper"in window.ace||(window.ace.helper={}),"vars"in window.ace||(window.ace.vars={}),window.ace.vars.icon=" ace-icon ",window.ace.vars[".icon"]=".ace-icon",ace.vars.touch="ontouchstart"in window;var e=navigator.userAgent;ace.vars.webkit=!!e.match(/AppleWebKit/i),ace.vars.safari=!!e.match(/Safari/i)&&!e.match(/Chrome/i),ace.vars.android=ace.vars.safari&&!!e.match(/Android/i),ace.vars.ios_safari=!!e.match(/OS ([4-9])(_\d)+ like Mac OS X/i)&&!e.match(/CriOS/i),ace.vars.ie=window.navigator.msPointerEnabled||document.all&&document.querySelector,ace.vars.old_ie=document.all&&!document.addEventListener,ace.vars.very_old_ie=document.all&&!document.querySelector,ace.vars.firefox="MozAppearance"in document.documentElement.style,ace.vars.non_auto_fixed=ace.vars.android||ace.vars.ios_safari}(),function(e){ace.click_event=ace.vars.touch&&e.fn.tap?"tap":"click"}(jQuery),jQuery(function(e){function t(){ace.vars.non_auto_fixed&&e("body").addClass("mob-safari"),ace.vars.transition=!!e.support.transition.end}function a(){var t=e(".sidebar");e.fn.ace_sidebar&&t.ace_sidebar(),e.fn.ace_sidebar_scroll&&t.ace_sidebar_scroll({include_toggle:!1||ace.vars.safari||ace.vars.ios_safari}),e.fn.ace_sidebar_hover&&t.ace_sidebar_hover({sub_hover_delay:750,sub_scroll_style:"no-track scroll-thin scroll-margin scroll-visible"})}function i(){if(e.fn.ace_ajax){window.Pace&&(window.paceOptions={ajax:!0,document:!0,eventLag:!1});var t={close_active:!0,default_url:"page/index",content_url:function(e){if(!e.match(/^page\//))return!1;var t=document.location.pathname;return t.match(/(\/ajax\/)(index\.html)?/)?t.replace(/(\/ajax\/)(index\.html)?/,"/ajax/content/"+e.replace(/^page\//,"")+".html"):t+"?"+e.replace(/\//,"=")}};window.Pace&&(t.loading_overlay="body"),e("[data-ajax-content=true]").ace_ajax(t),e(window).on("error.ace_ajax",function(){e("[data-ajax-content=true]").each(function(){var t=e(this);t.ace_ajax("working")&&(window.Pace&&Pace.running&&Pace.stop(),t.ace_ajax("stopLoading",!0))})})}}function s(){var t=!!e.fn.ace_scroll;t&&e(".dropdown-content").ace_scroll({reset:!1,mouseWheelLock:!0}),t&&!ace.vars.old_ie&&(e(window).on("resize.reset_scroll",function(){e(".ace-scroll:not(.scroll-disabled)").not(":hidden").ace_scroll("reset")}),t&&e(document).on("settings.ace.reset_scroll",function(t,a){"sidebar_collapsed"==a&&e(".ace-scroll:not(.scroll-disabled)").not(":hidden").ace_scroll("reset")}))}function n(){e(document).on("click.dropdown.pos",'.dropdown-toggle[data-position="auto"]',function(){var t=e(this).offset(),a=e(this.parentNode);parseInt(t.top+e(this).height())+50>ace.helper.scrollTop()+ace.helper.winHeight()-a.find(".dropdown-menu").eq(0).height()?a.addClass("dropup"):a.removeClass("dropup")})}function r(){e('.ace-nav [class*="icon-animated-"]').closest("a").one("click",function(){var t=e(this).find('[class*="icon-animated-"]').eq(0),a=t.attr("class").match(/icon\-animated\-([\d\w]+)/);t.removeClass(a[0])}),e(document).on("click",".dropdown-navbar .nav-tabs",function(t){t.stopPropagation();{var a;t.target}(a=e(t.target).closest("[data-toggle=tab]"))&&a.length>0&&(a.tab("show"),t.preventDefault(),e(window).triggerHandler("resize.navbar.dropdown"))})}function o(){e(".sidebar .nav-list .badge[title],.sidebar .nav-list .badge[title]").each(function(){var t=e(this).attr("class").match(/tooltip\-(?:\w+)/);t=t?t[0]:"tooltip-error",e(this).tooltip({placement:function(t,a){var i=e(a).offset();return parseInt(i.left)<parseInt(document.body.scrollWidth/2)?"right":"left"},container:"body",template:'<div class="tooltip '+t+'"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>'})})}function l(){var t=e(".btn-scroll-up");if(t.length>0){var a=!1;e(window).on("scroll.scroll_btn",function(){var e=ace.helper.scrollTop(),i=ace.helper.winHeight(),s=document.body.scrollHeight;e>parseInt(i/4)||e>0&&s>=i&&i+e>=s-1?a||(t.addClass("display"),a=!0):a&&(t.removeClass("display"),a=!1)}).triggerHandler("scroll.scroll_btn"),t.on(ace.click_event,function(){var t=Math.min(500,Math.max(100,parseInt(ace.helper.scrollTop()/3)));return e("html,body").animate({scrollTop:0},t),!1})}}function c(){if(ace.vars.webkit){var t=e(".ace-nav").get(0);t&&e(window).on("resize.webkit_fix",function(){ace.helper.redraw(t)})}ace.vars.ios_safari&&e(document).on("ace.settings.ios_fix",function(t,a,i){"navbar_fixed"==a&&(e(document).off("focus.ios_fix blur.ios_fix","input,textarea,.wysiwyg-editor"),1==i&&e(document).on("focus.ios_fix","input,textarea,.wysiwyg-editor",function(){e(window).on("scroll.ios_fix",function(){var t=e("#navbar").get(0);t&&ace.helper.redraw(t)})}).on("blur.ios_fix","input,textarea,.wysiwyg-editor",function(){e(window).off("scroll.ios_fix")}))}).triggerHandler("ace.settings.ios_fix",["navbar_fixed","fixed"==e("#navbar").css("position")])}function d(){e(document).on("hide.bs.collapse show.bs.collapse",function(t){var a=t.target.getAttribute("id"),i=e('a[href*="#'+a+'"]');0==i.length&&(i=e('a[data-target*="#'+a+'"]')),0!=i.length&&i.find(ace.vars[".icon"]).each(function(){var a,i=e(this),s=null,n=null;return(s=i.attr("data-icon-show"))?n=i.attr("data-icon-hide"):(a=i.attr("class").match(/fa\-(.*)\-(up|down)/))&&(s="fa-"+a[1]+"-down",n="fa-"+a[1]+"-up"),s?("show"==t.type?i.removeClass(s).addClass(n):i.removeClass(n).addClass(s),!1):void 0})})}function h(){function t(){var t=e(this).find("> .dropdown-menu");if("fixed"==t.css("position")){var i=parseInt(e(window).width()),s=i>320?60:i>240?40:30,n=parseInt(i)-s,r=parseInt(e(window).height())-30,o=parseInt(Math.min(n,320));t.css("width",o);var l=!1,c=0,d=t.find(".tab-pane.active .dropdown-content.ace-scroll");0==d.length?d=t.find(".dropdown-content.ace-scroll"):l=!0;var h=d.closest(".dropdown-menu"),u=t[0].scrollHeight;if(1==d.length){var f=d.find(".scroll-content")[0];f&&(u=f.scrollHeight),c+=h.find(".dropdown-header").outerHeight(),c+=h.find(".dropdown-footer").outerHeight();var p=h.closest(".tab-content");0!=p.length&&(c+=p.siblings(".nav-tabs").eq(0).height())}var v=parseInt(Math.min(r,480,u+c)),g=parseInt(Math.abs((n+s-o)/2)),b=parseInt(Math.abs((r+30-v)/2)),m=parseInt(t.css("z-index"))||0;if(t.css({height:v,left:g,right:"auto",top:b-(l?3:1)}),1==d.length&&(ace.vars.touch?d.ace_scroll("disable").css("max-height",v-c).addClass("overflow-scroll"):d.ace_scroll("update",{size:v-c}).ace_scroll("enable").ace_scroll("reset")),t.css("height",v+(l?7:2)),t.hasClass("user-menu")){t.css("height","");var _=e(this).find(".user-info");_.css(1==_.length&&"fixed"==_.css("position")?{left:g,right:"auto",top:b,width:o-2,"max-width":o-2,"z-index":m+1}:{left:"",right:"",top:"",width:"","max-width":"","z-index":""})}e(this).closest(".navbar.navbar-fixed-top").css("z-index",m)}else 0!=t.length&&a.call(this,t);var w=this;e(window).off("resize.navbar.dropdown").one("resize.navbar.dropdown",function(){e(w).triggerHandler("shown.bs.dropdown.navbar")})}function a(t){if(t=t||e(this).find("> .dropdown-menu"),t.length>0&&(t.css({width:"",height:"",left:"",right:"",top:""}).find(".dropdown-content").each(function(){ace.vars.touch&&e(this).css("max-height","").removeClass("overflow-scroll");var t=parseInt(e(this).attr("data-size")||0)||e.fn.ace_scroll.defaults.size;e(this).ace_scroll("update",{size:t}).ace_scroll("enable").ace_scroll("reset")}),t.hasClass("user-menu"))){e(this).find(".user-info").css({left:"",right:"",top:"",width:"","max-width":"","z-index":""})}e(this).closest(".navbar").css("z-index","")}ace.vars.old_ie||e(".ace-nav > li").on("shown.bs.dropdown.navbar",function(){t.call(this)}).on("hidden.bs.dropdown.navbar",function(){e(window).off("resize.navbar.dropdown"),a.call(this)})}t(),a(),i(),s(),n(),r(),o(),l(),c(),d(),h()}),function(e){e.unCamelCase=function(e){return e.replace(/([a-z])([A-Z])/g,function(e,t,a){return t+"-"+a.toLowerCase()})},e.strToVal=function(e){var t=e.match(/^(?:(true)|(false)|(null)|(\-?[\d]+(?:\.[\d]+)?)|(\[.*\]|\{.*\}))$/i),a=e;if(t)if(t[1])a=!0;else if(t[2])a=!1;else if(t[3])a=null;else if(t[4])a=parseFloat(e);else if(t[5])try{a=JSON.parse(e)}catch(i){}return a},e.getAttrSettings=function(t,a,i){var s=a instanceof Array?1:2,i=i?i.replace(/([^\-])$/,"$1-"):"";i="data-"+i;var n={};for(var r in a)if(a.hasOwnProperty(r)){var o,l=1==s?a[r]:r,c=e.unCamelCase(l.replace(/[^A-Za-z0-9]{1,}/g,"-")).toLowerCase();if(!(o=t.getAttribute(i+c)))continue;n[l]=e.strToVal(o)}return n},e.scrollTop=function(){return document.scrollTop||document.documentElement.scrollTop||document.body.scrollTop},e.winHeight=function(){return window.innerHeight||document.documentElement.clientHeight},e.redraw=function(e,t){var a=e.style.display;e.style.display="none",e.offsetHeight,t!==!0?e.style.display=a:setTimeout(function(){e.style.display=a},10)}}(ace.helper),function(e,t){function a(t,a){function s(t){var a="",i=e(".breadcrumb");if(i.length>0&&i.is(":visible")){i.find("> li:not(:first-child)").remove();var s=0;t.parents(".nav li").each(function(){var t=e(this).find("> a"),n=t.clone();n.find("i,.fa,.glyphicon,.ace-icon,.menu-icon,.badge,.label").remove();var r=n.text();n.remove();var o=t.attr("href");if(0==s){var l=e('<li class="active"></li>').appendTo(i);l.text(r),a=r}else{var l=e("<li><a /></li>").insertAfter(i.find("> li:first-child"));l.find("a").attr("href",o).text(r)}s++})}return a}function n(t){var a=r.find(".ajax-append-title");if(a.length>0)document.title=a.text(),a.remove();else if(t.length>0){var i=e.trim(String(document.title).replace(/^(.*)[\-]/,""));i&&(i=" - "+i),t=e.trim(t)+i}}var r=e(t),o=this;r.attr("data-ajax-content","true");var l=ace.helper.getAttrSettings(t,e.fn.ace_ajax.defaults);this.settings=e.extend({},e.fn.ace_ajax.defaults,a,l);var c=!1,d=e();this.force_reload=!1,this.loadUrl=function(e,t){var a=!1;e=e.replace(/^(\#\!)?\#/,""),this.force_reload=t===!1,"function"==typeof this.settings.content_url&&(a=this.settings.content_url(e)),"string"==typeof a&&this.getUrl(a,e,!1)},this.loadAddr=function(e,t,a){this.force_reload=a===!1,this.getUrl(e,t,!1)},this.getUrl=function(t,a,i){if(!c){var l;r.trigger(l=e.Event("ajaxloadstart"),{url:t,hash:a}),l.isDefaultPrevented()||(o.startLoading(),e.ajax({url:t,cache:!this.force_reload}).error(function(){r.trigger("ajaxloaderror",{url:t,hash:a}),o.stopLoading(!0)}).done(function(l){r.trigger("ajaxloaddone",{url:t,hash:a});var c=null,h="";if("function"==typeof o.settings.update_active)c=o.settings.update_active.call(null,a,t);else if(o.settings.update_active===!0&&a&&(c=e('a[data-url="'+a+'"]'),c.length>0)){var u=c.closest(".nav");if(u.length>0){u.find(".active").each(function(){var t="active";(e(this).hasClass("hover")||o.settings.close_active)&&(t+=" open"),e(this).removeClass(t),o.settings.close_active&&e(this).find(" > .submenu").css("display","")});{c.closest("li").addClass("active").parents(".nav li").addClass("active open")}u.closest(".sidebar[data-sidebar-scroll=true]").each(function(){var t=e(this);t.ace_sidebar_scroll("reset"),i&&t.ace_sidebar_scroll("scroll_to_active")})}}"function"==typeof o.settings.update_breadcrumbs?h=o.settings.update_breadcrumbs.call(null,a,t,c):o.settings.update_breadcrumbs===!0&&null!=c&&c.length>0&&(h=s(c)),l=String(l).replace(/<(title|link)([\s\>])/gi,'<div class="hidden ajax-append-$1"$2').replace(/<\/(title|link)\>/gi,"</div>"),d.addClass("content-loaded").detach(),r.empty().html(l),e(o.settings.loading_overlay||r).append(d),setTimeout(function(){e("head").find("link.ace-ajax-stylesheet").remove();for(var t=["link.ace-main-stylesheet","link#main-ace-style",'link[href*="/ace.min.css"]','link[href*="/ace.css"]'],a=[],i=0;i<t.length&&(a=e("head").find(t[i]).first(),!(a.length>0));i++);r.find(".ajax-append-link").each(function(){var t=e(this);if(t.attr("href")){var i=jQuery("<link />",{type:"text/css",rel:"stylesheet","class":"ace-ajax-stylesheet"});a.length>0?i.insertBefore(a):i.appendTo("head"),i.attr("href",t.attr("href"))}t.remove()})},10),"function"==typeof o.settings.update_title?o.settings.update_title.call(null,a,t,h):o.settings.update_title===!0&&n(h),i||e("html,body").animate({scrollTop:0},250),r.trigger("ajaxloadcomplete",{url:t,hash:a}),o.stopLoading()}))}};var h=!1,u=null;this.startLoading=function(){c||(c=!0,this.settings.loading_overlay||"static"!=r.css("position")||(r.css("position","relative"),h=!0),d.remove(),d=e('<div class="ajax-loading-overlay"><i class="ajax-loading-icon '+(this.settings.loading_icon||"")+'"></i> '+this.settings.loading_text+"</div>"),"body"==this.settings.loading_overlay?e("body").append(d.addClass("ajax-overlay-body")):this.settings.loading_overlay?e(this.settings.loading_overlay).append(d):r.append(d),this.settings.max_load_wait!==!1&&(u=setTimeout(function(){if(u=null,c){var t;r.trigger(t=e.Event("ajaxloadlong")),t.isDefaultPrevented()||o.stopLoading(!0)}},1e3*this.settings.max_load_wait)))},this.stopLoading=function(e){e===!0?(c=!1,d.remove(),h&&(r.css("position",""),h=!1),null!=u&&(clearTimeout(u),u=null)):(d.addClass("almost-loaded"),r.one("ajaxscriptsloaded.inner_call",function(){o.stopLoading(!0)}))},this.working=function(){return c},this.loadScripts=function(t,a){e.ajaxPrefilter("script",function(e){e.cache=!0}),setTimeout(function(){function s(){"function"==typeof a&&a(),e('.btn-group[data-toggle="buttons"] > .btn').button(),r.trigger("ajaxscriptsloaded")}function n(e){e+=1,e<t.length?o(e):s()}function o(a){if(a=a||0,!t[a])return n(a);var r="js-"+t[a].replace(/[^\w\d\-]/g,"-").replace(/\-\-/g,"-");i[r]!==!0?e.getScript(t[a]).done(function(){i[r]=!0}).complete(function(){d++,d>=l&&c?s():n(a)}):n(a)}for(var l=0,d=0,h=0;h<t.length;h++)t[h]&&!function(){var e="js-"+t[h].replace(/[^\w\d\-]/g,"-").replace(/\-\-/g,"-");i[e]!==!0&&l++}();l>0?o():s()},10)},e(window).off("hashchange.ace_ajax").on("hashchange.ace_ajax",function(){var t=e.trim(window.location.hash);t&&0!=t.length&&o.loadUrl(t)}).trigger("hashchange.ace_ajax",[!0]);var f=e.trim(window.location.hash);!f&&this.settings.default_url&&(window.location.hash=this.settings.default_url)}var i={};e.fn.aceAjax=e.fn.ace_ajax=function(i,s,n,r){var o,l=this.each(function(){var l=e(this),c=l.data("ace_ajax"),d="object"==typeof i&&i;c||l.data("ace_ajax",c=new a(this,d)),"string"==typeof i&&"function"==typeof c[i]&&(o=r!=t?c[i](s,n,r):n!=t?c[i](s,n):c[i](s))});return o===t?l:o},e.fn.aceAjax.defaults=e.fn.ace_ajax.defaults={content_url:!1,default_url:!1,loading_icon:"fa fa-spin fa-spinner fa-2x orange",loading_text:"",loading_overlay:null,update_breadcrumbs:!0,update_title:!0,update_active:!0,close_active:!1,max_load_wait:!1}}(window.jQuery),function(e,t){if(ace.vars.touch){var a="touchstart MSPointerDown pointerdown",i="touchend touchcancel MSPointerUp MSPointerCancel pointerup pointercancel",s="touchmove MSPointerMove MSPointerHover pointermove";e.event.special.ace_drag={setup:function(){var n=0,r=e(this);r.on(a,function(a){function o(e){if(d){var t=e.originalEvent.touches?e.originalEvent.touches[0]:e;if(l={coords:[t.pageX,t.pageY]},d&&l&&(u=0,f=0,h=Math.abs(f=d.coords[1]-l.coords[1])>n&&Math.abs(u=d.coords[0]-l.coords[0])<=Math.abs(f)?f>0?"up":"down":Math.abs(u=d.coords[0]-l.coords[0])>n&&Math.abs(f)<=Math.abs(u)?u>0?"left":"right":!1,h!==!1)){var a={cancel:!1};d.origin.trigger({type:"ace_drag",direction:h,dx:u,dy:f,retval:a}),0==a.cancel&&e.preventDefault()}d.coords[0]=l.coords[0],d.coords[1]=l.coords[1]}}var l,c=a.originalEvent.touches?a.originalEvent.touches[0]:a,d={coords:[c.pageX,c.pageY],origin:e(a.target)},h=!1,u=0,f=0;r.on(s,o).one(i,function(){r.off(s,o),d=l=t})})}}}}(window.jQuery),function(e,t){function a(a,s){function n(){this.mobile_view=this.mobile_style<4&&this.is_mobile_view(),this.collapsible=!this.mobile_view&&this.is_collapsible(),this.minimized=!this.collapsible&&this.$sidebar.hasClass(h)||3==this.mobile_style&&this.mobile_view&&this.$sidebar.hasClass(u),this.horizontal=!(this.mobile_view||this.collapsible)&&this.$sidebar.hasClass(f)}var r=this;this.$sidebar=e(a),this.$sidebar.attr("data-sidebar","true"),this.$sidebar.attr("id")||this.$sidebar.attr("id","id-sidebar-"+ ++i);var o=ace.helper.getAttrSettings(a,e.fn.ace_sidebar.defaults,"sidebar-");this.settings=e.extend({},e.fn.ace_sidebar.defaults,s,o),this.minimized=!1,this.collapsible=!1,this.horizontal=!1,this.mobile_view=!1,this.vars=function(){return{minimized:this.minimized,collapsible:this.collapsible,horizontal:this.horizontal,mobile_view:this.mobile_view}},this.get=function(e){return this.hasOwnProperty(e)?this[e]:void 0},this.set=function(e,t){this.hasOwnProperty(e)&&(this[e]=t)},this.ref=function(){return this};var l=function(a){var i,s,n=e(this).find(ace.vars[".icon"]);n.length>0&&(i=n.attr("data-icon1"),s=n.attr("data-icon2"),a!==t?a?n.removeClass(i).addClass(s):n.removeClass(s).addClass(i):n.toggleClass(i).toggleClass(s))},c=function(){var t=r.$sidebar.find(".sidebar-collapse");return 0==t.length&&(t=e('.sidebar-collapse[data-target="#'+(r.$sidebar.attr("id")||"")+'"]')),t=0!=t.length?t[0]:null};this.toggleMenu=function(e,t){if(!this.collapsible){this.minimized=!this.minimized;try{ace.settings.sidebar_collapsed(a,this.minimized,!(e===!1||t===!1))}catch(i){this.minimized?this.$sidebar.addClass("menu-min"):this.$sidebar.removeClass("menu-min")}e||(e=c()),e&&l.call(e,this.minimized),ace.vars.old_ie&&ace.helper.redraw(a)}},this.collapse=function(e,t){this.collapsible||(this.minimized=!1,this.toggleMenu(e,t))},this.expand=function(e,t){this.collapsible||(this.minimized=!0,this.toggleMenu(e,t))},this.toggleResponsive=function(t){if(this.mobile_view&&3==this.mobile_style){if(this.$sidebar.hasClass("menu-min")){this.$sidebar.removeClass("menu-min");var a=c();a&&l.call(a)}if(this.minimized=!this.$sidebar.hasClass("responsive-min"),this.$sidebar.toggleClass("responsive-min responsive-max"),t||(t=this.$sidebar.find(".sidebar-expand"),0==t.length&&(t=e('.sidebar-expand[data-target="#'+(this.$sidebar.attr("id")||"")+'"]')),t=0!=t.length?t[0]:null),t){var i,s,n=e(t).find(ace.vars[".icon"]);n.length>0&&(i=n.attr("data-icon1"),s=n.attr("data-icon2"),n.toggleClass(i).toggleClass(s))}e(document).triggerHandler("settings.ace",["sidebar_collapsed",this.minimized])}},this.is_collapsible=function(){var t;return this.$sidebar.hasClass("navbar-collapse")&&null!=(t=e('.navbar-toggle[data-target="#'+(this.$sidebar.attr("id")||"")+'"]').get(0))&&t.scrollHeight>0},this.is_mobile_view=function(){var t;return null!=(t=e('.menu-toggler[data-target="#'+(this.$sidebar.attr("id")||"")+'"]').get(0))&&t.scrollHeight>0},this.$sidebar.on(ace.click_event+".ace.submenu",".nav-list",function(t){var a=this,i=e(t.target).closest("a");if(i&&0!=i.length){var s=r.minimized&&!r.collapsible;if(i.hasClass("dropdown-toggle")){t.preventDefault();var n=i.siblings(".submenu").get(0);if(!n)return!1;var o=e(n),l=0,c=n.parentNode.parentNode;if(s&&c==a||o.parent().hasClass("hover")&&"absolute"==o.css("position")&&!r.collapsible)return!1;var d=0==n.scrollHeight;return d&&e(c).find("> .open > .submenu").each(function(){this==n||e(this.parentNode).hasClass("active")||(l-=this.scrollHeight,r.hide(this,r.settings.duration,!1))}),d?(r.show(n,r.settings.duration),0!=l&&(l+=n.scrollHeight)):(r.hide(n,r.settings.duration),l-=n.scrollHeight),0!=l&&("true"!=r.$sidebar.attr("data-sidebar-scroll")||r.minimized||r.$sidebar.ace_sidebar_scroll("prehide",l)),!1}if("tap"==ace.click_event&&s&&i.get(0).parentNode.parentNode==a){var h=i.find(".menu-text").get(0);if(null!=h&&t.target!=h&&!e.contains(h,t.target))return t.preventDefault(),!1}if(ace.vars.ios_safari&&"false"!==i.attr("data-link"))return document.location=i.attr("href"),t.preventDefault(),!1}});var d=!1;this.show=function(t,a,i){if(i=i!==!1,i&&d)return!1;var s,n=e(t);if(n.trigger(s=e.Event("show.ace.submenu")),s.isDefaultPrevented())return!1;i&&(d=!0),a=a||this.settings.duration,n.css({height:0,overflow:"hidden",display:"block"}).removeClass("nav-hide").addClass("nav-show").parent().addClass("open"),t.scrollTop=0,a>0&&n.css({height:t.scrollHeight,"transition-property":"height","transition-duration":a/1e3+"s"});var r=function(t,a){t&&t.stopPropagation(),n.css({"transition-property":"","transition-duration":"",overflow:"",height:""}),a!==!1&&n.trigger(e.Event("shown.ace.submenu")),i&&(d=!1)};return a>0&&e.support.transition.end?n.one(e.support.transition.end,r):r(),ace.vars.android&&setTimeout(function(){r(null,!1),ace.helper.redraw(t)},a+20),!0},this.hide=function(t,a,i){if(i=i!==!1,i&&d)return!1;var s,n=e(t);if(n.trigger(s=e.Event("hide.ace.submenu")),s.isDefaultPrevented())return!1;i&&(d=!0),a=a||this.settings.duration,n.css({height:t.scrollHeight,overflow:"hidden",display:"block"}).parent().removeClass("open"),t.offsetHeight,a>0&&n.css({height:0,"transition-property":"height","transition-duration":a/1e3+"s"});var r=function(t,a){t&&t.stopPropagation(),n.css({display:"none",overflow:"",height:"","transition-property":"","transition-duration":""}).removeClass("nav-show").addClass("nav-hide"),a!==!1&&n.trigger(e.Event("hidden.ace.submenu")),i&&(d=!1)};return a>0&&e.support.transition.end?n.one(e.support.transition.end,r):r(),ace.vars.android&&setTimeout(function(){r(null,!1),ace.helper.redraw(t)},a+20),!0},this.toggle=function(e,t){if(t=t||r.settings.duration,0==e.scrollHeight){if(this.show(e,t))return 1}else if(this.hide(e,t))return-1;return 0};var h="menu-min",u="responsive-min",f="h-sidebar",p=function(){this.mobile_style=1,this.$sidebar.hasClass("responsive")&&!e('.menu-toggler[data-target="#'+this.$sidebar.attr("id")+'"]').hasClass("navbar-toggle")?this.mobile_style=2:this.$sidebar.hasClass(u)?this.mobile_style=3:this.$sidebar.hasClass("navbar-collapse")&&(this.mobile_style=4)};p.call(r),e(window).on("resize.sidebar.vars",function(){n.call(r)}).triggerHandler("resize.sidebar.vars")}var i=0;e(document).on(ace.click_event+".ace.menu",".menu-toggler",function(t){var a=e(this),i=e(a.attr("data-target"));if(0!=i.length){t.preventDefault(),i.toggleClass("display"),a.toggleClass("display");var s=ace.click_event+".ace.autohide",n="true"===i.attr("data-auto-hide");return a.hasClass("display")?(n&&e(document).on(s,function(t){return i.get(0)==t.target||e.contains(i.get(0),t.target)?void t.stopPropagation():(i.removeClass("display"),a.removeClass("display"),void e(document).off(s))}),"true"==i.attr("data-sidebar-scroll")&&i.ace_sidebar_scroll("reset")):n&&e(document).off(s),!1}}).on(ace.click_event+".ace.menu",".sidebar-collapse",function(t){var a=e(this).attr("data-target"),i=null;a&&(i=e(a)),(null==i||0==i.length)&&(i=e(this).closest(".sidebar")),0!=i.length&&(t.preventDefault(),i.ace_sidebar("toggleMenu",this))}).on(ace.click_event+".ace.menu",".sidebar-expand",function(t){var a=e(this).attr("data-target"),i=null;if(a&&(i=e(a)),(null==i||0==i.length)&&(i=e(this).closest(".sidebar")),0!=i.length){var s=this;t.preventDefault(),i.ace_sidebar("toggleResponsive",this);var n=ace.click_event+".ace.autohide";"true"===i.attr("data-auto-hide")&&(i.hasClass("responsive-max")?e(document).on(n,function(t){return i.get(0)==t.target||e.contains(i.get(0),t.target)?void t.stopPropagation():(i.ace_sidebar("toggleResponsive",s),void e(document).off(n))}):e(document).off(n))}}),e.fn.ace_sidebar=function(i,s){var n,r=this.each(function(){var t=e(this),r=t.data("ace_sidebar"),o="object"==typeof i&&i;r||t.data("ace_sidebar",r=new a(this,o)),"string"==typeof i&&"function"==typeof r[i]&&(n=s instanceof Array?r[i].apply(r,s):r[i](s))});return n===t?r:n},e.fn.ace_sidebar.defaults={duration:300}}(window.jQuery),function(e,t){function a(t,a){var n=this,r=e(window),o=e(t),l=o.find(".nav-list"),c=o.find(".sidebar-toggle").eq(0),d=o.find(".sidebar-shortcuts").eq(0),h=l.get(0);if(h){var u=ace.helper.getAttrSettings(t,e.fn.ace_sidebar_scroll.defaults);this.settings=e.extend({},e.fn.ace_sidebar_scroll.defaults,a,u);var f=n.settings.scroll_to_active,p=o.ace_sidebar("ref");o.attr("data-sidebar-scroll","true");var v=null,g=null,b=null,m=null,_=null,w=null;this.is_scrolling=!1;var y=!1;this.sidebar_fixed=s(t,"fixed");var x,C,k=function(){var e=l.parent().offset();return n.sidebar_fixed&&(e.top-=ace.helper.scrollTop()),r.innerHeight()-e.top-(n.settings.include_toggle?0:c.outerHeight())},$=function(){return h.clientHeight},z=function(t){if(!y&&n.sidebar_fixed){l.wrap('<div class="nav-wrap-up pos-rel" />'),l.after("<div><div></div></div>"),l.wrap('<div class="nav-wrap" />'),n.settings.include_toggle||c.css({"z-index":1}),n.settings.include_shortcuts||d.css({"z-index":99}),v=l.parent().next().ace_scroll({size:k(),mouseWheelLock:!0,hoverReset:!1,dragEvent:!0,styleClass:n.settings.scroll_style,touchDrag:!1}).closest(".ace-scroll").addClass("nav-scroll"),w=v.data("ace_scroll"),g=v.find(".scroll-content").eq(0),b=g.find(" > div").eq(0),_=e(w.get_track()),m=_.find(".scroll-bar").eq(0),n.settings.include_shortcuts&&0!=d.length&&(l.parent().prepend(d).wrapInner("<div />"),l=l.parent()),n.settings.include_toggle&&0!=c.length&&(l.append(c),l.closest(".nav-wrap").addClass("nav-wrap-t")),l.css({position:"relative"}),1==n.settings.scroll_outside&&v.addClass("scrollout"),h=l.get(0),h.style.top=0,g.on("scroll.nav",function(){h.style.top=-1*this.scrollTop+"px"}),l.on(e.event.special.mousewheel?"mousewheel.ace_scroll":"mousewheel.ace_scroll DOMMouseScroll.ace_scroll",function(e){return n.is_scrolling&&w.is_active()?v.trigger(e):!n.settings.lock_anyway}),l.on("mouseenter.ace_scroll",function(){_.addClass("scroll-hover")}).on("mouseleave.ace_scroll",function(){_.removeClass("scroll-hover")});var a=g.get(0);if(l.on("ace_drag.nav",function(t){if(!n.is_scrolling||!w.is_active())return void(t.retval.cancel=!0);if(0!=e(t.target).closest(".can-scroll").length)return void(t.retval.cancel=!0);if("up"==t.direction||"down"==t.direction){w.move_bar(!0);var i=t.dy;i=parseInt(Math.min(x,i)),Math.abs(i)>2&&(i=2*i),0!=i&&(a.scrollTop=a.scrollTop+i,h.style.top=-1*a.scrollTop+"px")}}),n.settings.smooth_scroll&&l.on("touchstart.nav MSPointerDown.nav pointerdown.nav",function(){l.css("transition-property","none"),m.css("transition-property","none")}).on("touchend.nav touchcancel.nav MSPointerUp.nav MSPointerCancel.nav pointerup.nav pointercancel.nav",function(){l.css("transition-property","top"),m.css("transition-property","top")}),i&&!n.settings.include_toggle){var s=c.get(0);s&&g.on("scroll.safari",function(){ace.helper.redraw(s)})}if(y=!0,1==t&&(n.reset(),f&&n.scroll_to_active(),f=!1),"number"==typeof n.settings.smooth_scroll&&n.settings.smooth_scroll>0&&(l.css({"transition-property":"top","transition-duration":(n.settings.smooth_scroll/1e3).toFixed(2)+"s"}),m.css({"transition-property":"top","transition-duration":(n.settings.smooth_scroll/1500).toFixed(2)+"s"}),v.on("drag.start",function(e){e.stopPropagation(),l.css("transition-property","none")}).on("drag.end",function(e){e.stopPropagation(),l.css("transition-property","top")})),ace.vars.android){var r=ace.helper.scrollTop();2>r&&(window.scrollTo(r,0),setTimeout(function(){n.reset()},20));var o,u=ace.helper.winHeight();e(window).on("scroll.ace_scroll",function(){n.is_scrolling&&w.is_active()&&(o=ace.helper.winHeight(),o!=u&&(u=o,n.reset()))})}}};this.scroll_to_active=function(){if(w&&w.is_active())try{var e,t=p.vars(),a=o.find(".nav-list");t.minimized&&!t.collapsible?e=a.find("> .active"):(e=l.find("> .active.hover"),0==e.length&&(e=l.find(".active:not(.open)")));var i=e.outerHeight();a=a.get(0);for(var s=e.get(0);s!=a;)i+=s.offsetTop,s=s.parentNode;var n=i-v.height();n>0&&(h.style.top=-n+"px",g.scrollTop(n))}catch(r){}},this.reset=function(e){if(e===!0&&(this.sidebar_fixed=s(t,"fixed")),!this.sidebar_fixed)return void this.disable();y||z();var a=p.vars(),i=!a.collapsible&&!a.horizontal&&(x=k())<(C=h.clientHeight);this.is_scrolling=!0,i&&(b.css({height:C,width:8}),v.prev().css({"max-height":x}),w.update({size:x}),w.enable(),w.reset()),i&&w.is_active()?o.addClass("sidebar-scroll"):this.is_scrolling&&this.disable()},this.disable=function(){this.is_scrolling=!1,v&&(v.css({height:"","max-height":""}),b.css({height:"",width:""}),v.prev().css({"max-height":""}),w.disable()),parseInt(h.style.top)<0&&n.settings.smooth_scroll&&e.support.transition.end?l.one(e.support.transition.end,function(){o.removeClass("sidebar-scroll"),l.off(".trans")}):o.removeClass("sidebar-scroll"),h.style.top=0},this.prehide=function(e){if(this.is_scrolling&&!p.get("minimized"))if($()+e<k())this.disable();else if(0>e){var t=g.scrollTop()+e;if(0>t)return;h.style.top=-1*t+"px"}},this._reset=function(e){e===!0&&(this.sidebar_fixed=s(t,"fixed")),ace.vars.webkit?setTimeout(function(){n.reset()},0):this.reset()},this.set_hover=function(){_&&_.addClass("scroll-hover")},this.get=function(e){return this.hasOwnProperty(e)?this[e]:void 0},this.set=function(e,t){this.hasOwnProperty(e)&&(this[e]=t)},this.ref=function(){return this},this.updateStyle=function(e){null!=w&&w.update({styleClass:e})},o.on("hidden.ace.submenu.sidebar_scroll shown.ace.submenu.sidebar_scroll",".submenu",function(e){e.stopPropagation(),p.get("minimized")||(n._reset(),"shown"==e.type&&n.set_hover())}),z(!0)}}var i=ace.vars.safari&&navigator.userAgent.match(/version\/[1-5]/i),s="getComputedStyle"in window?function(e,t){return e.offsetHeight,window.getComputedStyle(e).position==t}:function(t,a){return t.offsetHeight,e(t).css("position")==a};e(document).on("settings.ace.sidebar_scroll",function(t,a){e(".sidebar[data-sidebar-scroll=true]").each(function(){var t=e(this),i=t.ace_sidebar_scroll("ref");if("sidebar_collapsed"==a&&s(this,"fixed"))"true"==t.attr("data-sidebar-hover")&&t.ace_sidebar_hover("reset"),i._reset();else if("sidebar_fixed"===a||"navbar_fixed"===a){var n=i.get("is_scrolling"),r=s(this,"fixed");i.set("sidebar_fixed",r),r&&!n?i._reset():r||i.disable()}})}),e(window).on("resize.ace.sidebar_scroll",function(){e(".sidebar[data-sidebar-scroll=true]").each(function(){var t=e(this);"true"==t.attr("data-sidebar-hover")&&t.ace_sidebar_hover("reset");var a=e(this).ace_sidebar_scroll("ref"),i=s(this,"fixed");a.set("sidebar_fixed",i),a._reset()})}),e.fn.ace_sidebar_scroll||(e.fn.ace_sidebar_scroll=function(i,s){var n,r=this.each(function(){var t=e(this),r=t.data("ace_sidebar_scroll"),o="object"==typeof i&&i;r||t.data("ace_sidebar_scroll",r=new a(this,o)),"string"==typeof i&&"function"==typeof r[i]&&(n=r[i](s))});return n===t?r:n},e.fn.ace_sidebar_scroll.defaults={scroll_to_active:!0,include_shortcuts:!0,include_toggle:!1,smooth_scroll:150,scroll_outside:!1,scroll_style:"",lock_anyway:!1})}(window.jQuery),function(e,t){function a(t,a){function o(t){var a=t,i=e(a),s=null,n=!1;this.show=function(){null!=s&&clearTimeout(s),s=null,i.addClass("hover-show hover-shown"),n=!0;for(var e=0;e<r.length;e++)r[e].find(".hover-show").not(".hover-shown").each(function(){l(this).hide()})},this.hide=function(){n=!1,i.removeClass("hover-show hover-shown hover-flip"),null!=s&&clearTimeout(s),s=null;var e=i.find("> .submenu").get(0);e&&c(e,"hide")},this.hideDelay=function(e){null!=s&&clearTimeout(s),i.removeClass("hover-shown"),s=setTimeout(function(){n=!1,i.removeClass("hover-show hover-flip"),s=null;var t=i.find("> .submenu").get(0);t&&c(t,"hide"),"function"==typeof e&&e.call(this)},u.settings.sub_hover_delay)},this.is_visible=function(){return n}}function l(t){var a=e(t).data("subHide");return a||e(t).data("subHide",a=new o(t)),a}function c(t,a){var i=e(t).data("ace_scroll");return i?"string"==typeof a?(i[a](),!0):i:!1}function d(a){var i=e(this),n=e(a);a.style.top="",a.style.bottom="";var r=null;g.minimized&&(r=i.find(".menu-text").get(0))&&(r.style.marginTop="");var o=ace.helper.scrollTop(),l=0,d=o;y&&(l=t.offsetTop,d+=l+1);var u=i.offset();u.top=parseInt(u.top);var f,p=0;a.style.maxHeight="";var b=a.scrollHeight,f=i.height();r&&(p=f,u.top+=p);var w=parseInt(u.top+b),C=0,k=_.height(),$=parseInt(u.top-d-p),z=k,j=g.horizontal,A=!1;j&&this.parentNode==v&&(C=0,u.top+=i.height(),A=!0),!A&&(C=w-(k+o))>=0&&(C=$>C?C:$,0==C&&(C=20),$-C>10&&(C+=parseInt(Math.min(25,$-C))),u.top+(f-p)>w-C&&(C-=u.top+(f-p)-(w-C)),C>0&&(a.style.top=-C+"px",r&&(r.style.marginTop=-C+"px"))),0>C&&(C=0);var T=C>0&&C>f-20;if(T?i.addClass("pull_up"):i.removeClass("pull_up"),j)if(i.parent().parent().hasClass("hover-flip"))i.addClass("hover-flip");
else{var I=n.offset(),H=n.width(),S=_.width();I.left+H>S&&i.addClass("hover-flip")}var P=i.hasClass("hover")&&!g.mobile_view;if(!(P&&n.find("> li > .submenu").length>0)){var D=z-(u.top-o)+C,M=C-D;if(M>0&&f>M&&(D+=parseInt(Math.max(f,f-M))),D-=5,!(90>D)){var L=!1;if(s)n.addClass("sub-scroll").css("max-height",D+"px");else{if(L=c(a),0==L){n.ace_scroll({observeContent:!0,detached:!0,updatePos:!1,reset:!0,mouseWheelLock:!0,styleClass:h.settings.sub_scroll_style}),L=c(a);var E=L.get_track();E&&n.after(E)}L.update({size:D})}if(x=D,!s&&L){D>14&&b-D>4?(L.enable(),L.reset()):L.disable();var E=L.get_track();if(E){E.style.top=-(C-p-1)+"px";var u=n.position(),q=u.left;q+=m?2:n.outerWidth()-L.track_size(),E.style.left=parseInt(q)+"px",A&&(E.style.left=parseInt(q-2)+"px",E.style.top=parseInt(u.top)+(r?p-2:0)+"px")}}ace.vars.safari&&ace.helper.redraw(a)}}}var h=this,u=this,f=ace.helper.getAttrSettings(t,e.fn.ace_sidebar_hover.defaults);this.settings=e.extend({},e.fn.ace_sidebar_hover.defaults,a,f);var p=e(t),v=p.find(".nav-list").get(0);p.attr("data-sidebar-hover","true"),r.push(p);var g={},b=ace.vars.old_ie,m=!1;i&&(h.settings.sub_hover_delay=parseInt(Math.max(h.settings.sub_hover_delay,2500)));var _=e(window),w=e(".navbar").eq(0),y="fixed"==w.css("position");this.update_vars=function(){y="fixed"==w.css("position")},h.dirty=!1,this.reset=function(){0!=h.dirty&&(h.dirty=!1,p.find(".submenu").each(function(){var t=e(this),a=t.parent();t.css({top:"",bottom:"","max-height":""}),t.hasClass("ace-scroll")?t.ace_scroll("disable"):t.removeClass("sub-scroll"),n(this,"absolute")?t.addClass("can-scroll"):t.removeClass("can-scroll"),a.removeClass("pull_up").find(".menu-text:first").css("margin-top","")}),p.find(".hover-show").removeClass("hover-show hover-shown hover-flip"))},this.updateStyle=function(e){sub_scroll_style=e,p.find(".submenu.ace-scroll").ace_scroll("update",{styleClass:e})},this.changeDir=function(e){m="right"===e};var x=-1;s||p.on("hide.ace.submenu.sidebar_hover",".submenu",function(t){if(!(1>x)){t.stopPropagation();var a=e(this).closest(".ace-scroll.can-scroll");0!=a.length&&n(a[0],"absolute")&&a[0].scrollHeight-this.scrollHeight<x&&a.ace_scroll("disable")}}),s||p.on("shown.ace.submenu.sidebar_hover hidden.ace.submenu.sidebar_hover",".submenu",function(){if(!(1>x)){var t=e(this).closest(".ace-scroll.can-scroll");if(0!=t.length&&n(t[0],"absolute")){var a=t[0].scrollHeight;x>14&&a-x>4?t.ace_scroll("enable").ace_scroll("reset"):t.ace_scroll("disable")}}});var C=-1,k=i?"touchstart.sub_hover":"mouseenter.sub_hover",$=i?"touchend.sub_hover touchcancel.sub_hover":"mouseleave.sub_hover";p.on(k,".nav-list li, .sidebar-shortcuts",function(){if(g=p.ace_sidebar("vars"),!g.collapsible){var t=e(this),a=!1,s=t.hasClass("hover"),r=t.find("> .submenu").get(0);if(!(r||this.parentNode==v||s||(a=t.hasClass("sidebar-shortcuts"))))return void(r&&e(r).removeClass("can-scroll"));var o=r,c=!1;if(o||this.parentNode!=v||(o=t.find("> a > .menu-text").get(0)),!o&&a&&(o=t.find(".sidebar-shortcuts-large").get(0)),!(o&&(c=n(o,"absolute"))||s))return void(r&&e(r).removeClass("can-scroll"));var u=l(this);if(r)if(c){h.dirty=!0;var f=ace.helper.scrollTop();if(!u.is_visible()||!i&&f!=C||b)if(e(r).addClass("can-scroll"),b||i){var m=this;setTimeout(function(){d.call(m,r)},0)}else d.call(this,r);C=f}else e(r).removeClass("can-scroll");u.show()}}).on($,".nav-list li, .sidebar-shortcuts",function(){g=p.ace_sidebar("vars"),g.collapsible||e(this).hasClass("hover-show")&&l(this).hideDelay()})}if(!ace.vars.very_old_ie){var i=ace.vars.touch,s=ace.vars.old_ie||i,n="getComputedStyle"in window?function(e,t){return e.offsetHeight,window.getComputedStyle(e).position==t}:function(t,a){return t.offsetHeight,e(t).css("position")==a};e(window).on("resize.sidebar.ace_hover",function(){e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("update_vars").ace_sidebar_hover("reset")}),e(document).on("settings.ace.ace_hover",function(t,a){"sidebar_collapsed"==a?e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("reset"):"navbar_fixed"==a&&e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("update_vars")});var r=[];e.fn.ace_sidebar_hover=function(i,s){var n,r=this.each(function(){var t=e(this),r=t.data("ace_sidebar_hover"),o="object"==typeof i&&i;r||t.data("ace_sidebar_hover",r=new a(this,o)),"string"==typeof i&&"function"==typeof r[i]&&(n=r[i](s))});return n===t?r:n},e.fn.ace_sidebar_hover.defaults={sub_sub_hover_delay:750,sub_scroll_style:"no-track scroll-thin"}}}(window.jQuery),function(e,t){function a(t,a){var i=t.find(".widget-main").eq(0);e(window).off("resize.widget.scroll");var s=ace.vars.old_ie||ace.vars.touch;if(a){var n=i.data("ace_scroll");n&&i.data("save_scroll",{size:n.size,lock:n.lock,lock_anyway:n.lock_anyway});var r=t.height()-t.find(".widget-header").height()-10;r=parseInt(r),i.css("min-height",r),s?(n&&i.ace_scroll("disable"),i.css("max-height",r).addClass("overflow-scroll")):(n?i.ace_scroll("update",{size:r,mouseWheelLock:!0,lockAnyway:!0}):i.ace_scroll({size:r,mouseWheelLock:!0,lockAnyway:!0}),i.ace_scroll("enable").ace_scroll("reset")),e(window).on("resize.widget.scroll",function(){var e=t.height()-t.find(".widget-header").height()-10;e=parseInt(e),i.css("min-height",e),s?i.css("max-height",e).addClass("overflow-scroll"):i.ace_scroll("update",{size:e}).ace_scroll("reset")})}else{i.css("min-height","");var o=i.data("save_scroll");o&&i.ace_scroll("update",{size:o.size,mouseWheelLock:o.lock,lockAnyway:o.lock_anyway}).ace_scroll("enable").ace_scroll("reset"),s?i.css("max-height","").removeClass("overflow-scroll"):o||i.ace_scroll("disable")}}var i=function(t){this.$box=e(t);this.reload=function(){var e=this.$box,t=!1;"static"==e.css("position")&&(t=!0,e.addClass("position-relative")),e.append('<div class="widget-box-overlay"><i class="'+ace.vars.icon+'loading-icon fa fa-spinner fa-spin fa-2x white"></i></div>'),e.one("reloaded.ace.widget",function(){e.find(".widget-box-overlay").remove(),t&&e.removeClass("position-relative")})},this.close=function(){var e=this.$box,t=300;e.fadeOut(t,function(){e.trigger("closed.ace.widget"),e.remove()})},this.toggle=function(e,t){var a=this.$box,i=a.find(".widget-body").eq(0),s=null,n="undefined"!=typeof e?e:a.hasClass("collapsed")?"show":"hide",r="show"==n?"shown":"hidden";if("undefined"==typeof t&&(t=a.find("> .widget-header a[data-action=collapse]").eq(0),0==t.length&&(t=null)),t){s=t.find(ace.vars[".icon"]).eq(0);var o,l=null,c=null;(l=s.attr("data-icon-show"))?c=s.attr("data-icon-hide"):(o=s.attr("class").match(/fa\-(.*)\-(up|down)/))&&(l="fa-"+o[1]+"-down",c="fa-"+o[1]+"-up")}var d=250,h=200;"show"==n?(s&&s.removeClass(l).addClass(c),i.hide(),a.removeClass("collapsed"),i.slideDown(d,function(){a.trigger(r+".ace.widget")})):(s&&s.removeClass(c).addClass(l),i.slideUp(h,function(){a.addClass("collapsed"),a.trigger(r+".ace.widget")}))},this.hide=function(){this.toggle("hide")},this.show=function(){this.toggle("show")},this.fullscreen=function(){var e=this.$box.find("> .widget-header a[data-action=fullscreen]").find(ace.vars[".icon"]).eq(0),t=null,i=null;(t=e.attr("data-icon1"))?i=e.attr("data-icon2"):(t="fa-expand",i="fa-compress"),this.$box.hasClass("fullscreen")?(e.addClass(t).removeClass(i),this.$box.removeClass("fullscreen"),a(this.$box,!1)):(e.removeClass(t).addClass(i),this.$box.addClass("fullscreen"),a(this.$box,!0)),this.$box.trigger("fullscreened.ace.widget")}};e.fn.widget_box=function(a,s){var n,r=this.each(function(){var t=e(this),r=t.data("widget_box"),o="object"==typeof a&&a;r||t.data("widget_box",r=new i(this,o)),"string"==typeof a&&(n=r[a](s))});return n===t?r:n},e(document).on("click.ace.widget",".widget-header a[data-action]",function(t){t.preventDefault();var a=e(this),s=a.closest(".widget-box");if(0!=s.length&&!s.hasClass("ui-sortable-helper")){var n=s.data("widget_box");n||s.data("widget_box",n=new i(s.get(0)));var r=a.data("action");if("collapse"==r){var o,l=s.hasClass("collapsed")?"show":"hide";if(s.trigger(o=e.Event(l+".ace.widget")),o.isDefaultPrevented())return;n.toggle(l,a)}else if("close"==r){var o;if(s.trigger(o=e.Event("close.ace.widget")),o.isDefaultPrevented())return;n.close()}else if("reload"==r){a.blur();var o;if(s.trigger(o=e.Event("reload.ace.widget")),o.isDefaultPrevented())return;n.reload()}else if("fullscreen"==r){var o;if(s.trigger(o=e.Event("fullscreen.ace.widget")),o.isDefaultPrevented())return;n.fullscreen()}else"settings"==r&&s.trigger("setting.ace.widget")}})}(window.jQuery),function(e){e("#ace-settings-btn").on(ace.click_event,function(t){t.preventDefault(),e(this).toggleClass("open"),e("#ace-settings-box").toggleClass("open")}),e("#ace-settings-navbar").on("click",function(){ace.settings.navbar_fixed(null,this.checked)}).each(function(){this.checked=ace.settings.is("navbar","fixed")}),e("#ace-settings-sidebar").on("click",function(){ace.settings.sidebar_fixed(null,this.checked)}).each(function(){this.checked=ace.settings.is("sidebar","fixed")}),e("#ace-settings-breadcrumbs").on("click",function(){ace.settings.breadcrumbs_fixed(null,this.checked)}).each(function(){this.checked=ace.settings.is("breadcrumbs","fixed")}),e("#ace-settings-add-container").on("click",function(){ace.settings.main_container_fixed(null,this.checked)}).each(function(){this.checked=ace.settings.is("main-container","fixed")}),e("#ace-settings-compact").on("click",function(){if(this.checked){e("#sidebar").addClass("compact");var t=e("#ace-settings-hover");t.length>0&&t.removeAttr("checked").trigger("click")}else e("#sidebar").removeClass("compact"),e("#sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("reset");ace.vars.old_ie&&ace.helper.redraw(e("#sidebar")[0],!0)}),e("#ace-settings-highlight").on("click",function(){this.checked?e("#sidebar .nav-list > li").addClass("highlight"):e("#sidebar .nav-list > li").removeClass("highlight"),ace.vars.old_ie&&ace.helper.redraw(e("#sidebar")[0])}),e("#ace-settings-hover").on("click",function(){if(!e("#sidebar").hasClass("h-sidebar")){if(this.checked)e("#sidebar li").addClass("hover").filter(".open").removeClass("open").find("> .submenu").css("display","none");else{e("#sidebar li.hover").removeClass("hover");var t=e("#ace-settings-compact");t.length>0&&t.get(0).checked&&t.trigger("click")}e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("reset"),e(".sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("reset"),ace.vars.old_ie&&ace.helper.redraw(e("#sidebar")[0])}})}(jQuery),function(e){try{e("#skin-colorpicker").ace_colorpicker({auto_pos:!1})}catch(t){}e("#skin-colorpicker").on("change",function(){function t(t){var a=e(document.body);a.removeClass("no-skin skin-1 skin-2 skin-3"),a.addClass(t),ace.data.set("skin",t);var i=["red","blue","green",""];e(".ace-nav > li.grey").removeClass("dark"),e(".ace-nav > li").removeClass("no-border margin-1"),e(".ace-nav > li:not(:last-child)").removeClass("light-pink").find("> a > "+ace.vars[".icon"]).removeClass("pink").end().eq(0).find(".badge").removeClass("badge-warning"),e(".sidebar-shortcuts .btn").removeClass("btn-pink btn-white").find(ace.vars[".icon"]).removeClass("white"),e(".ace-nav > li.grey").removeClass("red").find(".badge").removeClass("badge-yellow"),e(".sidebar-shortcuts .btn").removeClass("btn-primary btn-white");var s=0;e(".sidebar-shortcuts .btn").each(function(){e(this).find(ace.vars[".icon"]).removeClass(i[s++])});var n=["btn-success","btn-info","btn-warning","btn-danger"];if("no-skin"==t){var s=0;e(".sidebar-shortcuts .btn").each(function(){e(this).attr("class","btn "+n[s++%4])}),e(".sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("updateStyle",""),e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("updateStyle","no-track scroll-thin")}else if("skin-1"==t){e(".ace-nav > li.grey").addClass("dark");var s=0;e(".sidebar-shortcuts").find(".btn").each(function(){e(this).attr("class","btn "+n[s++%4])}),e(".sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("updateStyle","scroll-white no-track"),e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("updateStyle","no-track scroll-thin scroll-white")}else if("skin-2"==t)e(".ace-nav > li").addClass("no-border margin-1"),e(".ace-nav > li:not(:last-child)").addClass("light-pink").find("> a > "+ace.vars[".icon"]).addClass("pink").end().eq(0).find(".badge").addClass("badge-warning"),e(".sidebar-shortcuts .btn").attr("class","btn btn-white btn-pink").find(ace.vars[".icon"]).addClass("white"),e(".sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("updateStyle","scroll-white no-track"),e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("updateStyle","no-track scroll-thin scroll-white");else if("skin-3"==t){a.addClass("no-skin"),e(".ace-nav > li.grey").addClass("red").find(".badge").addClass("badge-yellow");var s=0;e(".sidebar-shortcuts .btn").each(function(){e(this).attr("class","btn btn-primary btn-white"),e(this).find(ace.vars[".icon"]).addClass(i[s++])}),e(".sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("updateStyle","scroll-dark no-track"),e(".sidebar[data-sidebar-hover=true]").ace_sidebar_hover("updateStyle","no-track scroll-thin")}e(".sidebar[data-sidebar-scroll=true]").ace_sidebar_scroll("reset"),ace.vars.old_ie&&ace.helper.redraw(document.body,!0)}var a=e(this).find("option:selected").data("skin");if(0==e("#ace-skins-stylesheet").length){var i=e("head").find("link.ace-main-stylesheet");0==i.length&&(i=e("head").find('link[href*="/ace.min.css"],link[href*="/ace-part2.min.css"]'),0==i.length&&(i=e("head").find('link[href*="/ace.css"],link[href*="/ace-part2.css"]')));var s=i.first().attr("href").replace(/(\.min)?\.css$/i,"-skins$1.css");e.ajax({url:s}).done(function(){var e=jQuery("<link />",{type:"text/css",rel:"stylesheet",id:"ace-skins-stylesheet"});i.length>0?e.insertAfter(i.last()):e.appendTo("head"),e.attr("href",s),t(a),window.Pace&&Pace.running&&Pace.stop()})}else t(a)})}(jQuery);